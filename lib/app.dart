import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/features/no_internet/presentation/no_internet.wrapper.dart';
import 'package:hrms_tst/theme/theme.dart';
import 'router.dart';

class App extends ConsumerWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

    final router = ref.watch(routerProvider);
    final theme = ref.watch(themeProvider);
    return MaterialApp.router(
      debugShowCheckedModeBanner: false,
      title: 'TST HRMS',
      themeMode: ThemeMode.light,
      routerConfig: router,
      localizationsDelegates: [FlutterQuillLocalizations.delegate],
      builder: (context, child) {
        return NoInternetWrapper(child: child!);
      },
      theme: theme.make(
        ColorScheme.fromSeed(
          brightness: Brightness.light,
          seedColor: const Color(0xFF35B729),
          primary: const Color(0xFF35B729),
          onPrimary: Colors.white,
          primaryContainer: const Color(0xFF35B729),
          onPrimaryContainer: Colors.white,
          secondary: const Color(0xFF28373D),
          onSecondary: Colors.white,
          secondaryContainer: const Color(0xFFBDCDD4),
          onSecondaryContainer: Colors.white,
          tertiary: const Color(0xFF648897),
          onTertiary: Colors.white,
          surface: Colors.white,
          onSurface: Colors.black,
          outline: Color(0xFFE9EEF1),
        ),
      ),
      darkTheme: theme.make(
        ColorScheme.fromSeed(
          dynamicSchemeVariant: DynamicSchemeVariant.rainbow,
          seedColor: const Color(0xFF35B729),
          // tertiary: const Color(0xFF569D91),
          surface: const Color(0xFF1E2A2F),
          outline: const Color(0xFF3C4A4F),
          brightness: Brightness.dark,
        ),
      ),
    );
  }
}
