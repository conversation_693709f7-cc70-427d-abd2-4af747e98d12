import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'theme.g.dart';

@riverpod
AppTheme theme(Ref ref) {
  return AppTheme();
}

class AppTheme {
  ThemeData make(ColorScheme colorScheme) => ThemeData(
    colorScheme: colorScheme,
    brightness: colorScheme.brightness,
    useMaterial3: true,
    visualDensity: VisualDensity.adaptivePlatformDensity,
    scaffoldBackgroundColor: colorScheme.outline,
    appBarTheme: AppBarTheme(backgroundColor: colorScheme.outline),
    inputDecorationTheme: InputDecorationTheme(
      border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
    ),

    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        shape: ContinuousRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 24),
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: colorScheme.primary,
        side: BorderSide(color: colorScheme.primary),
        padding: EdgeInsets.symmetric(vertical: 14, horizontal: 24),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
          side: BorderSide(color: colorScheme.primary),
        ),
      ),
    ),
    dialogTheme: DialogThemeData(
      surfaceTintColor: colorScheme.surface,
      backgroundColor: colorScheme.surface,
    ),

    datePickerTheme: DatePickerThemeData(backgroundColor: colorScheme.surface),
    timePickerTheme: TimePickerThemeData(
      backgroundColor: colorScheme.surface,
      dialBackgroundColor: colorScheme.outline,
      dayPeriodColor: colorScheme.primary,
      dayPeriodTextColor: WidgetStateColor.fromMap({
        WidgetState.selected: colorScheme.onPrimary,
        WidgetState.any: colorScheme.onSurface,
      }),
      hourMinuteColor: WidgetStateColor.fromMap({
        WidgetState.selected: colorScheme.primary,
        WidgetState.any: colorScheme.outline,
      }),
    ),

    tooltipTheme: TooltipThemeData(
      textStyle: GoogleFonts.inter(color: colorScheme.onSurface),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: colorScheme.secondary.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
    ),

    fontFamily: GoogleFonts.inter().fontFamily,
  );
}
