/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/tst_hrms_launcher.png
  AssetGenImage get tstHrmsLauncher =>
      const AssetGenImage('assets/images/tst_hrms_launcher.png');

  /// List of all assets
  List<AssetGenImage> get values => [tstHrmsLauncher];
}

class $AssetsMlModelGen {
  const $AssetsMlModelGen();

  /// File path: assets/ml-model/mobilefacenet.tflite
  String get mobilefacenet => 'assets/ml-model/mobilefacenet.tflite';

  /// List of all assets
  List<String> get values => [mobilefacenet];
}

class $AssetsSvgsGen {
  const $AssetsSvgsGen();

  /// File path: assets/svgs/anniversary.svg
  String get anniversary => 'assets/svgs/anniversary.svg';

  /// File path: assets/svgs/attached_note_icon.svg
  String get attachedNoteIcon => 'assets/svgs/attached_note_icon.svg';

  /// File path: assets/svgs/attached_photos_icon.svg
  String get attachedPhotosIcon => 'assets/svgs/attached_photos_icon.svg';

  /// File path: assets/svgs/attachment_link.svg
  String get attachmentLink => 'assets/svgs/attachment_link.svg';

  /// File path: assets/svgs/back_arrow_icon.svg
  String get backArrowIcon => 'assets/svgs/back_arrow_icon.svg';

  /// File path: assets/svgs/bottom_home.svg
  String get bottomHome => 'assets/svgs/bottom_home.svg';

  /// File path: assets/svgs/bottom_me.svg
  String get bottomMe => 'assets/svgs/bottom_me.svg';

  /// File path: assets/svgs/bottom_request.svg
  String get bottomRequest => 'assets/svgs/bottom_request.svg';

  /// File path: assets/svgs/bottom_setting.svg
  String get bottomSetting => 'assets/svgs/bottom_setting.svg';

  /// File path: assets/svgs/bottom_teammates.svg
  String get bottomTeammates => 'assets/svgs/bottom_teammates.svg';

  /// File path: assets/svgs/cake.svg
  String get cake => 'assets/svgs/cake.svg';

  /// File path: assets/svgs/chevron_right.svg
  String get chevronRight => 'assets/svgs/chevron_right.svg';

  /// File path: assets/svgs/clock_icon.svg
  String get clockIcon => 'assets/svgs/clock_icon.svg';

  /// File path: assets/svgs/close_icon.svg
  String get closeIcon => 'assets/svgs/close_icon.svg';

  /// File path: assets/svgs/document_verified.svg
  String get documentVerified => 'assets/svgs/document_verified.svg';

  /// File path: assets/svgs/dropdown_arrow_icon.svg
  String get dropdownArrowIcon => 'assets/svgs/dropdown_arrow_icon.svg';

  /// File path: assets/svgs/email_icon.svg
  String get emailIcon => 'assets/svgs/email_icon.svg';

  /// File path: assets/svgs/filter_panel_icon.svg
  String get filterPanelIcon => 'assets/svgs/filter_panel_icon.svg';

  /// File path: assets/svgs/forward_arrow.svg
  String get forwardArrow => 'assets/svgs/forward_arrow.svg';

  /// File path: assets/svgs/google_logo.svg
  String get googleLogo => 'assets/svgs/google_logo.svg';

  /// File path: assets/svgs/location_icon.svg
  String get locationIcon => 'assets/svgs/location_icon.svg';

  /// File path: assets/svgs/new_joinee.svg
  String get newJoinee => 'assets/svgs/new_joinee.svg';

  /// File path: assets/svgs/no_data.svg
  String get noData => 'assets/svgs/no_data.svg';

  /// File path: assets/svgs/notifications_icon.svg
  String get notificationsIcon => 'assets/svgs/notifications_icon.svg';

  /// File path: assets/svgs/phone_icon.svg
  String get phoneIcon => 'assets/svgs/phone_icon.svg';

  /// File path: assets/svgs/record_verification_pending.svg
  String get recordVerificationPending =>
      'assets/svgs/record_verification_pending.svg';

  /// File path: assets/svgs/record_verified.svg
  String get recordVerified => 'assets/svgs/record_verified.svg';

  /// File path: assets/svgs/search_icon.svg
  String get searchIcon => 'assets/svgs/search_icon.svg';

  /// File path: assets/svgs/team_progress_books.svg
  String get teamProgressBooks => 'assets/svgs/team_progress_books.svg';

  /// File path: assets/svgs/team_progress_courses.svg
  String get teamProgressCourses => 'assets/svgs/team_progress_courses.svg';

  /// File path: assets/svgs/team_progress_events.svg
  String get teamProgressEvents => 'assets/svgs/team_progress_events.svg';

  /// File path: assets/svgs/team_progress_gds.svg
  String get teamProgressGds => 'assets/svgs/team_progress_gds.svg';

  /// File path: assets/svgs/team_progress_sessions.svg
  String get teamProgressSessions => 'assets/svgs/team_progress_sessions.svg';

  /// File path: assets/svgs/teammate_filter_icon.svg
  String get teammateFilterIcon => 'assets/svgs/teammate_filter_icon.svg';

  /// File path: assets/svgs/tst_technology_full_name.svg
  String get tstTechnologyFullName =>
      'assets/svgs/tst_technology_full_name.svg';

  /// List of all assets
  List<String> get values => [
    anniversary,
    attachedNoteIcon,
    attachedPhotosIcon,
    attachmentLink,
    backArrowIcon,
    bottomHome,
    bottomMe,
    bottomRequest,
    bottomSetting,
    bottomTeammates,
    cake,
    chevronRight,
    clockIcon,
    closeIcon,
    documentVerified,
    dropdownArrowIcon,
    emailIcon,
    filterPanelIcon,
    forwardArrow,
    googleLogo,
    locationIcon,
    newJoinee,
    noData,
    notificationsIcon,
    phoneIcon,
    recordVerificationPending,
    recordVerified,
    searchIcon,
    teamProgressBooks,
    teamProgressCourses,
    teamProgressEvents,
    teamProgressGds,
    teamProgressSessions,
    teammateFilterIcon,
    tstTechnologyFullName,
  ];
}

class Assets {
  const Assets._();

  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsMlModelGen mlModel = $AssetsMlModelGen();
  static const $AssetsSvgsGen svgs = $AssetsSvgsGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
