import 'package:hrms_tst/core/services/auth/protocol/auth.protocol.dart';
import 'package:hrms_tst/core/services/notifications/push_notifications/push_notifications.service.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:hrms_tst/features/notifications/domain/notifications.controller.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../models/user.model.dart';

part 'auth.provider.g.dart';

@riverpod
class Auth extends _$Auth {
  final AuthProtocol _auth = getIt<AuthProtocol>();

  @override
  FutureOr<UserModel?> build() async {
    try {
      final fcmToken = await getIt<PushNotificationsService>().token;
      if (fcmToken != null) {
        await _auth.setFCMToken(fcmToken);
      }
    } catch (e) {
      // Unable to set FCM token, continue without it
    }

    final result = (await _auth.getMe()).ignoreFailure();

    ref.read(notificationCountProvider.notifier).set(result?.$2 ?? 0);

    return result?.$1;
  }

  Future<void> signInWithGoogle() async {
    final result = await _auth.signInWithGoogle();
    await result.fold(
      onSuccess: (data) async {
        ref.invalidateSelf();
        await ref.read(authProvider.future);
      },
      onFailure: (message) {
        state = AsyncValue.error(message, StackTrace.current);
      },
    );
  }
}
