import 'package:freezed_annotation/freezed_annotation.dart';

part 'teammate_filter.freezed.dart';

@freezed
abstract class TeammateFilter<T> with _$TeammateFilter<T> {
  const factory TeammateFilter({
    required final String label,
    required final String key,
    required final List<TeammateFilterOption> options,
    final T? selectedOption,
  }) = _TeammateFilter<T>;
}

@freezed
abstract class TeammateFilterOption<T> with _$TeammateFilterOption<T> {
  const factory TeammateFilterOption({
    required final T data,
    required final String label,
  }) = _TeammateFilterOption<T>;
}
