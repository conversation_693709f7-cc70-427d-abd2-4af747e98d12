// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user.model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserModel {

 int get id; String? get firstName; String? get middleName; String? get lastName; String? get displayName; String? get mobile; String? get countryCode; String? get personalMobile; DateTime? get dob; Gender? get gender; String? get personalEmail; String? get image; Role? get role;@JsonKey(name: 'employees', fromJson: employeeDetailsFromJson) EmployeeDetails? get details;
/// Create a copy of UserModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserModelCopyWith<UserModel> get copyWith => _$UserModelCopyWithImpl<UserModel>(this as UserModel, _$identity);

  /// Serializes this UserModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserModel&&(identical(other.id, id) || other.id == id)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.middleName, middleName) || other.middleName == middleName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.mobile, mobile) || other.mobile == mobile)&&(identical(other.countryCode, countryCode) || other.countryCode == countryCode)&&(identical(other.personalMobile, personalMobile) || other.personalMobile == personalMobile)&&(identical(other.dob, dob) || other.dob == dob)&&(identical(other.gender, gender) || other.gender == gender)&&(identical(other.personalEmail, personalEmail) || other.personalEmail == personalEmail)&&(identical(other.image, image) || other.image == image)&&(identical(other.role, role) || other.role == role)&&(identical(other.details, details) || other.details == details));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,firstName,middleName,lastName,displayName,mobile,countryCode,personalMobile,dob,gender,personalEmail,image,role,details);

@override
String toString() {
  return 'UserModel(id: $id, firstName: $firstName, middleName: $middleName, lastName: $lastName, displayName: $displayName, mobile: $mobile, countryCode: $countryCode, personalMobile: $personalMobile, dob: $dob, gender: $gender, personalEmail: $personalEmail, image: $image, role: $role, details: $details)';
}


}

/// @nodoc
abstract mixin class $UserModelCopyWith<$Res>  {
  factory $UserModelCopyWith(UserModel value, $Res Function(UserModel) _then) = _$UserModelCopyWithImpl;
@useResult
$Res call({
 int id, String? firstName, String? middleName, String? lastName, String? displayName, String? mobile, String? countryCode, String? personalMobile, DateTime? dob, Gender? gender, String? personalEmail, String? image, Role? role,@JsonKey(name: 'employees', fromJson: employeeDetailsFromJson) EmployeeDetails? details
});


$EmployeeDetailsCopyWith<$Res>? get details;

}
/// @nodoc
class _$UserModelCopyWithImpl<$Res>
    implements $UserModelCopyWith<$Res> {
  _$UserModelCopyWithImpl(this._self, this._then);

  final UserModel _self;
  final $Res Function(UserModel) _then;

/// Create a copy of UserModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? firstName = freezed,Object? middleName = freezed,Object? lastName = freezed,Object? displayName = freezed,Object? mobile = freezed,Object? countryCode = freezed,Object? personalMobile = freezed,Object? dob = freezed,Object? gender = freezed,Object? personalEmail = freezed,Object? image = freezed,Object? role = freezed,Object? details = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,firstName: freezed == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String?,middleName: freezed == middleName ? _self.middleName : middleName // ignore: cast_nullable_to_non_nullable
as String?,lastName: freezed == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String?,displayName: freezed == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String?,mobile: freezed == mobile ? _self.mobile : mobile // ignore: cast_nullable_to_non_nullable
as String?,countryCode: freezed == countryCode ? _self.countryCode : countryCode // ignore: cast_nullable_to_non_nullable
as String?,personalMobile: freezed == personalMobile ? _self.personalMobile : personalMobile // ignore: cast_nullable_to_non_nullable
as String?,dob: freezed == dob ? _self.dob : dob // ignore: cast_nullable_to_non_nullable
as DateTime?,gender: freezed == gender ? _self.gender : gender // ignore: cast_nullable_to_non_nullable
as Gender?,personalEmail: freezed == personalEmail ? _self.personalEmail : personalEmail // ignore: cast_nullable_to_non_nullable
as String?,image: freezed == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String?,role: freezed == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as Role?,details: freezed == details ? _self.details : details // ignore: cast_nullable_to_non_nullable
as EmployeeDetails?,
  ));
}
/// Create a copy of UserModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EmployeeDetailsCopyWith<$Res>? get details {
    if (_self.details == null) {
    return null;
  }

  return $EmployeeDetailsCopyWith<$Res>(_self.details!, (value) {
    return _then(_self.copyWith(details: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _UserModel implements UserModel {
  const _UserModel({required this.id, required this.firstName, required this.middleName, required this.lastName, required this.displayName, required this.mobile, required this.countryCode, required this.personalMobile, required this.dob, required this.gender, required this.personalEmail, required this.image, required this.role, @JsonKey(name: 'employees', fromJson: employeeDetailsFromJson) this.details});
  factory _UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);

@override final  int id;
@override final  String? firstName;
@override final  String? middleName;
@override final  String? lastName;
@override final  String? displayName;
@override final  String? mobile;
@override final  String? countryCode;
@override final  String? personalMobile;
@override final  DateTime? dob;
@override final  Gender? gender;
@override final  String? personalEmail;
@override final  String? image;
@override final  Role? role;
@override@JsonKey(name: 'employees', fromJson: employeeDetailsFromJson) final  EmployeeDetails? details;

/// Create a copy of UserModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserModelCopyWith<_UserModel> get copyWith => __$UserModelCopyWithImpl<_UserModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserModel&&(identical(other.id, id) || other.id == id)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.middleName, middleName) || other.middleName == middleName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.mobile, mobile) || other.mobile == mobile)&&(identical(other.countryCode, countryCode) || other.countryCode == countryCode)&&(identical(other.personalMobile, personalMobile) || other.personalMobile == personalMobile)&&(identical(other.dob, dob) || other.dob == dob)&&(identical(other.gender, gender) || other.gender == gender)&&(identical(other.personalEmail, personalEmail) || other.personalEmail == personalEmail)&&(identical(other.image, image) || other.image == image)&&(identical(other.role, role) || other.role == role)&&(identical(other.details, details) || other.details == details));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,firstName,middleName,lastName,displayName,mobile,countryCode,personalMobile,dob,gender,personalEmail,image,role,details);

@override
String toString() {
  return 'UserModel(id: $id, firstName: $firstName, middleName: $middleName, lastName: $lastName, displayName: $displayName, mobile: $mobile, countryCode: $countryCode, personalMobile: $personalMobile, dob: $dob, gender: $gender, personalEmail: $personalEmail, image: $image, role: $role, details: $details)';
}


}

/// @nodoc
abstract mixin class _$UserModelCopyWith<$Res> implements $UserModelCopyWith<$Res> {
  factory _$UserModelCopyWith(_UserModel value, $Res Function(_UserModel) _then) = __$UserModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String? firstName, String? middleName, String? lastName, String? displayName, String? mobile, String? countryCode, String? personalMobile, DateTime? dob, Gender? gender, String? personalEmail, String? image, Role? role,@JsonKey(name: 'employees', fromJson: employeeDetailsFromJson) EmployeeDetails? details
});


@override $EmployeeDetailsCopyWith<$Res>? get details;

}
/// @nodoc
class __$UserModelCopyWithImpl<$Res>
    implements _$UserModelCopyWith<$Res> {
  __$UserModelCopyWithImpl(this._self, this._then);

  final _UserModel _self;
  final $Res Function(_UserModel) _then;

/// Create a copy of UserModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? firstName = freezed,Object? middleName = freezed,Object? lastName = freezed,Object? displayName = freezed,Object? mobile = freezed,Object? countryCode = freezed,Object? personalMobile = freezed,Object? dob = freezed,Object? gender = freezed,Object? personalEmail = freezed,Object? image = freezed,Object? role = freezed,Object? details = freezed,}) {
  return _then(_UserModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,firstName: freezed == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String?,middleName: freezed == middleName ? _self.middleName : middleName // ignore: cast_nullable_to_non_nullable
as String?,lastName: freezed == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String?,displayName: freezed == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String?,mobile: freezed == mobile ? _self.mobile : mobile // ignore: cast_nullable_to_non_nullable
as String?,countryCode: freezed == countryCode ? _self.countryCode : countryCode // ignore: cast_nullable_to_non_nullable
as String?,personalMobile: freezed == personalMobile ? _self.personalMobile : personalMobile // ignore: cast_nullable_to_non_nullable
as String?,dob: freezed == dob ? _self.dob : dob // ignore: cast_nullable_to_non_nullable
as DateTime?,gender: freezed == gender ? _self.gender : gender // ignore: cast_nullable_to_non_nullable
as Gender?,personalEmail: freezed == personalEmail ? _self.personalEmail : personalEmail // ignore: cast_nullable_to_non_nullable
as String?,image: freezed == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String?,role: freezed == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as Role?,details: freezed == details ? _self.details : details // ignore: cast_nullable_to_non_nullable
as EmployeeDetails?,
  ));
}

/// Create a copy of UserModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EmployeeDetailsCopyWith<$Res>? get details {
    if (_self.details == null) {
    return null;
  }

  return $EmployeeDetailsCopyWith<$Res>(_self.details!, (value) {
    return _then(_self.copyWith(details: value));
  });
}
}


/// @nodoc
mixin _$EmployeeDetails {

 int get id; int get empNo; String? get email; String? get about; String? get interests; String? get hobbies; String? get bloodGroup; String? get maritalStatus; bool get handicapped; String? get currentAddr; String? get permanentAddr; bool get isAddressSame; String? get city; String? get state; String? get country; String? get permanentCity; String? get permanentState; String? get permanentCountry; DateTime? get joiningDate; DateTime? get relievedDate; bool? get partTime; String? get workMode; double? get sickLeaves; DateTime? get createdAt; DateTime? get deletedAt; DateTime? get removedAt;@JsonKey(name: 'ManagerId') int? get managerId;@JsonKey(name: 'LeavePolicyId') int? get leavePolicyId;@JsonKey(name: 'BankDetailId') int? get bankDetailId;@JsonKey(name: 'UserId') int? get userId;@JsonKey(name: 'OrganizationId') int? get organizationId;@JsonKey(name: 'OfficeId') int? get officeId;@JsonKey(name: 'DesignationId') int? get designationId;@JsonKey(name: 'TimetableId') int? get timetableId;@JsonKey(name: 'WorkShiftId') int? get workShiftId;@JsonKey(name: 'ReferredByEmpId') int? get referredByEmpId; OfficeModel? get office; DesignationModel? get designation; List<DepartmentModel>? get departments; List<JobRoleModel>? get jobRoles; List<OfferLetterModel>? get offerLetters;
/// Create a copy of EmployeeDetails
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EmployeeDetailsCopyWith<EmployeeDetails> get copyWith => _$EmployeeDetailsCopyWithImpl<EmployeeDetails>(this as EmployeeDetails, _$identity);

  /// Serializes this EmployeeDetails to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EmployeeDetails&&(identical(other.id, id) || other.id == id)&&(identical(other.empNo, empNo) || other.empNo == empNo)&&(identical(other.email, email) || other.email == email)&&(identical(other.about, about) || other.about == about)&&(identical(other.interests, interests) || other.interests == interests)&&(identical(other.hobbies, hobbies) || other.hobbies == hobbies)&&(identical(other.bloodGroup, bloodGroup) || other.bloodGroup == bloodGroup)&&(identical(other.maritalStatus, maritalStatus) || other.maritalStatus == maritalStatus)&&(identical(other.handicapped, handicapped) || other.handicapped == handicapped)&&(identical(other.currentAddr, currentAddr) || other.currentAddr == currentAddr)&&(identical(other.permanentAddr, permanentAddr) || other.permanentAddr == permanentAddr)&&(identical(other.isAddressSame, isAddressSame) || other.isAddressSame == isAddressSame)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&(identical(other.country, country) || other.country == country)&&(identical(other.permanentCity, permanentCity) || other.permanentCity == permanentCity)&&(identical(other.permanentState, permanentState) || other.permanentState == permanentState)&&(identical(other.permanentCountry, permanentCountry) || other.permanentCountry == permanentCountry)&&(identical(other.joiningDate, joiningDate) || other.joiningDate == joiningDate)&&(identical(other.relievedDate, relievedDate) || other.relievedDate == relievedDate)&&(identical(other.partTime, partTime) || other.partTime == partTime)&&(identical(other.workMode, workMode) || other.workMode == workMode)&&(identical(other.sickLeaves, sickLeaves) || other.sickLeaves == sickLeaves)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt)&&(identical(other.removedAt, removedAt) || other.removedAt == removedAt)&&(identical(other.managerId, managerId) || other.managerId == managerId)&&(identical(other.leavePolicyId, leavePolicyId) || other.leavePolicyId == leavePolicyId)&&(identical(other.bankDetailId, bankDetailId) || other.bankDetailId == bankDetailId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.organizationId, organizationId) || other.organizationId == organizationId)&&(identical(other.officeId, officeId) || other.officeId == officeId)&&(identical(other.designationId, designationId) || other.designationId == designationId)&&(identical(other.timetableId, timetableId) || other.timetableId == timetableId)&&(identical(other.workShiftId, workShiftId) || other.workShiftId == workShiftId)&&(identical(other.referredByEmpId, referredByEmpId) || other.referredByEmpId == referredByEmpId)&&(identical(other.office, office) || other.office == office)&&(identical(other.designation, designation) || other.designation == designation)&&const DeepCollectionEquality().equals(other.departments, departments)&&const DeepCollectionEquality().equals(other.jobRoles, jobRoles)&&const DeepCollectionEquality().equals(other.offerLetters, offerLetters));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,empNo,email,about,interests,hobbies,bloodGroup,maritalStatus,handicapped,currentAddr,permanentAddr,isAddressSame,city,state,country,permanentCity,permanentState,permanentCountry,joiningDate,relievedDate,partTime,workMode,sickLeaves,createdAt,deletedAt,removedAt,managerId,leavePolicyId,bankDetailId,userId,organizationId,officeId,designationId,timetableId,workShiftId,referredByEmpId,office,designation,const DeepCollectionEquality().hash(departments),const DeepCollectionEquality().hash(jobRoles),const DeepCollectionEquality().hash(offerLetters)]);

@override
String toString() {
  return 'EmployeeDetails(id: $id, empNo: $empNo, email: $email, about: $about, interests: $interests, hobbies: $hobbies, bloodGroup: $bloodGroup, maritalStatus: $maritalStatus, handicapped: $handicapped, currentAddr: $currentAddr, permanentAddr: $permanentAddr, isAddressSame: $isAddressSame, city: $city, state: $state, country: $country, permanentCity: $permanentCity, permanentState: $permanentState, permanentCountry: $permanentCountry, joiningDate: $joiningDate, relievedDate: $relievedDate, partTime: $partTime, workMode: $workMode, sickLeaves: $sickLeaves, createdAt: $createdAt, deletedAt: $deletedAt, removedAt: $removedAt, managerId: $managerId, leavePolicyId: $leavePolicyId, bankDetailId: $bankDetailId, userId: $userId, organizationId: $organizationId, officeId: $officeId, designationId: $designationId, timetableId: $timetableId, workShiftId: $workShiftId, referredByEmpId: $referredByEmpId, office: $office, designation: $designation, departments: $departments, jobRoles: $jobRoles, offerLetters: $offerLetters)';
}


}

/// @nodoc
abstract mixin class $EmployeeDetailsCopyWith<$Res>  {
  factory $EmployeeDetailsCopyWith(EmployeeDetails value, $Res Function(EmployeeDetails) _then) = _$EmployeeDetailsCopyWithImpl;
@useResult
$Res call({
 int id, int empNo, String? email, String? about, String? interests, String? hobbies, String? bloodGroup, String? maritalStatus, bool handicapped, String? currentAddr, String? permanentAddr, bool isAddressSame, String? city, String? state, String? country, String? permanentCity, String? permanentState, String? permanentCountry, DateTime? joiningDate, DateTime? relievedDate, bool? partTime, String? workMode, double? sickLeaves, DateTime? createdAt, DateTime? deletedAt, DateTime? removedAt,@JsonKey(name: 'ManagerId') int? managerId,@JsonKey(name: 'LeavePolicyId') int? leavePolicyId,@JsonKey(name: 'BankDetailId') int? bankDetailId,@JsonKey(name: 'UserId') int? userId,@JsonKey(name: 'OrganizationId') int? organizationId,@JsonKey(name: 'OfficeId') int? officeId,@JsonKey(name: 'DesignationId') int? designationId,@JsonKey(name: 'TimetableId') int? timetableId,@JsonKey(name: 'WorkShiftId') int? workShiftId,@JsonKey(name: 'ReferredByEmpId') int? referredByEmpId, OfficeModel? office, DesignationModel? designation, List<DepartmentModel>? departments, List<JobRoleModel>? jobRoles, List<OfferLetterModel>? offerLetters
});


$OfficeModelCopyWith<$Res>? get office;$DesignationModelCopyWith<$Res>? get designation;

}
/// @nodoc
class _$EmployeeDetailsCopyWithImpl<$Res>
    implements $EmployeeDetailsCopyWith<$Res> {
  _$EmployeeDetailsCopyWithImpl(this._self, this._then);

  final EmployeeDetails _self;
  final $Res Function(EmployeeDetails) _then;

/// Create a copy of EmployeeDetails
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? empNo = null,Object? email = freezed,Object? about = freezed,Object? interests = freezed,Object? hobbies = freezed,Object? bloodGroup = freezed,Object? maritalStatus = freezed,Object? handicapped = null,Object? currentAddr = freezed,Object? permanentAddr = freezed,Object? isAddressSame = null,Object? city = freezed,Object? state = freezed,Object? country = freezed,Object? permanentCity = freezed,Object? permanentState = freezed,Object? permanentCountry = freezed,Object? joiningDate = freezed,Object? relievedDate = freezed,Object? partTime = freezed,Object? workMode = freezed,Object? sickLeaves = freezed,Object? createdAt = freezed,Object? deletedAt = freezed,Object? removedAt = freezed,Object? managerId = freezed,Object? leavePolicyId = freezed,Object? bankDetailId = freezed,Object? userId = freezed,Object? organizationId = freezed,Object? officeId = freezed,Object? designationId = freezed,Object? timetableId = freezed,Object? workShiftId = freezed,Object? referredByEmpId = freezed,Object? office = freezed,Object? designation = freezed,Object? departments = freezed,Object? jobRoles = freezed,Object? offerLetters = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,empNo: null == empNo ? _self.empNo : empNo // ignore: cast_nullable_to_non_nullable
as int,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,about: freezed == about ? _self.about : about // ignore: cast_nullable_to_non_nullable
as String?,interests: freezed == interests ? _self.interests : interests // ignore: cast_nullable_to_non_nullable
as String?,hobbies: freezed == hobbies ? _self.hobbies : hobbies // ignore: cast_nullable_to_non_nullable
as String?,bloodGroup: freezed == bloodGroup ? _self.bloodGroup : bloodGroup // ignore: cast_nullable_to_non_nullable
as String?,maritalStatus: freezed == maritalStatus ? _self.maritalStatus : maritalStatus // ignore: cast_nullable_to_non_nullable
as String?,handicapped: null == handicapped ? _self.handicapped : handicapped // ignore: cast_nullable_to_non_nullable
as bool,currentAddr: freezed == currentAddr ? _self.currentAddr : currentAddr // ignore: cast_nullable_to_non_nullable
as String?,permanentAddr: freezed == permanentAddr ? _self.permanentAddr : permanentAddr // ignore: cast_nullable_to_non_nullable
as String?,isAddressSame: null == isAddressSame ? _self.isAddressSame : isAddressSame // ignore: cast_nullable_to_non_nullable
as bool,city: freezed == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String?,state: freezed == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String?,country: freezed == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String?,permanentCity: freezed == permanentCity ? _self.permanentCity : permanentCity // ignore: cast_nullable_to_non_nullable
as String?,permanentState: freezed == permanentState ? _self.permanentState : permanentState // ignore: cast_nullable_to_non_nullable
as String?,permanentCountry: freezed == permanentCountry ? _self.permanentCountry : permanentCountry // ignore: cast_nullable_to_non_nullable
as String?,joiningDate: freezed == joiningDate ? _self.joiningDate : joiningDate // ignore: cast_nullable_to_non_nullable
as DateTime?,relievedDate: freezed == relievedDate ? _self.relievedDate : relievedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,partTime: freezed == partTime ? _self.partTime : partTime // ignore: cast_nullable_to_non_nullable
as bool?,workMode: freezed == workMode ? _self.workMode : workMode // ignore: cast_nullable_to_non_nullable
as String?,sickLeaves: freezed == sickLeaves ? _self.sickLeaves : sickLeaves // ignore: cast_nullable_to_non_nullable
as double?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,removedAt: freezed == removedAt ? _self.removedAt : removedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,managerId: freezed == managerId ? _self.managerId : managerId // ignore: cast_nullable_to_non_nullable
as int?,leavePolicyId: freezed == leavePolicyId ? _self.leavePolicyId : leavePolicyId // ignore: cast_nullable_to_non_nullable
as int?,bankDetailId: freezed == bankDetailId ? _self.bankDetailId : bankDetailId // ignore: cast_nullable_to_non_nullable
as int?,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as int?,organizationId: freezed == organizationId ? _self.organizationId : organizationId // ignore: cast_nullable_to_non_nullable
as int?,officeId: freezed == officeId ? _self.officeId : officeId // ignore: cast_nullable_to_non_nullable
as int?,designationId: freezed == designationId ? _self.designationId : designationId // ignore: cast_nullable_to_non_nullable
as int?,timetableId: freezed == timetableId ? _self.timetableId : timetableId // ignore: cast_nullable_to_non_nullable
as int?,workShiftId: freezed == workShiftId ? _self.workShiftId : workShiftId // ignore: cast_nullable_to_non_nullable
as int?,referredByEmpId: freezed == referredByEmpId ? _self.referredByEmpId : referredByEmpId // ignore: cast_nullable_to_non_nullable
as int?,office: freezed == office ? _self.office : office // ignore: cast_nullable_to_non_nullable
as OfficeModel?,designation: freezed == designation ? _self.designation : designation // ignore: cast_nullable_to_non_nullable
as DesignationModel?,departments: freezed == departments ? _self.departments : departments // ignore: cast_nullable_to_non_nullable
as List<DepartmentModel>?,jobRoles: freezed == jobRoles ? _self.jobRoles : jobRoles // ignore: cast_nullable_to_non_nullable
as List<JobRoleModel>?,offerLetters: freezed == offerLetters ? _self.offerLetters : offerLetters // ignore: cast_nullable_to_non_nullable
as List<OfferLetterModel>?,
  ));
}
/// Create a copy of EmployeeDetails
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$OfficeModelCopyWith<$Res>? get office {
    if (_self.office == null) {
    return null;
  }

  return $OfficeModelCopyWith<$Res>(_self.office!, (value) {
    return _then(_self.copyWith(office: value));
  });
}/// Create a copy of EmployeeDetails
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DesignationModelCopyWith<$Res>? get designation {
    if (_self.designation == null) {
    return null;
  }

  return $DesignationModelCopyWith<$Res>(_self.designation!, (value) {
    return _then(_self.copyWith(designation: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _EmployeeDetails implements EmployeeDetails {
  const _EmployeeDetails({required this.id, required this.empNo, this.email, this.about, this.interests, this.hobbies, this.bloodGroup, this.maritalStatus, required this.handicapped, this.currentAddr, this.permanentAddr, required this.isAddressSame, this.city, this.state, this.country, this.permanentCity, this.permanentState, this.permanentCountry, this.joiningDate, this.relievedDate, this.partTime, this.workMode, this.sickLeaves, this.createdAt, this.deletedAt, this.removedAt, @JsonKey(name: 'ManagerId') this.managerId, @JsonKey(name: 'LeavePolicyId') this.leavePolicyId, @JsonKey(name: 'BankDetailId') this.bankDetailId, @JsonKey(name: 'UserId') this.userId, @JsonKey(name: 'OrganizationId') this.organizationId, @JsonKey(name: 'OfficeId') this.officeId, @JsonKey(name: 'DesignationId') this.designationId, @JsonKey(name: 'TimetableId') this.timetableId, @JsonKey(name: 'WorkShiftId') this.workShiftId, @JsonKey(name: 'ReferredByEmpId') this.referredByEmpId, this.office, this.designation, final  List<DepartmentModel>? departments, final  List<JobRoleModel>? jobRoles, final  List<OfferLetterModel>? offerLetters}): _departments = departments,_jobRoles = jobRoles,_offerLetters = offerLetters;
  factory _EmployeeDetails.fromJson(Map<String, dynamic> json) => _$EmployeeDetailsFromJson(json);

@override final  int id;
@override final  int empNo;
@override final  String? email;
@override final  String? about;
@override final  String? interests;
@override final  String? hobbies;
@override final  String? bloodGroup;
@override final  String? maritalStatus;
@override final  bool handicapped;
@override final  String? currentAddr;
@override final  String? permanentAddr;
@override final  bool isAddressSame;
@override final  String? city;
@override final  String? state;
@override final  String? country;
@override final  String? permanentCity;
@override final  String? permanentState;
@override final  String? permanentCountry;
@override final  DateTime? joiningDate;
@override final  DateTime? relievedDate;
@override final  bool? partTime;
@override final  String? workMode;
@override final  double? sickLeaves;
@override final  DateTime? createdAt;
@override final  DateTime? deletedAt;
@override final  DateTime? removedAt;
@override@JsonKey(name: 'ManagerId') final  int? managerId;
@override@JsonKey(name: 'LeavePolicyId') final  int? leavePolicyId;
@override@JsonKey(name: 'BankDetailId') final  int? bankDetailId;
@override@JsonKey(name: 'UserId') final  int? userId;
@override@JsonKey(name: 'OrganizationId') final  int? organizationId;
@override@JsonKey(name: 'OfficeId') final  int? officeId;
@override@JsonKey(name: 'DesignationId') final  int? designationId;
@override@JsonKey(name: 'TimetableId') final  int? timetableId;
@override@JsonKey(name: 'WorkShiftId') final  int? workShiftId;
@override@JsonKey(name: 'ReferredByEmpId') final  int? referredByEmpId;
@override final  OfficeModel? office;
@override final  DesignationModel? designation;
 final  List<DepartmentModel>? _departments;
@override List<DepartmentModel>? get departments {
  final value = _departments;
  if (value == null) return null;
  if (_departments is EqualUnmodifiableListView) return _departments;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<JobRoleModel>? _jobRoles;
@override List<JobRoleModel>? get jobRoles {
  final value = _jobRoles;
  if (value == null) return null;
  if (_jobRoles is EqualUnmodifiableListView) return _jobRoles;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<OfferLetterModel>? _offerLetters;
@override List<OfferLetterModel>? get offerLetters {
  final value = _offerLetters;
  if (value == null) return null;
  if (_offerLetters is EqualUnmodifiableListView) return _offerLetters;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of EmployeeDetails
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EmployeeDetailsCopyWith<_EmployeeDetails> get copyWith => __$EmployeeDetailsCopyWithImpl<_EmployeeDetails>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EmployeeDetailsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EmployeeDetails&&(identical(other.id, id) || other.id == id)&&(identical(other.empNo, empNo) || other.empNo == empNo)&&(identical(other.email, email) || other.email == email)&&(identical(other.about, about) || other.about == about)&&(identical(other.interests, interests) || other.interests == interests)&&(identical(other.hobbies, hobbies) || other.hobbies == hobbies)&&(identical(other.bloodGroup, bloodGroup) || other.bloodGroup == bloodGroup)&&(identical(other.maritalStatus, maritalStatus) || other.maritalStatus == maritalStatus)&&(identical(other.handicapped, handicapped) || other.handicapped == handicapped)&&(identical(other.currentAddr, currentAddr) || other.currentAddr == currentAddr)&&(identical(other.permanentAddr, permanentAddr) || other.permanentAddr == permanentAddr)&&(identical(other.isAddressSame, isAddressSame) || other.isAddressSame == isAddressSame)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&(identical(other.country, country) || other.country == country)&&(identical(other.permanentCity, permanentCity) || other.permanentCity == permanentCity)&&(identical(other.permanentState, permanentState) || other.permanentState == permanentState)&&(identical(other.permanentCountry, permanentCountry) || other.permanentCountry == permanentCountry)&&(identical(other.joiningDate, joiningDate) || other.joiningDate == joiningDate)&&(identical(other.relievedDate, relievedDate) || other.relievedDate == relievedDate)&&(identical(other.partTime, partTime) || other.partTime == partTime)&&(identical(other.workMode, workMode) || other.workMode == workMode)&&(identical(other.sickLeaves, sickLeaves) || other.sickLeaves == sickLeaves)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt)&&(identical(other.removedAt, removedAt) || other.removedAt == removedAt)&&(identical(other.managerId, managerId) || other.managerId == managerId)&&(identical(other.leavePolicyId, leavePolicyId) || other.leavePolicyId == leavePolicyId)&&(identical(other.bankDetailId, bankDetailId) || other.bankDetailId == bankDetailId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.organizationId, organizationId) || other.organizationId == organizationId)&&(identical(other.officeId, officeId) || other.officeId == officeId)&&(identical(other.designationId, designationId) || other.designationId == designationId)&&(identical(other.timetableId, timetableId) || other.timetableId == timetableId)&&(identical(other.workShiftId, workShiftId) || other.workShiftId == workShiftId)&&(identical(other.referredByEmpId, referredByEmpId) || other.referredByEmpId == referredByEmpId)&&(identical(other.office, office) || other.office == office)&&(identical(other.designation, designation) || other.designation == designation)&&const DeepCollectionEquality().equals(other._departments, _departments)&&const DeepCollectionEquality().equals(other._jobRoles, _jobRoles)&&const DeepCollectionEquality().equals(other._offerLetters, _offerLetters));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,empNo,email,about,interests,hobbies,bloodGroup,maritalStatus,handicapped,currentAddr,permanentAddr,isAddressSame,city,state,country,permanentCity,permanentState,permanentCountry,joiningDate,relievedDate,partTime,workMode,sickLeaves,createdAt,deletedAt,removedAt,managerId,leavePolicyId,bankDetailId,userId,organizationId,officeId,designationId,timetableId,workShiftId,referredByEmpId,office,designation,const DeepCollectionEquality().hash(_departments),const DeepCollectionEquality().hash(_jobRoles),const DeepCollectionEquality().hash(_offerLetters)]);

@override
String toString() {
  return 'EmployeeDetails(id: $id, empNo: $empNo, email: $email, about: $about, interests: $interests, hobbies: $hobbies, bloodGroup: $bloodGroup, maritalStatus: $maritalStatus, handicapped: $handicapped, currentAddr: $currentAddr, permanentAddr: $permanentAddr, isAddressSame: $isAddressSame, city: $city, state: $state, country: $country, permanentCity: $permanentCity, permanentState: $permanentState, permanentCountry: $permanentCountry, joiningDate: $joiningDate, relievedDate: $relievedDate, partTime: $partTime, workMode: $workMode, sickLeaves: $sickLeaves, createdAt: $createdAt, deletedAt: $deletedAt, removedAt: $removedAt, managerId: $managerId, leavePolicyId: $leavePolicyId, bankDetailId: $bankDetailId, userId: $userId, organizationId: $organizationId, officeId: $officeId, designationId: $designationId, timetableId: $timetableId, workShiftId: $workShiftId, referredByEmpId: $referredByEmpId, office: $office, designation: $designation, departments: $departments, jobRoles: $jobRoles, offerLetters: $offerLetters)';
}


}

/// @nodoc
abstract mixin class _$EmployeeDetailsCopyWith<$Res> implements $EmployeeDetailsCopyWith<$Res> {
  factory _$EmployeeDetailsCopyWith(_EmployeeDetails value, $Res Function(_EmployeeDetails) _then) = __$EmployeeDetailsCopyWithImpl;
@override @useResult
$Res call({
 int id, int empNo, String? email, String? about, String? interests, String? hobbies, String? bloodGroup, String? maritalStatus, bool handicapped, String? currentAddr, String? permanentAddr, bool isAddressSame, String? city, String? state, String? country, String? permanentCity, String? permanentState, String? permanentCountry, DateTime? joiningDate, DateTime? relievedDate, bool? partTime, String? workMode, double? sickLeaves, DateTime? createdAt, DateTime? deletedAt, DateTime? removedAt,@JsonKey(name: 'ManagerId') int? managerId,@JsonKey(name: 'LeavePolicyId') int? leavePolicyId,@JsonKey(name: 'BankDetailId') int? bankDetailId,@JsonKey(name: 'UserId') int? userId,@JsonKey(name: 'OrganizationId') int? organizationId,@JsonKey(name: 'OfficeId') int? officeId,@JsonKey(name: 'DesignationId') int? designationId,@JsonKey(name: 'TimetableId') int? timetableId,@JsonKey(name: 'WorkShiftId') int? workShiftId,@JsonKey(name: 'ReferredByEmpId') int? referredByEmpId, OfficeModel? office, DesignationModel? designation, List<DepartmentModel>? departments, List<JobRoleModel>? jobRoles, List<OfferLetterModel>? offerLetters
});


@override $OfficeModelCopyWith<$Res>? get office;@override $DesignationModelCopyWith<$Res>? get designation;

}
/// @nodoc
class __$EmployeeDetailsCopyWithImpl<$Res>
    implements _$EmployeeDetailsCopyWith<$Res> {
  __$EmployeeDetailsCopyWithImpl(this._self, this._then);

  final _EmployeeDetails _self;
  final $Res Function(_EmployeeDetails) _then;

/// Create a copy of EmployeeDetails
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? empNo = null,Object? email = freezed,Object? about = freezed,Object? interests = freezed,Object? hobbies = freezed,Object? bloodGroup = freezed,Object? maritalStatus = freezed,Object? handicapped = null,Object? currentAddr = freezed,Object? permanentAddr = freezed,Object? isAddressSame = null,Object? city = freezed,Object? state = freezed,Object? country = freezed,Object? permanentCity = freezed,Object? permanentState = freezed,Object? permanentCountry = freezed,Object? joiningDate = freezed,Object? relievedDate = freezed,Object? partTime = freezed,Object? workMode = freezed,Object? sickLeaves = freezed,Object? createdAt = freezed,Object? deletedAt = freezed,Object? removedAt = freezed,Object? managerId = freezed,Object? leavePolicyId = freezed,Object? bankDetailId = freezed,Object? userId = freezed,Object? organizationId = freezed,Object? officeId = freezed,Object? designationId = freezed,Object? timetableId = freezed,Object? workShiftId = freezed,Object? referredByEmpId = freezed,Object? office = freezed,Object? designation = freezed,Object? departments = freezed,Object? jobRoles = freezed,Object? offerLetters = freezed,}) {
  return _then(_EmployeeDetails(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,empNo: null == empNo ? _self.empNo : empNo // ignore: cast_nullable_to_non_nullable
as int,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,about: freezed == about ? _self.about : about // ignore: cast_nullable_to_non_nullable
as String?,interests: freezed == interests ? _self.interests : interests // ignore: cast_nullable_to_non_nullable
as String?,hobbies: freezed == hobbies ? _self.hobbies : hobbies // ignore: cast_nullable_to_non_nullable
as String?,bloodGroup: freezed == bloodGroup ? _self.bloodGroup : bloodGroup // ignore: cast_nullable_to_non_nullable
as String?,maritalStatus: freezed == maritalStatus ? _self.maritalStatus : maritalStatus // ignore: cast_nullable_to_non_nullable
as String?,handicapped: null == handicapped ? _self.handicapped : handicapped // ignore: cast_nullable_to_non_nullable
as bool,currentAddr: freezed == currentAddr ? _self.currentAddr : currentAddr // ignore: cast_nullable_to_non_nullable
as String?,permanentAddr: freezed == permanentAddr ? _self.permanentAddr : permanentAddr // ignore: cast_nullable_to_non_nullable
as String?,isAddressSame: null == isAddressSame ? _self.isAddressSame : isAddressSame // ignore: cast_nullable_to_non_nullable
as bool,city: freezed == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String?,state: freezed == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String?,country: freezed == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String?,permanentCity: freezed == permanentCity ? _self.permanentCity : permanentCity // ignore: cast_nullable_to_non_nullable
as String?,permanentState: freezed == permanentState ? _self.permanentState : permanentState // ignore: cast_nullable_to_non_nullable
as String?,permanentCountry: freezed == permanentCountry ? _self.permanentCountry : permanentCountry // ignore: cast_nullable_to_non_nullable
as String?,joiningDate: freezed == joiningDate ? _self.joiningDate : joiningDate // ignore: cast_nullable_to_non_nullable
as DateTime?,relievedDate: freezed == relievedDate ? _self.relievedDate : relievedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,partTime: freezed == partTime ? _self.partTime : partTime // ignore: cast_nullable_to_non_nullable
as bool?,workMode: freezed == workMode ? _self.workMode : workMode // ignore: cast_nullable_to_non_nullable
as String?,sickLeaves: freezed == sickLeaves ? _self.sickLeaves : sickLeaves // ignore: cast_nullable_to_non_nullable
as double?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,removedAt: freezed == removedAt ? _self.removedAt : removedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,managerId: freezed == managerId ? _self.managerId : managerId // ignore: cast_nullable_to_non_nullable
as int?,leavePolicyId: freezed == leavePolicyId ? _self.leavePolicyId : leavePolicyId // ignore: cast_nullable_to_non_nullable
as int?,bankDetailId: freezed == bankDetailId ? _self.bankDetailId : bankDetailId // ignore: cast_nullable_to_non_nullable
as int?,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as int?,organizationId: freezed == organizationId ? _self.organizationId : organizationId // ignore: cast_nullable_to_non_nullable
as int?,officeId: freezed == officeId ? _self.officeId : officeId // ignore: cast_nullable_to_non_nullable
as int?,designationId: freezed == designationId ? _self.designationId : designationId // ignore: cast_nullable_to_non_nullable
as int?,timetableId: freezed == timetableId ? _self.timetableId : timetableId // ignore: cast_nullable_to_non_nullable
as int?,workShiftId: freezed == workShiftId ? _self.workShiftId : workShiftId // ignore: cast_nullable_to_non_nullable
as int?,referredByEmpId: freezed == referredByEmpId ? _self.referredByEmpId : referredByEmpId // ignore: cast_nullable_to_non_nullable
as int?,office: freezed == office ? _self.office : office // ignore: cast_nullable_to_non_nullable
as OfficeModel?,designation: freezed == designation ? _self.designation : designation // ignore: cast_nullable_to_non_nullable
as DesignationModel?,departments: freezed == departments ? _self._departments : departments // ignore: cast_nullable_to_non_nullable
as List<DepartmentModel>?,jobRoles: freezed == jobRoles ? _self._jobRoles : jobRoles // ignore: cast_nullable_to_non_nullable
as List<JobRoleModel>?,offerLetters: freezed == offerLetters ? _self._offerLetters : offerLetters // ignore: cast_nullable_to_non_nullable
as List<OfferLetterModel>?,
  ));
}

/// Create a copy of EmployeeDetails
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$OfficeModelCopyWith<$Res>? get office {
    if (_self.office == null) {
    return null;
  }

  return $OfficeModelCopyWith<$Res>(_self.office!, (value) {
    return _then(_self.copyWith(office: value));
  });
}/// Create a copy of EmployeeDetails
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DesignationModelCopyWith<$Res>? get designation {
    if (_self.designation == null) {
    return null;
  }

  return $DesignationModelCopyWith<$Res>(_self.designation!, (value) {
    return _then(_self.copyWith(designation: value));
  });
}
}

// dart format on
