// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'teammate_filter.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$TeammateFilter<T> {

 String get label; String get key; List<TeammateFilterOption> get options; T? get selectedOption;
/// Create a copy of TeammateFilter
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TeammateFilterCopyWith<T, TeammateFilter<T>> get copyWith => _$TeammateFilterCopyWithImpl<T, TeammateFilter<T>>(this as TeammateFilter<T>, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TeammateFilter<T>&&(identical(other.label, label) || other.label == label)&&(identical(other.key, key) || other.key == key)&&const DeepCollectionEquality().equals(other.options, options)&&const DeepCollectionEquality().equals(other.selectedOption, selectedOption));
}


@override
int get hashCode => Object.hash(runtimeType,label,key,const DeepCollectionEquality().hash(options),const DeepCollectionEquality().hash(selectedOption));

@override
String toString() {
  return 'TeammateFilter<$T>(label: $label, key: $key, options: $options, selectedOption: $selectedOption)';
}


}

/// @nodoc
abstract mixin class $TeammateFilterCopyWith<T,$Res>  {
  factory $TeammateFilterCopyWith(TeammateFilter<T> value, $Res Function(TeammateFilter<T>) _then) = _$TeammateFilterCopyWithImpl;
@useResult
$Res call({
 String label, String key, List<TeammateFilterOption> options, T? selectedOption
});




}
/// @nodoc
class _$TeammateFilterCopyWithImpl<T,$Res>
    implements $TeammateFilterCopyWith<T, $Res> {
  _$TeammateFilterCopyWithImpl(this._self, this._then);

  final TeammateFilter<T> _self;
  final $Res Function(TeammateFilter<T>) _then;

/// Create a copy of TeammateFilter
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? label = null,Object? key = null,Object? options = null,Object? selectedOption = freezed,}) {
  return _then(_self.copyWith(
label: null == label ? _self.label : label // ignore: cast_nullable_to_non_nullable
as String,key: null == key ? _self.key : key // ignore: cast_nullable_to_non_nullable
as String,options: null == options ? _self.options : options // ignore: cast_nullable_to_non_nullable
as List<TeammateFilterOption>,selectedOption: freezed == selectedOption ? _self.selectedOption : selectedOption // ignore: cast_nullable_to_non_nullable
as T?,
  ));
}

}


/// @nodoc


class _TeammateFilter<T> implements TeammateFilter<T> {
  const _TeammateFilter({required this.label, required this.key, required final  List<TeammateFilterOption> options, this.selectedOption}): _options = options;
  

@override final  String label;
@override final  String key;
 final  List<TeammateFilterOption> _options;
@override List<TeammateFilterOption> get options {
  if (_options is EqualUnmodifiableListView) return _options;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_options);
}

@override final  T? selectedOption;

/// Create a copy of TeammateFilter
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TeammateFilterCopyWith<T, _TeammateFilter<T>> get copyWith => __$TeammateFilterCopyWithImpl<T, _TeammateFilter<T>>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TeammateFilter<T>&&(identical(other.label, label) || other.label == label)&&(identical(other.key, key) || other.key == key)&&const DeepCollectionEquality().equals(other._options, _options)&&const DeepCollectionEquality().equals(other.selectedOption, selectedOption));
}


@override
int get hashCode => Object.hash(runtimeType,label,key,const DeepCollectionEquality().hash(_options),const DeepCollectionEquality().hash(selectedOption));

@override
String toString() {
  return 'TeammateFilter<$T>(label: $label, key: $key, options: $options, selectedOption: $selectedOption)';
}


}

/// @nodoc
abstract mixin class _$TeammateFilterCopyWith<T,$Res> implements $TeammateFilterCopyWith<T, $Res> {
  factory _$TeammateFilterCopyWith(_TeammateFilter<T> value, $Res Function(_TeammateFilter<T>) _then) = __$TeammateFilterCopyWithImpl;
@override @useResult
$Res call({
 String label, String key, List<TeammateFilterOption> options, T? selectedOption
});




}
/// @nodoc
class __$TeammateFilterCopyWithImpl<T,$Res>
    implements _$TeammateFilterCopyWith<T, $Res> {
  __$TeammateFilterCopyWithImpl(this._self, this._then);

  final _TeammateFilter<T> _self;
  final $Res Function(_TeammateFilter<T>) _then;

/// Create a copy of TeammateFilter
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? label = null,Object? key = null,Object? options = null,Object? selectedOption = freezed,}) {
  return _then(_TeammateFilter<T>(
label: null == label ? _self.label : label // ignore: cast_nullable_to_non_nullable
as String,key: null == key ? _self.key : key // ignore: cast_nullable_to_non_nullable
as String,options: null == options ? _self._options : options // ignore: cast_nullable_to_non_nullable
as List<TeammateFilterOption>,selectedOption: freezed == selectedOption ? _self.selectedOption : selectedOption // ignore: cast_nullable_to_non_nullable
as T?,
  ));
}


}

/// @nodoc
mixin _$TeammateFilterOption<T> {

 T get data; String get label;
/// Create a copy of TeammateFilterOption
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TeammateFilterOptionCopyWith<T, TeammateFilterOption<T>> get copyWith => _$TeammateFilterOptionCopyWithImpl<T, TeammateFilterOption<T>>(this as TeammateFilterOption<T>, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TeammateFilterOption<T>&&const DeepCollectionEquality().equals(other.data, data)&&(identical(other.label, label) || other.label == label));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(data),label);

@override
String toString() {
  return 'TeammateFilterOption<$T>(data: $data, label: $label)';
}


}

/// @nodoc
abstract mixin class $TeammateFilterOptionCopyWith<T,$Res>  {
  factory $TeammateFilterOptionCopyWith(TeammateFilterOption<T> value, $Res Function(TeammateFilterOption<T>) _then) = _$TeammateFilterOptionCopyWithImpl;
@useResult
$Res call({
 T data, String label
});




}
/// @nodoc
class _$TeammateFilterOptionCopyWithImpl<T,$Res>
    implements $TeammateFilterOptionCopyWith<T, $Res> {
  _$TeammateFilterOptionCopyWithImpl(this._self, this._then);

  final TeammateFilterOption<T> _self;
  final $Res Function(TeammateFilterOption<T>) _then;

/// Create a copy of TeammateFilterOption
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? data = freezed,Object? label = null,}) {
  return _then(_self.copyWith(
data: freezed == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as T,label: null == label ? _self.label : label // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc


class _TeammateFilterOption<T> implements TeammateFilterOption<T> {
  const _TeammateFilterOption({required this.data, required this.label});
  

@override final  T data;
@override final  String label;

/// Create a copy of TeammateFilterOption
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TeammateFilterOptionCopyWith<T, _TeammateFilterOption<T>> get copyWith => __$TeammateFilterOptionCopyWithImpl<T, _TeammateFilterOption<T>>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TeammateFilterOption<T>&&const DeepCollectionEquality().equals(other.data, data)&&(identical(other.label, label) || other.label == label));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(data),label);

@override
String toString() {
  return 'TeammateFilterOption<$T>(data: $data, label: $label)';
}


}

/// @nodoc
abstract mixin class _$TeammateFilterOptionCopyWith<T,$Res> implements $TeammateFilterOptionCopyWith<T, $Res> {
  factory _$TeammateFilterOptionCopyWith(_TeammateFilterOption<T> value, $Res Function(_TeammateFilterOption<T>) _then) = __$TeammateFilterOptionCopyWithImpl;
@override @useResult
$Res call({
 T data, String label
});




}
/// @nodoc
class __$TeammateFilterOptionCopyWithImpl<T,$Res>
    implements _$TeammateFilterOptionCopyWith<T, $Res> {
  __$TeammateFilterOptionCopyWithImpl(this._self, this._then);

  final _TeammateFilterOption<T> _self;
  final $Res Function(_TeammateFilterOption<T>) _then;

/// Create a copy of TeammateFilterOption
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? data = freezed,Object? label = null,}) {
  return _then(_TeammateFilterOption<T>(
data: freezed == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as T,label: null == label ? _self.label : label // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
