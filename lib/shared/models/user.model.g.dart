// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserModel _$UserModelFromJson(Map<String, dynamic> json) => _UserModel(
  id: (json['id'] as num).toInt(),
  firstName: json['firstName'] as String?,
  middleName: json['middleName'] as String?,
  lastName: json['lastName'] as String?,
  displayName: json['displayName'] as String?,
  mobile: json['mobile'] as String?,
  countryCode: json['countryCode'] as String?,
  personalMobile: json['personalMobile'] as String?,
  dob: json['dob'] == null ? null : DateTime.parse(json['dob'] as String),
  gender: $enumDecodeNullable(_$GenderEnumMap, json['gender']),
  personalEmail: json['personalEmail'] as String?,
  image: json['image'] as String?,
  role: $enumDecodeNullable(_$RoleEnumMap, json['role']),
  details: employeeDetailsFromJson(json['employees']),
);

Map<String, dynamic> _$UserModelToJson(_UserModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'firstName': instance.firstName,
      'middleName': instance.middleName,
      'lastName': instance.lastName,
      'displayName': instance.displayName,
      'mobile': instance.mobile,
      'countryCode': instance.countryCode,
      'personalMobile': instance.personalMobile,
      'dob': instance.dob?.toIso8601String(),
      'gender': _$GenderEnumMap[instance.gender],
      'personalEmail': instance.personalEmail,
      'image': instance.image,
      'role': _$RoleEnumMap[instance.role],
      'employees': instance.details,
    };

const _$GenderEnumMap = {
  Gender.male: 'male',
  Gender.female: 'female',
  Gender.other: 'other',
};

const _$RoleEnumMap = {
  Role.employee: 'employee',
  Role.hr: 'hr',
  Role.admin: 'admin',
  Role.orgAdmin: 'org-admin',
};

_EmployeeDetails _$EmployeeDetailsFromJson(Map<String, dynamic> json) =>
    _EmployeeDetails(
      id: (json['id'] as num).toInt(),
      empNo: (json['empNo'] as num).toInt(),
      email: json['email'] as String?,
      about: json['about'] as String?,
      interests: json['interests'] as String?,
      hobbies: json['hobbies'] as String?,
      bloodGroup: json['bloodGroup'] as String?,
      maritalStatus: json['maritalStatus'] as String?,
      handicapped: json['handicapped'] as bool,
      currentAddr: json['currentAddr'] as String?,
      permanentAddr: json['permanentAddr'] as String?,
      isAddressSame: json['isAddressSame'] as bool,
      city: json['city'] as String?,
      state: json['state'] as String?,
      country: json['country'] as String?,
      permanentCity: json['permanentCity'] as String?,
      permanentState: json['permanentState'] as String?,
      permanentCountry: json['permanentCountry'] as String?,
      joiningDate: json['joiningDate'] == null
          ? null
          : DateTime.parse(json['joiningDate'] as String),
      relievedDate: json['relievedDate'] == null
          ? null
          : DateTime.parse(json['relievedDate'] as String),
      partTime: json['partTime'] as bool?,
      workMode: json['workMode'] as String?,
      sickLeaves: (json['sickLeaves'] as num?)?.toDouble(),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      removedAt: json['removedAt'] == null
          ? null
          : DateTime.parse(json['removedAt'] as String),
      managerId: (json['ManagerId'] as num?)?.toInt(),
      leavePolicyId: (json['LeavePolicyId'] as num?)?.toInt(),
      bankDetailId: (json['BankDetailId'] as num?)?.toInt(),
      userId: (json['UserId'] as num?)?.toInt(),
      organizationId: (json['OrganizationId'] as num?)?.toInt(),
      officeId: (json['OfficeId'] as num?)?.toInt(),
      designationId: (json['DesignationId'] as num?)?.toInt(),
      timetableId: (json['TimetableId'] as num?)?.toInt(),
      workShiftId: (json['WorkShiftId'] as num?)?.toInt(),
      referredByEmpId: (json['ReferredByEmpId'] as num?)?.toInt(),
      office: json['office'] == null
          ? null
          : OfficeModel.fromJson(json['office'] as Map<String, dynamic>),
      designation: json['designation'] == null
          ? null
          : DesignationModel.fromJson(
              json['designation'] as Map<String, dynamic>,
            ),
      departments: (json['departments'] as List<dynamic>?)
          ?.map((e) => DepartmentModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      jobRoles: (json['jobRoles'] as List<dynamic>?)
          ?.map((e) => JobRoleModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      offerLetters: (json['offerLetters'] as List<dynamic>?)
          ?.map((e) => OfferLetterModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$EmployeeDetailsToJson(_EmployeeDetails instance) =>
    <String, dynamic>{
      'id': instance.id,
      'empNo': instance.empNo,
      'email': instance.email,
      'about': instance.about,
      'interests': instance.interests,
      'hobbies': instance.hobbies,
      'bloodGroup': instance.bloodGroup,
      'maritalStatus': instance.maritalStatus,
      'handicapped': instance.handicapped,
      'currentAddr': instance.currentAddr,
      'permanentAddr': instance.permanentAddr,
      'isAddressSame': instance.isAddressSame,
      'city': instance.city,
      'state': instance.state,
      'country': instance.country,
      'permanentCity': instance.permanentCity,
      'permanentState': instance.permanentState,
      'permanentCountry': instance.permanentCountry,
      'joiningDate': instance.joiningDate?.toIso8601String(),
      'relievedDate': instance.relievedDate?.toIso8601String(),
      'partTime': instance.partTime,
      'workMode': instance.workMode,
      'sickLeaves': instance.sickLeaves,
      'createdAt': instance.createdAt?.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'removedAt': instance.removedAt?.toIso8601String(),
      'ManagerId': instance.managerId,
      'LeavePolicyId': instance.leavePolicyId,
      'BankDetailId': instance.bankDetailId,
      'UserId': instance.userId,
      'OrganizationId': instance.organizationId,
      'OfficeId': instance.officeId,
      'DesignationId': instance.designationId,
      'TimetableId': instance.timetableId,
      'WorkShiftId': instance.workShiftId,
      'ReferredByEmpId': instance.referredByEmpId,
      'office': instance.office,
      'designation': instance.designation,
      'departments': instance.departments,
      'jobRoles': instance.jobRoles,
      'offerLetters': instance.offerLetters,
    };
