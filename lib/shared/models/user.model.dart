import 'package:freezed_annotation/freezed_annotation.dart';

import '../../features/teammates/data/models/teammate.model.dart';
import '../../features/teammates/data/models/teammate_detail.model.dart';

part 'user.model.freezed.dart';
part 'user.model.g.dart';

EmployeeDetails? employeeDetailsFromJson(Object? json) {
  if (json is List) {
    return EmployeeDetails.fromJson(json.first);
  }
  return null;
}

@freezed
abstract class UserModel with _$UserModel {
  const factory UserModel({
    required int id,
    required String? firstName,
    required String? middleName,
    required String? lastName,
    required String? displayName,
    required String? mobile,
    required String? countryCode,
    required String? personalMobile,
    required DateTime? dob,
    required Gender? gender,
    required String? personalEmail,
    required String? image,
    required Role? role,
    @Json<PERSON>ey(name: 'employees', fromJson: employeeDetailsFromJson)
    final EmployeeDetails? details,
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModel<PERSON>romJson(json);
}

@freezed
abstract class EmployeeDetails with _$EmployeeDetails {
  const factory EmployeeDetails({
    required final int id,
    required final int empNo,
    final String? email,
    final String? about,
    final String? interests,
    final String? hobbies,
    final String? bloodGroup,
    final String? maritalStatus,
    required final bool handicapped,
    final String? currentAddr,
    final String? permanentAddr,
    required final bool isAddressSame,
    final String? city,
    final String? state,
    final String? country,
    final String? permanentCity,
    final String? permanentState,
    final String? permanentCountry,
    final DateTime? joiningDate,
    final DateTime? relievedDate,
    final bool? partTime,
    final String? workMode,
    final double? sickLeaves,
    final DateTime? createdAt,
    final DateTime? deletedAt,
    final DateTime? removedAt,
    @JsonKey(name: 'ManagerId') final int? managerId,
    @JsonKey(name: 'LeavePolicyId') final int? leavePolicyId,
    @JsonKey(name: 'BankDetailId') final int? bankDetailId,
    @JsonKey(name: 'UserId') final int? userId,
    @JsonKey(name: 'OrganizationId') final int? organizationId,
    @JsonKey(name: 'OfficeId') final int? officeId,
    @JsonKey(name: 'DesignationId') final int? designationId,
    @JsonKey(name: 'TimetableId') final int? timetableId,
    @JsonKey(name: 'WorkShiftId') final int? workShiftId,
    @JsonKey(name: 'ReferredByEmpId') final int? referredByEmpId,
    final OfficeModel? office,
    final DesignationModel? designation,
    final List<DepartmentModel>? departments,
    final List<JobRoleModel>? jobRoles,
    final List<OfferLetterModel>? offerLetters,
  }) = _EmployeeDetails;

  factory EmployeeDetails.fromJson(Map<String, dynamic> json) =>
      _$EmployeeDetailsFromJson(json);
}

enum Gender { male, female, other }

enum Role {
  employee,
  hr,
  admin,
  @JsonValue('org-admin')
  orgAdmin,
}
