import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/shared/widgets/loading_placeholder.dart';

class FileImageWithLoading extends ConsumerWidget {
  const FileImageWithLoading({
    super.key,
    required this.file,
    required this.imageSize,
  });

  final File file;
  final Size imageSize;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Image.file(
      file,
      height: imageSize.height,
      width: imageSize.width,
      fit: BoxFit.cover,
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded || frame != null) {
          return child;
        } else {
          return loader();
        }
      },
    );
  }

  Widget loader() {
    return LoadingPlaceholder(
      height: imageSize.height,
      width: imageSize.width,
      child: SizedBox.square(
        dimension: min(imageSize.height, imageSize.width) / 5,
        child: Center(child: CircularProgressIndicator.adaptive()),
      ),
    );
  }
}
