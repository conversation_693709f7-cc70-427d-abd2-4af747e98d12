import 'package:flutter/material.dart';

import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';

class ScreenBackButton extends ConsumerWidget {
  const ScreenBackButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      onTap: () {
        context.pop();
      },
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          children: [
            SVGImage(Assets.svgs.backArrowIcon),
            Space.x(4),
            Text(
              'Back',
              style: context.textStyles.titleMedium?.copyWith(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: context.colors.secondary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
