import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';

class CardContainer extends ConsumerWidget {
  const CardContainer({super.key, this.child, this.elevation});

  final Widget? child;
  final double? elevation;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: EdgeInsets.zero,
      surfaceTintColor: context.colors.surface,
      color: context.colors.surface,
      elevation: elevation ?? (context.isDarkMode ? 6 : 3),
      shadowColor:
          (context.isDarkMode
                  ? context.colors.surface
                  : context.colors.secondary)
              .withValues(alpha: 0.5),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
      child: child,
    );
  }
}
