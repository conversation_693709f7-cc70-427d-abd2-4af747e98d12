import 'dart:developer';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';

class CachedImage extends ConsumerWidget {
  const CachedImage({
    super.key,
    required this.url,
    this.height,
    this.width,
    this.fit = BoxFit.cover,
    this.decoration,
    this.borderRadius,
    this.radiusValue,
  });

  final String url;
  final double? height;
  final double? width;
  final BoxFit fit;
  final Decoration? decoration;
  final BorderRadius? borderRadius;
  final double? radiusValue;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      clipBehavior: Clip.antiAliasWithSaveLayer,
      decoration:
          decoration ??
          BoxDecoration(
            borderRadius:
                borderRadius ?? BorderRadius.circular(radiusValue ?? 8),
          ),
      child: CachedNetworkImage(
        imageUrl: url,
        height: height,
        width: width,
        fit: fit,
        memCacheHeight: (context.screenWidth).ceil(),
        memCacheWidth: (context.screenWidth).ceil(),
        placeholder: (context, url) => Container(
          decoration:
              decoration ??
              BoxDecoration(
                borderRadius:
                    borderRadius ?? BorderRadius.circular(radiusValue ?? 8),
                color: context.colors.outline,
              ),
        ),
        errorWidget: (context, url, error) {
          log(error.toString());
          return Container(
            color: context.colors.outline,
            child: Icon(Icons.error, color: context.colors.error, size: 16),
          );
        },
      ),
    );
  }
}
