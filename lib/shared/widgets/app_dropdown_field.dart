import 'dart:math';

import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';

class AppDropdownField<T> extends ConsumerWidget {
  final bool readOnly;
  final TextEditingController? controller;
  final List<DropdownMenuItem<T>> items;
  final T? value;
  final String? labelText;
  final String? hintText;
  final bool obscureText;
  final TextInputType? keyboardType;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? Function(T?)? validator;
  final void Function(T? value) onChanged;
  final void Function(String)? onFieldSubmitted;
  final int? maxLines;
  final int? minLines;
  final bool enabled;
  final TextCapitalization textCapitalization;
  final TextInputAction? textInputAction;
  final FocusNode? focusNode;
  final EdgeInsetsGeometry? contentPadding;
  final Color? fillColor;
  final List<Widget> Function(BuildContext)? selectedItemBuilder;

  const AppDropdownField({
    super.key,
    this.readOnly = false,
    this.controller,
    required this.items,
    this.value,
    this.labelText,
    this.hintText,
    this.obscureText = false,
    this.keyboardType,
    this.prefixIcon,
    this.suffixIcon,
    this.validator,
    required this.onChanged,
    this.onFieldSubmitted,
    this.maxLines = 1,
    this.minLines,
    this.enabled = true,
    this.textCapitalization = TextCapitalization.none,
    this.textInputAction,
    this.focusNode,
    this.contentPadding,
    this.fillColor,
    this.selectedItemBuilder,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final border = OutlineInputBorder(
      borderRadius: BorderRadius.circular(10),
      borderSide: BorderSide(color: context.appTextFieldBorderColor),
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (labelText != null) ...[
          Text(
            labelText ?? '',
            style: context.textStyles.bodyMedium?.copyWith(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: Color(0xFF7A7A7A),
            ),
          ),
          Space.y(4),
        ],
        DropdownButtonFormField2<T>(
          value: value,
          validator: validator,
          onChanged: onChanged,
          focusNode: focusNode,
          hint: Text(hintText ?? 'Select'),
          style: context.textStyles.bodyLarge?.copyWith(
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),

          decoration: InputDecoration(
            hintText: hintText,
            prefixIcon: prefixIcon,
            suffixIcon: suffixIcon,
            contentPadding:
                contentPadding ??
                EdgeInsets.symmetric(vertical: 12).copyWith(right: 12),
            filled: true,
            fillColor: fillColor ?? context.appTextFieldFillColor,
            border: border,
            errorBorder: border.copyWith(
              borderSide: BorderSide(color: context.colors.error),
            ),
            focusedBorder: border,
            enabledBorder: border,
            disabledBorder: border,
            focusedErrorBorder: border.copyWith(
              borderSide: BorderSide(color: context.colors.error),
            ),
          ),
          items: items,
          selectedItemBuilder: selectedItemBuilder,
          dropdownStyleData: DropdownStyleData(
            offset: Offset(0, -6),
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
            elevation: 1,
          ),
          iconStyleData: IconStyleData(
            icon: Transform.rotate(
              angle: pi,
              child: SVGImage(
                Assets.svgs.dropdownArrowIcon,
                height: 18,
                width: 18,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
