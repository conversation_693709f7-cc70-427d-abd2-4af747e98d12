import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';

class AppTextField extends ConsumerWidget {
  final bool readOnly;
  final bool autofocus;
  final TextEditingController? controller;
  final String? initialValue;
  final String? labelText;
  final String? hintText;
  final bool obscureText;
  final TextInputType? keyboardType;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? Function(String? value)? validator;
  final void Function(String value)? onChanged;
  final void Function(String value)? onFieldSubmitted;
  final int? maxLines;
  final int? minLines;
  final bool enabled;
  final TextCapitalization textCapitalization;
  final TextInputAction? textInputAction;
  final FocusNode? focusNode;
  final EdgeInsetsGeometry? contentPadding;
  final Color? fillColor;
  final Function? onTap;

  const AppTextField({
    super.key,
    this.readOnly = false,
    this.autofocus = false,
    this.controller,
    this.initialValue,
    this.labelText,
    this.hintText,
    this.obscureText = false,
    this.keyboardType,
    this.prefixIcon,
    this.suffixIcon,
    this.validator,
    this.onChanged,
    this.onFieldSubmitted,
    this.maxLines = 1,
    this.minLines,
    this.enabled = true,
    this.textCapitalization = TextCapitalization.none,
    this.textInputAction,
    this.focusNode,
    this.contentPadding,
    this.fillColor,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final border = OutlineInputBorder(
      borderRadius: BorderRadius.circular(10),
      borderSide: BorderSide(color: context.appTextFieldBorderColor),
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (labelText != null) ...[
          Text(
            labelText ?? '',
            style: context.textStyles.bodyMedium?.copyWith(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: Color(0xFF7A7A7A),
            ),
          ),
          Space.y(4),
        ],
        TextFormField(
          readOnly: readOnly,
          enableInteractiveSelection: !readOnly,
          autofocus: autofocus,
          onTap: () => onTap?.call(),
          controller: controller,
          initialValue: initialValue,
          obscureText: obscureText,
          keyboardType: keyboardType,
          validator: validator,
          onChanged: onChanged,
          onFieldSubmitted: onFieldSubmitted,
          maxLines: maxLines,
          minLines: minLines,
          enabled: enabled,
          textCapitalization: textCapitalization,
          textInputAction: textInputAction,
          focusNode: focusNode,
          style: context.textStyles.bodyLarge?.copyWith(
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),
          decoration: InputDecoration(
            hintText: hintText,
            prefixIcon: prefixIcon,
            suffixIcon: suffixIcon,
            contentPadding: contentPadding,
            filled: true,
            fillColor: fillColor ?? context.appTextFieldFillColor,
            border: border,
            errorBorder: border.copyWith(
              borderSide: BorderSide(color: context.colors.error),
            ),
            focusedBorder: border,
            enabledBorder: border,
            disabledBorder: border,
            focusedErrorBorder: border.copyWith(
              borderSide: BorderSide(color: context.colors.error),
            ),
          ),
          onTapOutside: (event) {
            FocusScope.of(context).unfocus();
          },
        ),
      ],
    );
  }
}
