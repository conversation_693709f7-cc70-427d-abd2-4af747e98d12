import 'dart:io';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';

class ConfirmationDialog extends ConsumerWidget {
  const ConfirmationDialog({
    super.key,
    this.title,
    this.cancelText,
    this.confirmText,
    this.onCancel,
    this.onConfirm,
  });

  final String? title;
  final String? cancelText;
  final String? confirmText;
  final Function? onCancel;
  final Function? onConfirm;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AlertDialog.adaptive(
      title: Text(
        title ?? 'Confirmation',
        style: context.textStyles.titleMedium?.copyWith(
          color: context.colors.secondary,
        ),
      ),

      actions: [
        TextButton(
          style: Platform.isIOS
              ? TextButton.styleFrom(
                  elevation: 0,
                  splashFactory: NoSplash.splashFactory,
                  overlayColor: Colors.transparent,
                )
              : TextButton.styleFrom(backgroundColor: context.colors.outline),
          onPressed: () {
            onCancel?.call();
            context.pop();
          },
          child: Text(
            cancelText ?? 'Cancel',
            style: context.textStyles.bodyMedium?.copyWith(
              color: context.colors.secondary,
            ),
          ),
        ),
        TextButton(
          style: Platform.isIOS
              ? TextButton.styleFrom(
                  elevation: 0,
                  splashFactory: NoSplash.splashFactory,
                  overlayColor: Colors.transparent,
                )
              : TextButton.styleFrom(backgroundColor: context.colors.outline),
          onPressed: () {
            onConfirm?.call();
            context.pop();
          },
          child: Text(
            confirmText ?? 'Confirm',
            style: context.textStyles.bodyMedium?.copyWith(
              color: context.colors.secondary,
            ),
          ),
        ),
      ],
    );
  }
}
