import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/snackbar.helper.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:intl/intl.dart';

class MonthRangeSelection extends HookConsumerWidget {
  const MonthRangeSelection({
    super.key,
    required this.startDate,
    required this.endDate,
    required this.onFilterSelected,
    this.filterButtonColor,
  });

  final Color? filterButtonColor;
  final DateTime? startDate;
  final DateTime? endDate;
  final Function(DateTime startDate, DateTime endDate) onFilterSelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      onTap: () {
        showModalBottomSheet(
          useRootNavigator: true,
          context: context,
          clipBehavior: Clip.antiAlias,
          backgroundColor: context.colors.surface,
          scrollControlDisabledMaxHeightRatio: 0.6,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(14),
          ),
          builder: (context) {
            return MonthRangeSelectionSheet(
              startDate: startDate ?? DateTime.now().copyWith(day: 1).toUtc(),
              endDate:
                  endDate ??
                  DateTime.now()
                      .copyWith(day: 1, month: DateTime.now().month + 1)
                      .toUtc(),
              onFilterSelected: onFilterSelected,
            );
          },
        );
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: filterButtonColor ?? context.colors.outline,
        ),
        child: Text(
          '${DateFormat('MMM').format(startDate ?? DateTime.now().toUtc())} - ${DateFormat('MMM').format(endDate ?? endDate ?? DateTime.now().toUtc())}',
          style: context.textStyles.titleMedium?.copyWith(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: context.colors.secondary,
          ),
        ),
      ),
    );
  }
}

class MonthRangeSelectionSheet extends HookConsumerWidget {
  const MonthRangeSelectionSheet({
    super.key,
    required this.startDate,
    required this.endDate,
    required this.onFilterSelected,
  });

  final DateTime startDate;
  final DateTime endDate;
  final Function(DateTime startDate, DateTime endDate) onFilterSelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isSelectingStartDate = useState(true);
    final selectedStartDate = useState(startDate);
    final selectedEndDate = useState(endDate);

    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Select ${isSelectingStartDate.value ? 'Start Date' : 'End Date'}',
                style: context.textStyles.titleMedium,
              ),
              IconButton.filled(
                onPressed: () {
                  if (isSelectingStartDate.value) {
                    isSelectingStartDate.value = false;
                  } else {
                    if (selectedStartDate.value.isAfter(
                      selectedEndDate.value,
                    )) {
                      context.showError('End Date should be after Start Date');
                      return;
                    }

                    onFilterSelected(
                      selectedStartDate.value,
                      selectedEndDate.value,
                    );
                  }
                },
                icon: Icon(
                  isSelectingStartDate.value
                      ? Icons.arrow_forward
                      : Icons.check,
                ),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                onPressed: () {
                  if (isSelectingStartDate.value) {
                    selectedStartDate.value = selectedStartDate.value
                        .toUtc()
                        .copyWith(year: selectedStartDate.value.year - 1);
                  } else {
                    selectedEndDate.value = selectedEndDate.value
                        .toUtc()
                        .copyWith(year: selectedEndDate.value.year - 1);
                  }
                },
                icon: Icon(Icons.arrow_back),
              ),
              Text(
                '${isSelectingStartDate.value ? selectedStartDate.value.year : selectedEndDate.value.year}',
              ),
              IconButton(
                onPressed: () {
                  if (isSelectingStartDate.value) {
                    selectedStartDate.value = selectedStartDate.value.copyWith(
                      year: selectedStartDate.value.year + 1,
                    );
                  } else {
                    selectedEndDate.value = selectedEndDate.value.copyWith(
                      year: selectedEndDate.value.year + 1,
                    );
                  }
                },
                icon: Icon(Icons.arrow_forward),
              ),
            ],
          ),
          Space.y(8),
          Divider(height: 1),
          Space.y(16),
          GridView.count(
            shrinkWrap: true,
            crossAxisCount: 3,
            childAspectRatio: 3,
            mainAxisSpacing: 8,
            crossAxisSpacing: 8,
            children: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((e) {
              final isSelected =
                  e ==
                  (isSelectingStartDate.value
                      ? selectedStartDate.value.month
                      : selectedEndDate.value.month);

              // final isDisabled = true;

              final isDisabled = isSelectingStartDate.value
                  ? false
                  : selectedEndDate.value
                        .copyWith(month: e)
                        .isBefore(selectedStartDate.value);
              return GestureDetector(
                onTap: isDisabled
                    ? null
                    : () {
                        if (isSelectingStartDate.value) {
                          selectedStartDate.value = selectedStartDate.value
                              .toUtc()
                              .copyWith(day: 1, month: e);
                        } else {
                          selectedEndDate.value = selectedEndDate.value
                              .toUtc()
                              .copyWith(day: 1, month: e + 1)
                              .subtract(Duration(days: 1));
                        }
                      },
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 4, horizontal: 16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: isDisabled
                        ? context.colors.outlineVariant
                        : isSelected
                        ? context.colors.primary
                        : context.colors.outline,
                  ),
                  child: Center(
                    child: Text(
                      DateFormat('MMMM').format(
                        DateTime.utc(
                          isSelectingStartDate.value
                              ? selectedStartDate.value.year
                              : selectedEndDate.value.year,
                          e,
                        ),
                      ),
                      style: context.textStyles.titleMedium?.copyWith(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isDisabled
                            ? context.colors.secondary
                            : isSelected
                            ? context.colors.onPrimary
                            : context.colors.secondary,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
