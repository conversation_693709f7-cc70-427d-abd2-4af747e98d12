import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/extensions/string.extension.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/core/utils/helpers/snackbar.helper.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/employee/data/models/leave_tab.model.dart';
import 'package:hrms_tst/features/request/domain/request.controller.dart';
import 'package:hrms_tst/shared/widgets/confirmation_dialog.dart';
import 'package:intl/intl.dart';

class LeaveHistoryTile extends ConsumerWidget {
  const LeaveHistoryTile({super.key, required this.record});

  final LeaveRequestRecordModel record;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: context.colors.outlineVariant),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              children: [
                Column(
                  children: [
                    Row(
                      children: [
                        Text(
                          DateFormat(
                            'dd MMM',
                          ).format(record.startDate!.toLocal()),
                          style: context.textStyles.bodyMedium?.copyWith(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Space.x(4),
                        Text(
                          switch (record.startDaySlot) {
                            'first-half' => '(FH)',
                            'second-half' => '(SH)',
                            _ => '',
                          },
                          style: context.textStyles.bodyMedium?.copyWith(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (record.endDate!
                                .difference(record.startDate!)
                                .inDays >=
                            1) ...[
                          Space.x(4),
                          Text('-'),
                          Space.x(4),
                          Row(
                            children: [
                              Text(
                                DateFormat('dd MMM').format(record.endDate!),
                                style: context.textStyles.bodyMedium?.copyWith(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              ...[
                                Space.x(4),
                                Text(
                                  switch (record.endDaySlot) {
                                    'first-half' => '(FH)',
                                    'second-half' => '(SH)',
                                    _ => '',
                                  },
                                  style: context.textStyles.bodyMedium
                                      ?.copyWith(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ],
                    ),
                    Text(
                      record.leaveType?.name ?? '',
                      style: context.textStyles.bodyMedium?.copyWith(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),

                Spacer(),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      (record.status ?? 'Pending').capitalize(),
                      style: context.textStyles.bodyMedium?.copyWith(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: context.colors.primary,
                      ),
                    ),
                    Text(
                      record.reviewer?.displayName ?? '',
                      style: context.textStyles.bodyMedium?.copyWith(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ],
            ),

            Space.y(12),
            Row(
              spacing: 4,
              children: [
                Expanded(
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: context.colors.outline,
                    ),
                    child: Text(
                      record.reason ?? '',
                      style: context.textStyles.titleSmall?.copyWith(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: context.colors.onSurface,
                      ),
                    ),
                  ),
                ),
                if (record.status == 'pending')
                  IconButton(
                    onPressed: () {
                      showAdaptiveDialog(
                        context: context,
                        builder: (context) => ConfirmationDialog(
                          title: 'Cancel Leave Request',
                          cancelText: 'No',
                          confirmText: 'Yes',
                          onConfirm: () async {
                            final result = await ref
                                .read(leaveRequestProvider.notifier)
                                .deleteRequest(requestId: record.id);
                            result.fold(
                              onSuccess: (data) {},
                              onFailure: (message) =>
                                  context.showError(message),
                            );
                          },
                        ),
                      );
                    },
                    icon: Icon(Icons.delete, color: context.colors.error),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
