import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';

class SVGImage extends ConsumerWidget {
  const SVGImage(
    this.path, {
    super.key,
    this.height,
    this.width,
    this.color,
    this.blendMode = BlendMode.srcIn,
    this.applyColor = true,
  });

  final String path;
  final double? height;
  final double? width;
  final Color? color;
  final BlendMode blendMode;
  final bool applyColor;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SvgPicture.asset(
      path,
      height: height,
      width: width,
      colorFilter: applyColor
          ? ColorFilter.mode(color ?? context.colors.secondary, blendMode)
          : null,
    );
  }
}
