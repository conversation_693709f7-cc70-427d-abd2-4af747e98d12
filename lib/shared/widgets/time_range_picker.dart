import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:progressive_time_picker/progressive_time_picker.dart';

class TimeRangePicker extends HookConsumerWidget {
  const TimeRangePicker({
    super.key,
    required this.isStartTimeSelectable,
    required this.startTime,
    required this.endTime,
    required this.onSelectionChange,
  });

  final bool isStartTimeSelectable;
  final TimeOfDay startTime;
  final TimeOfDay endTime;
  final Function(TimeOfDay startTime, TimeOfDay endTime, bool? isValid)
  onSelectionChange;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedStartTime = useState(startTime);
    final selectedEndTime = useState(endTime);

    ClockTimeFormat clockTimeFormat = ClockTimeFormat.twentyFourHours;
    ClockIncrementTimeFormat clockIncrementTimeFormat =
        ClockIncrementTimeFormat.fiveMin;

    final currentTime = TimeOfDay.now();

    return Row(
      children: [
        Expanded(
          flex: 5,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: TimePicker(
              initTime: PickedTime(
                h: selectedStartTime.value.hour,
                m: selectedStartTime.value.minute,
              ),
              endTime: PickedTime(
                h: selectedEndTime.value.hour,
                m: selectedEndTime.value.minute,
              ),
              disabledRanges: [
                DisabledRange(
                  initTime: PickedTime(
                    h: currentTime.hour,
                    m: currentTime.minute,
                  ),
                  endTime: PickedTime(h: startTime.hour, m: startTime.minute),
                ),
              ],
              disabledRangesColor: context.colors.outlineVariant,
              disabledRangesErrorColor: context.colors.error,
              isInitHandlerSelectable: isStartTimeSelectable,
              onSelectionChange: (a, b, valid) {
                selectedStartTime.value = TimeOfDay(hour: a.h, minute: a.m);
                selectedEndTime.value = TimeOfDay(hour: b.h, minute: b.m);
                onSelectionChange(
                  selectedStartTime.value,
                  selectedEndTime.value,
                  valid,
                );
              },
              onSelectionEnd: (a, b, valid) {
                selectedStartTime.value = TimeOfDay(hour: a.h, minute: a.m);
                selectedEndTime.value = TimeOfDay(hour: b.h, minute: b.m);
                onSelectionChange(
                  selectedStartTime.value,
                  selectedEndTime.value,
                  valid,
                );
              },
              primarySectors: clockTimeFormat.value,
              secondarySectors: clockTimeFormat.value * 2,
              decoration: TimePickerDecoration(
                baseColor: context.colors.outline,
                pickerBaseCirclePadding: 15.0,
                sweepDecoration: TimePickerSweepDecoration(
                  pickerStrokeWidth: 30.0,
                  pickerColor: context.colors.primaryContainer.withValues(
                    alpha: 0.8,
                  ),
                  showConnector: true,
                ),
                initHandlerDecoration: TimePickerHandlerDecoration(
                  color: context.colors.secondary,
                  shape: BoxShape.circle,
                  radius: 10.0,
                ),
                endHandlerDecoration: TimePickerHandlerDecoration(
                  color: Color(0xFF141925),
                  shape: BoxShape.circle,
                  radius: 10.0,
                ),
                primarySectorsDecoration: TimePickerSectorDecoration(
                  color: context.colors.primary,
                  width: 1.0,
                  size: 4.0,
                  radiusPadding: 24.0,
                ),
                secondarySectorsDecoration: TimePickerSectorDecoration(
                  color: context.colors.tertiary,
                  width: 1.0,
                  size: 2.0,
                  radiusPadding: 24.0,
                ),
                clockNumberDecoration: TimePickerClockNumberDecoration(
                  defaultTextColor: context.colors.primary,
                  defaultFontSize: 6.0,
                  scaleFactor: 2.0,
                  showNumberIndicators: true,

                  clockTimeFormat: clockTimeFormat,
                  clockIncrementTimeFormat: clockIncrementTimeFormat,
                  clockIncrementHourFormat: ClockIncrementHourFormat.three,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(62.0),
                child: Center(
                  child: Builder(
                    builder: (context) {
                      final difference = getDifference(
                        selectedStartTime.value,
                        selectedEndTime.value,
                      );
                      return Text(
                        '${difference.hour.toString().padLeft(2, '0')} Hr${difference.hour > 1 ? 's' : ''}\n${difference.minute.toString().padLeft(2, '0')} Min${difference.minute > 1 ? 's' : ''}',
                        textAlign: TextAlign.center,
                        style: context.textStyles.labelSmall,
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
        ),
        Space.x(8),
        Expanded(
          flex: 3,
          child: Column(
            children: [
              _timeWidget(context, 'From', selectedStartTime.value),
              Space.y(8),
              _timeWidget(context, 'To', selectedEndTime.value),
            ],
          ),
        ),
      ],
    );
  }

  Widget _timeWidget(BuildContext context, String title, TimeOfDay time) {
    return Container(
      decoration: BoxDecoration(
        color: context.colors.outline,
        borderRadius: BorderRadius.circular(14.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              title,
              style: context.textStyles.labelLarge?.copyWith(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: context.colors.tertiary,
              ),
            ),
            Space.y(4),
            Text(
              '${time.hourOfPeriod.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')} ${time.period.name.toUpperCase()}',
              style: context.textStyles.titleLarge?.copyWith(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  TimeOfDay getDifference(TimeOfDay start, TimeOfDay end) {
    int startMins = start.hour * 60 + start.minute;
    int endMins = end.hour * 60 + end.minute;
    int diffMins = endMins - startMins;
    if (diffMins < 0) {
      diffMins += 24 * 60; // handle overnight difference
    }
    return TimeOfDay(hour: diffMins ~/ 60, minute: diffMins % 60);
  }
}
