import 'package:flutter/widgets.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/string.extension.dart';
import 'package:url_launcher/url_launcher.dart';

class HtmlView extends ConsumerWidget {
  const HtmlView({
    super.key,
    required this.htmlContent,
    this.placeholder = const SizedBox.shrink(),
  });

  final String? htmlContent;
  final Widget placeholder;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (isAvailable(htmlContent)) {
      return Html(
        data: htmlContent!.cleanExtraBRTags(),
        onLinkTap: (url, attributes, element) {
          launchUrl(Uri.parse(url!));
        },
      );
    } else {
      return placeholder;
    }
  }

  bool isAvailable(String? value) {
    return value != null &&
        value.isNotEmpty &&
        value.trim().stripHTMLTags().isNotEmpty;
  }
}
