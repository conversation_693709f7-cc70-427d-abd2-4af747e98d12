import 'package:flutter/material.dart';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';

class NoDataView extends ConsumerWidget {
  const NoDataView({
    super.key,
    this.message,
    this.iconColor,
    this.messageTextColor,
  });

  final String? message;
  final Color? iconColor;
  final Color? messageTextColor;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Space.y(16),
          SVGImage(
            Assets.svgs.noData,
            color: iconColor ?? context.colors.outline,
          ),
          Space.y(8),
          Text(
            message ?? 'No Data',
            style: context.textStyles.labelLarge?.copyWith(
              color: messageTextColor ?? context.colors.outlineVariant,
            ),
          ),
        ],
      ),
    );
  }
}
