import 'package:flutter/material.dart';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';

class FiltersPanel extends ConsumerWidget {
  const FiltersPanel({
    super.key,
    required this.filters,
    required this.trailing,
  });

  final List<Widget> filters;
  final List<Widget> trailing;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      decoration: BoxDecoration(color: context.colors.outline),
      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Row(
        children: [
          Tooltip(
            message: 'Filters',
            triggerMode: TooltipTriggerMode.tap,
            decoration: BoxDecoration(
              color: context.colors.surface,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: context.colors.secondary.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            textStyle: context.textStyles.labelMedium?.copyWith(
              color: context.colors.primary,
            ),
            child: SVGImage(
              Assets.svgs.filterPanelIcon,
              height: 24,
              width: 24,
              color: context.colors.secondary,
            ),
          ),
          Space.x(8),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(children: filters),
            ),
          ),
          Space.x(8),
          ...trailing,
        ],
      ),
    );
  }
}
