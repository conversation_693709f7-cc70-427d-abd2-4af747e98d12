import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';

class PopupFilterButton<T> extends ConsumerWidget {
  const PopupFilterButton({
    super.key,
    this.filterName,
    this.initialValue,
    required this.stringValue,
    required this.options,
    required this.onSelected,
  });

  final String? filterName;
  final T? initialValue;
  final String Function(T value) stringValue;
  final List<T> options;
  final Function(T? option) onSelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PopupMenuButton(
      initialValue: initialValue,
      itemBuilder: (context) => options.map((e) {
        return PopupMenuItem(
          height: 36,
          value: e,
          onTap: () => onSelected(e),
          padding: EdgeInsets.symmetric(horizontal: 8),
          child: Text(stringValue(e)),
        );
      }).toList(),
      menuPadding: EdgeInsets.zero,
      padding: EdgeInsetsGeometry.zero,
      borderRadius: BorderRadius.circular(14),
      elevation: 1,
      surfaceTintColor: context.colors.surface,
      color: context.colors.surface,
      offset: Offset(0, 32),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 400),
        curve: Curves.ease,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: initialValue != null
              ? context.colors.primary
              : context.colors.surface,
        ),
        child: IntrinsicHeight(
          child: Row(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
                child: Text(
                  initialValue != null
                      ? stringValue(initialValue as T)
                      : filterName!,
                  style: context.textStyles.titleMedium?.copyWith(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: initialValue != null
                        ? context.colors.onPrimary
                        : context.colors.secondary,
                  ),
                ),
              ),
              if (initialValue != null) ...[
                VerticalDivider(color: context.colors.outline, width: 1),
                GestureDetector(
                  onTap: () {
                    onSelected(null);
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 2.0),
                    child: Icon(
                      Icons.close,
                      color: context.colors.onPrimary,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
