import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';

class LoadingPlaceholder extends ConsumerWidget {
  const LoadingPlaceholder({
    super.key,
    this.height,
    this.width,
    this.padding,
    this.decoration,
    this.borderRadius,
    this.color,
    this.child,
  });

  final double? height;
  final double? width;
  final EdgeInsetsGeometry? padding;
  final Decoration? decoration;
  final BorderRadius? borderRadius;
  final Color? color;
  final Widget? child;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      height: height,
      width: width,
      padding: padding,
      decoration:
          decoration ??
          (borderRadius != null
              ? BoxDecoration(
                  borderRadius: borderRadius,
                  color: color ?? context.colors.outline,
                )
              : BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: color ?? context.colors.outline,
                )),
      child: child,
    );
  }
}
