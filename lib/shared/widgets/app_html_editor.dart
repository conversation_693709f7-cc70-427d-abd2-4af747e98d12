import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_quill_delta_from_html/flutter_quill_delta_from_html.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/extensions/string.extension.dart';
import 'package:vsc_quill_delta_to_html/vsc_quill_delta_to_html.dart';

class AppHTMLEditor extends HookConsumerWidget {
  final FormFieldState? fieldState;
  final String? initialValue;
  final ValueChanged<String>? onChanged;
  final Function()? onFocus;
  final double? height;
  final int? characterLimit;
  final Color? backgroundColor;

  const AppHTMLEditor({
    super.key,
    this.fieldState,
    this.initialValue,
    this.onChanged,
    this.onFocus,
    this.height,
    this.characterLimit,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = useState(QuillController.basic());

    final scrollController = useScrollController();

    final focusNode = useFocusNode();

    final isContextMenuVisible = useState(false);

    editorControllerListener() {
      if (onChanged != null) {
        List deltaJson = controller.value.document.toDelta().toJson();
        QuillDeltaToHtmlConverter converter = QuillDeltaToHtmlConverter(
          List.castFrom(deltaJson),
        );
        final text = converter.convert();
        log(text);
        onChanged?.call(webCompatible(text));
      }
    }

    useEffect(() {
      focusNode.addListener(() {
        if (focusNode.hasFocus) {
          onFocus?.call();
        } else {
          // Reset context menu visibility when focus is lost
          isContextMenuVisible.value = false;
        }
      });

      if (initialValue != null && initialValue!.isNotEmpty) {
        try {
          final delta = HtmlToDelta().convert(initialValue!.cleanExtraBRTags());
          final document = Document.fromDelta(delta);
          controller.value = QuillController(
            document: document,
            selection: const TextSelection.collapsed(offset: 0),
          );
        } catch (e) {
          log('Error converting initial value to Delta: $e');
        }
      }

      controller.value.addListener(editorControllerListener);

      return () {
        controller.value.removeListener(editorControllerListener);
        controller.value.dispose();
      };
    }, []);

    final defaultTextStyle = DefaultTextStyle.of(context);
    final baseStyle = defaultTextStyle.style.copyWith(
      fontSize: 16,
      height: 1.15,
      decoration: TextDecoration.none,
    );
    const baseHorizontalSpacing = HorizontalSpacing(0, 0);
    const baseVerticalSpacing = VerticalSpacing(6, 0);

    return TapRegion(
      groupId: 'editor',
      onTapOutside: (event) {
        if (!focusNode.hasFocus) return;

        // Don't unfocus if there's an active text selection (context menu might be showing)
        // or if the selection is being modified
        if (!controller.value.selection.isCollapsed) {
          // Add a small delay to allow anchor interactions
          Future.delayed(const Duration(milliseconds: 150), () {
            if (controller.value.selection.isCollapsed) {
              focusNode.unfocus();
            }
          });
          return;
        }

        focusNode.unfocus();
      },
      child: Container(
        height: height ?? context.screenHeight * 0.36,
        width: double.infinity,
        decoration: BoxDecoration(
          color: backgroundColor ?? context.colors.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: (fieldState?.hasError ?? false)
                ? context.colors.error
                : context.appTextFieldBorderColor,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: QuillEditor(
                focusNode: focusNode,
                controller: controller.value,
                scrollController: scrollController,
                config: QuillEditorConfig(
                  padding: EdgeInsets.all(8),
                  expands: true,
                  enableSelectionToolbar: true,
                  enableInteractiveSelection: true,
                  showCursor: true,
                  contextMenuBuilder: (context, rawEditorState) {
                    // Set context menu visibility
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      isContextMenuVisible.value =
                          rawEditorState.contextMenuButtonItems.isNotEmpty;
                    });

                    return TapRegion(
                      groupId: 'editor',
                      child: AdaptiveTextSelectionToolbar.buttonItems(
                        buttonItems: rawEditorState.contextMenuButtonItems,
                        anchors: rawEditorState.contextMenuAnchors,
                      ),
                    );
                  },
                  customStyles: DefaultStyles(
                    lists: DefaultListBlockStyle(
                      baseStyle,
                      baseHorizontalSpacing,
                      baseVerticalSpacing.copyWith(bottom: 12),
                      const VerticalSpacing(0, 6),
                      null,
                      null,
                    ),
                  ),
                ),
              ),
            ),
            IntrinsicHeight(
              child: Row(
                children: [
                  _toolbarButton(
                    context: context,
                    controller: controller.value,
                    icon: Icons.format_bold_rounded,
                    attribute: Attribute.bold,
                  ),
                  _toolbarButton(
                    context: context,
                    controller: controller.value,
                    icon: Icons.format_italic_rounded,
                    attribute: Attribute.italic,
                  ),
                  _toolbarButton(
                    context: context,
                    controller: controller.value,
                    icon: Icons.format_underline_rounded,
                    attribute: Attribute.underline,
                  ),
                  _toolbarButton(
                    context: context,
                    controller: controller.value,
                    icon: Icons.format_strikethrough_rounded,
                    attribute: Attribute.strikeThrough,
                  ),
                  VerticalDivider(
                    indent: 10,
                    endIndent: 10,
                    color: context.colors.secondary,
                  ),
                  _toolbarButton(
                    context: context,
                    controller: controller.value,
                    icon: Icons.format_list_bulleted_rounded,
                    attribute: Attribute.ul,
                  ),
                  _toolbarButton(
                    context: context,
                    controller: controller.value,
                    icon: Icons.format_list_numbered_rounded,
                    attribute: Attribute.ol,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _toolbarButton({
    required BuildContext context,
    required QuillController controller,
    required IconData icon,
    required Attribute attribute,
  }) {
    return QuillToolbarToggleStyleButton(
      controller: controller,
      attribute: attribute,
    );
  }

  String webCompatible(String html) {
    // Convert <br> tags to <br/> for web compatibility
    return html.replaceAll(
      RegExp(r'<br/>', caseSensitive: false),
      '<p><br></p>',
    );
  }
}
