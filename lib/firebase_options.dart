// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCZX9TvIrG0Mbwft2Ogn1dfc11kTrocnx8',
    appId: '1:690147380218:web:db0a550cee38a088427243',
    messagingSenderId: '690147380218',
    projectId: 'tst-hrms-c8f00',
    authDomain: 'tst-hrms-c8f00.firebaseapp.com',
    storageBucket: 'tst-hrms-c8f00.firebasestorage.app',
    measurementId: 'G-R87LJNP6Q6',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAQWUSq0nZAgTY60YI_5wJLmuWFwjG-TSA',
    appId: '1:690147380218:android:7734b52fa440f48a427243',
    messagingSenderId: '690147380218',
    projectId: 'tst-hrms-c8f00',
    storageBucket: 'tst-hrms-c8f00.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBXgMhZ8JLUk97xdh5oj1UrJUFcTDWQHxM',
    appId: '1:690147380218:ios:ed90d79c6582a45e427243',
    messagingSenderId: '690147380218',
    projectId: 'tst-hrms-c8f00',
    storageBucket: 'tst-hrms-c8f00.firebasestorage.app',
    androidClientId: '690147380218-527qbvdss6s7t5ek2v28lk4a8jrcfmu7.apps.googleusercontent.com',
    iosClientId: '690147380218-5mjlkegts4biae24327sfg6jo1kgsos6.apps.googleusercontent.com',
    iosBundleId: 'com.tsttechnology.hrms',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBXgMhZ8JLUk97xdh5oj1UrJUFcTDWQHxM',
    appId: '1:690147380218:ios:022e72286fc6e065427243',
    messagingSenderId: '690147380218',
    projectId: 'tst-hrms-c8f00',
    storageBucket: 'tst-hrms-c8f00.firebasestorage.app',
    androidClientId: '690147380218-527qbvdss6s7t5ek2v28lk4a8jrcfmu7.apps.googleusercontent.com',
    iosClientId: '690147380218-gu01u1315vp255s4sgtfhetud7ta9hv1.apps.googleusercontent.com',
    iosBundleId: 'com.tst.hrmsTst',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCZX9TvIrG0Mbwft2Ogn1dfc11kTrocnx8',
    appId: '1:690147380218:web:06151086bb217967427243',
    messagingSenderId: '690147380218',
    projectId: 'tst-hrms-c8f00',
    authDomain: 'tst-hrms-c8f00.firebaseapp.com',
    storageBucket: 'tst-hrms-c8f00.firebasestorage.app',
    measurementId: 'G-W5T3C6FW5X',
  );

}