import 'dart:async';
import 'dart:developer';

import 'package:face_camera/face_camera.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/firebase_options.dart';
import 'app.dart';
import 'core/config/environment.dart';
import 'dependencies.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await FaceCamera.initialize();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Do not forget to change geofence callback environment if you change here
  getIt.registerSingleton<Environment>(Environment.dev);

  await setupDependencies();

  FlutterError.onError = (details) {
    log(details.exceptionAsString(), stackTrace: details.stack);
  };

  runApp(ProviderScope(child: const App()));
}
