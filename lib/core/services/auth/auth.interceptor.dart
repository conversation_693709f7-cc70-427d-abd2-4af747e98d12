import 'dart:developer';

import 'package:dio/dio.dart';

import '../network/network.service.dart';
import 'protocol/auth.protocol.dart';
import 'protocol/token.protocol.dart';

class AuthInterceptor extends InterceptorsWrapper {
  final NetworkService networkService;
  final AuthProtocol authService;
  final TokenProtocol tokenService;

  AuthInterceptor({
    required this.networkService,
    required this.authService,
    required this.tokenService,
  });

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    log('AuthInterceptor: Auth Error: ${err.response?.statusCode}');
    handler.next(err);
    // if (err.response?.statusCode == HttpStatus.unauthorized) {
    //   final result = await authService.refresh();
    //   await result.fold(
    //     onSuccess: (data) async {
    //       await tokenService.setAccessToken(data.access);
    //       await tokenService.setRefreshToken(data.refresh);
    //       _retry(err.requestOptions);
    //     },
    //     onFailure: (message) {
    //       log('AuthInterceptor: Refresh Token Failed: $message');
    //       handler.next(err);
    //     },
    //   );
    // } else {
    //   handler.next(err);
    // }
  }

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    final token = await tokenService.accessToken;
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }

  // Future<Response<dynamic>> _retry(RequestOptions requestOptions) async {
  //   final result = await getIt.get<NetworkService>().request<dynamic>(
  //     requestOptions.path,
  //     data: requestOptions.data,
  //     queryParameters: requestOptions.queryParameters,
  //     options: Options(
  //       method: requestOptions.method,
  //       headers: requestOptions.headers,
  //     ),
  //     mapper: (response) => response,
  //   );

  //   return result.ignoreFailure();
  // }
}
