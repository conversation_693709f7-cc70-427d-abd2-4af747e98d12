import 'package:hrms_tst/core/services/auth/protocol/token.protocol.dart';
import 'package:hrms_tst/core/services/prefs/prefs.protocol.dart';

class TokenService implements TokenProtocol {
  final PrefsProtocol _prefs;

  TokenService({required PrefsProtocol prefs}) : _prefs = prefs;

  @override
  Future<String?> get accessToken => _prefs.get<String>('access_token', null);

  @override
  Future<String?> get refreshToken => _prefs.get<String>('refresh_token', null);

  @override
  Future<void> clear() async {
    await _prefs.remove('access_token');
  }

  @override
  Future<void> setAccessToken(String? token) async {
    await _prefs.save<String?>('access_token', token);
  }

  @override
  Future<void> setRefreshToken(String? token) async {
    await _prefs.save<String?>('refresh_token', token);
  }

  @override
  Future<void> init() async {}
}
