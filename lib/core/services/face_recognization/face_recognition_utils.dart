import 'dart:math';
import 'dart:typed_data';
import 'package:image/image.dart' as img;

abstract class FaceRecognizationUtils {
  static Float32List imageToByteListFloat32(img.Image image) {
    final Float32List convertedBytes = Float32List(112 * 112 * 3);
    final buffer = Float32List.view(convertedBytes.buffer);
    int pixelIndex = 0;

    for (int y = 0; y < 112; y++) {
      for (int x = 0; x < 112; x++) {
        final pixel = image.getPixel(x, y);
        buffer[pixelIndex++] = ((pixel.r / 255.0) - 0.5) * 2; // [-1, 1]
        buffer[pixelIndex++] = ((pixel.g / 255.0) - 0.5) * 2;
        buffer[pixelIndex++] = ((pixel.b / 255.0) - 0.5) * 2;
      }
    }

    return convertedBytes;
  }

  static bool hasSimiliarity({
    required List<double> a,
    required List<double> b,
    double euclideanThreshold = 1.0,
    double cosineThreshold = 0.9,
  }) {
    final euc = _euclideanDistance(a, b);
    final cos = _cosineSimilarity(a, b);
    return euc < euclideanThreshold && cos > cosineThreshold;
  }

  static double _euclideanDistance(List e1, List e2) {
    double sum = 0.0;
    for (int i = 0; i < e1.length; i++) {
      sum += pow((e1[i] - e2[i]), 2);
    }
    return sqrt(sum);
  }

  static double _cosineSimilarity(List<double> a, List<double> b) {
    double dot = 0, normA = 0, normB = 0;
    for (int i = 0; i < a.length; i++) {
      dot += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }
    return dot / (sqrt(normA) * sqrt(normB));
  }
}
