import 'dart:math';
import 'dart:typed_data';
import 'package:face_camera/face_camera.dart';
import 'package:hrms_tst/core/services/face_recognization/face_recognition_utils.dart';
import 'package:image/image.dart' as img;

import 'package:tflite_flutter/tflite_flutter.dart';

class FaceRecognizationService {
  late final Interpreter _interpreter;

  Future<void> loadModel() async {
    try {
      _interpreter = await Interpreter.fromAsset(
        'assets/ml-model/mobilefacenet.tflite',
      );
    } catch (e) {
      throw Exception('Failed to load model: $e');
    }
  }

  Future<void> dispose() async {
    if (_interpreter.isDeleted) return;
    _interpreter.close();
  }

  Future<List<double>> getEmbedding(img.Image image, Face face) async {
    final input = _imageToInput(image, face);
    final output = List.filled(192, 0.0).reshape([1, 192]);

    _interpreter.run(input.reshape([1, 112, 112, 3]), output);
    return _l2Normalize(List<double>.from(output.reshape([192])));
  }

  Float32List _imageToInput(img.Image image, Face face) {
    final croppedImage = cropFace(image, face);
    final formattedImage = img.copyResizeCropSquare(croppedImage, size: 112);

    // transforms the cropped face to array data
    Float32List imageAsList = FaceRecognizationUtils.imageToByteListFloat32(
      formattedImage,
    );
    return imageAsList;
  }

  img.Image cropFace(img.Image image, Face detectedFace) {
    final padding = 24;
    double x = detectedFace.boundingBox.left + padding;
    double y = detectedFace.boundingBox.top + padding;
    double w = detectedFace.boundingBox.width - padding;
    double h = detectedFace.boundingBox.height - padding;
    return img.copyCrop(
      image,
      x: x.round(),
      y: y.round(),
      width: w.round(),
      height: h.round(),
    );
  }

  List<double> _l2Normalize(List<double> vector) {
    final double norm = sqrt(vector.fold(0.0, (sum, val) => sum + val * val));
    return vector.map((e) => e / norm).toList();
  }
}
