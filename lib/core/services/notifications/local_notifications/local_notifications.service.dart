import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../../permissions/permissions.protocol.dart';

import 'local_notifications.protocol.dart';

class LocalNotificationsService implements LocalNotificationsProtocol {
  LocalNotificationsService({
    required FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin,
    required PermissionsProtocol permissions,
  }) : _flutterLocalNotificationsPlugin = flutterLocalNotificationsPlugin,
       _permissions = permissions;

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin;
  final PermissionsProtocol _permissions;

  final AndroidNotificationChannel _channel = const AndroidNotificationChannel(
    'high_importance_channel',
    'High Importance Notifications',
    description: 'This channel is used for important notifications.',
    importance: Importance.max,
  );

  @override
  Future<void> init() async {
    final result = await _permissions.requestNotificationsPermission();

    if (result) {
      await _initLocalNotifications();
    }
  }

  Future<void> _initLocalNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings("@mipmap/ic_launcher");

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
          requestSoundPermission: true,
          requestBadgePermission: true,
          requestAlertPermission: true,
          defaultPresentAlert: true,
          defaultPresentSound: true,
        );

    const InitializationSettings initializationSettings =
        InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsIOS,
        );

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (details) async =>
          await _handleNotificationClick(jsonDecode(details.payload ?? '{}')),
    );

    if (Platform.isAndroid) {
      await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin
          >()
          ?.createNotificationChannel(_channel);
    }

    if (Platform.isIOS) {
      await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin
          >()
          ?.requestPermissions(alert: true, badge: true, sound: true);
    }
  }

  @override
  Future<void> showNotification({
    int? id,
    required String title,
    required String body,
    Map<String, dynamic>? payload,
  }) async {
    final AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
          _channel.id,
          _channel.name,
          channelDescription: _channel.description,
          icon: '@mipmap/ic_launcher',
        );

    final DarwinNotificationDetails iOSNotificationDetails =
        const DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
          sound: 'default',
        );

    final NotificationDetails notificationDetails = NotificationDetails(
      android: androidNotificationDetails,
      iOS: iOSNotificationDetails,
    );

    await _flutterLocalNotificationsPlugin.show(
      0,
      title,
      body,
      notificationDetails,
      payload: jsonEncode(payload),
    );
  }

  Future<void> _handleNotificationClick(Map<String, dynamic> payload) async {
    // Handle the notification click here
    // For example, you can navigate to a specific screen based on the payload
    // or perform some action based on the notification data.
    log('Notification clicked with payload: $payload');
  }
}
