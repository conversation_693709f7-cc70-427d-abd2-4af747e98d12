import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:hrms_tst/core/config/environment.dart';

import '../../utils/helpers/result.dart';

class NetworkService {
  final Dio _dio;

  NetworkService({required Environment env, required Dio dio}) : _dio = dio {
    log('baseUrl: ${env.baseUrl}');
    _dio.options.baseUrl = env.baseUrl;
    _dio.options.headers.addEntries([
      const MapEntry('Content-Type', 'application/json'),
    ]);
  }

  Future<void> addInterceptor(
    Interceptor Function(NetworkService networkService) interceptor,
  ) async {
    _dio.interceptors.add(interceptor(this));
  }

  Future<Result<R>> get<R>(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onReceiveProgress,
    required R Function(Response response) mapper,
  }) async {
    return await _dio
        .get(
          path,
          data: data,
          queryParameters: queryParameters,
          options: options,
          cancelToken: cancelToken,
          onReceiveProgress: onReceiveProgress,
        )
        .mapToResult(mapper);
  }

  Future<Result<R>> post<R>(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    void Function(int, int)? onReceiveProgress,
    required R Function(Response response) mapper,
  }) async {
    return await _dio
        .post(
          path,
          data: data,
          queryParameters: queryParameters,
          options: options,
          cancelToken: cancelToken,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress,
        )
        .mapToResult(mapper);
  }

  Future<Result<R>> put<R>(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    void Function(int, int)? onReceiveProgress,
    required R Function(Response response) mapper,
  }) async {
    return await _dio
        .put(
          path,
          data: data,
          queryParameters: queryParameters,
          options: options,
          cancelToken: cancelToken,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress,
        )
        .mapToResult(mapper);
  }

  Future<Result<R>> patch<R>(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    void Function(int, int)? onReceiveProgress,
    required R Function(Response response) mapper,
  }) async {
    return await _dio
        .patch(
          path,
          data: data,
          queryParameters: queryParameters,
          options: options,
          cancelToken: cancelToken,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress,
        )
        .mapToResult(mapper);
  }

  Future<Result<R>> delete<R>(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    required R Function(Response response) mapper,
  }) async {
    return await _dio
        .delete(
          path,
          data: data,
          queryParameters: queryParameters,
          options: options,
          cancelToken: cancelToken,
        )
        .mapToResult(mapper);
  }

  Future<Result<R>> request<R>(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    void Function(int, int)? onReceiveProgress,
    required R Function(Response response) mapper,
  }) async {
    return await _dio
        .request(
          path,
          data: data,
          queryParameters: queryParameters,
          options: options,
          cancelToken: cancelToken,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress,
        )
        .mapToResult(mapper);
  }
}
