import 'dart:io';

import 'package:image_picker/image_picker.dart';

import '../permissions/permissions.protocol.dart';
import 'media_picker.protocol.dart';

class MediaPickerService implements MediaPickerProtocol {
  MediaPickerService({required this.permissions});

  final _picker = ImagePicker();
  final PermissionsProtocol permissions;

  @override
  Future<File?> pickImage({
    required MediaSource source,
    int qualityPercent = 50,
    required Function onCancelled,
  }) async {
    final permission = await permissions.requestImagePermission();

    if (permission) {
      final pickedFile = await _picker.pickImage(
        source: source,
        imageQuality: qualityPercent,
      );

      if (pickedFile != null) {
        return File(pickedFile.path);
      } else {
        onCancelled();
        return null;
      }
    }
    return null;
  }

  @override
  Future<List<File>?> pickMultipleImages({
    required MediaSource source,
    int? maxCount,
    int qualityPercent = 50,
    required Function onCancelled,
  }) async {
    // final permission = await permissions.requestImagePermission();

    // if (permission) {
    final pickedFiles = await _picker.pickMultiImage(
      limit: maxCount,
      imageQuality: qualityPercent,
    );

    if (pickedFiles.isNotEmpty) {
      return pickedFiles.map((e) => File(e.path)).toList();
    } else {
      onCancelled();
      return null;
    }
    // }
    // return null;
  }
}
