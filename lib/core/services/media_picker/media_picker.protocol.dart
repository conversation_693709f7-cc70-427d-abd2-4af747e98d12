import 'dart:io';

import 'package:image_picker/image_picker.dart';

abstract interface class MediaPickerProtocol {
  Future<File?> pickImage({
    required MediaSource source,
    int qualityPercent = 50,
    required Function onCancelled,
  });

  Future<List<File>?> pickMultipleImages({
    required MediaSource source,
    int? maxCount,
    int qualityPercent = 50,
    required Function onCancelled,
  });
}

typedef MediaSource = ImageSource;
