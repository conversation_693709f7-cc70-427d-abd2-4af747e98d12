import 'dart:developer';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';

import '../device_info/device_info.service.dart';
import 'permissions.protocol.dart';

class PermissionsService implements PermissionsProtocol {
  PermissionsService({required DeviceInfoService deviceInfoService})
    : _deviceInfo = deviceInfoService.plugin;

  final DeviceInfoPlugin _deviceInfo;

  @override
  Future<bool> requestNotificationsPermission({
    Function? onDenied,
    Function? onPermanentlyDenied,
  }) {
    return _requestPermission(
      permission: Permission.notification,
      onDenied: onDenied,
      onPermanentlyDenied: onPermanentlyDenied,
    );
  }

  @override
  Future<bool> requestImagePermission({
    Function? onDenied,
    Function? onPermanentlyDenied,
  }) async {
    final permission = Platform.isAndroid
        ? (await _deviceInfo.androidInfo).version.sdkInt >= 33
              ? Permission.photos
              : Permission.storage
        : Permission.photos;

    return await _requestPermission(
      permission: permission,
      onDenied: onDenied,
      onPermanentlyDenied: onPermanentlyDenied,
    );
  }

  @override
  Future<bool> requestGeofencePermission({
    Function? onLocationDenied,
    Function? onLocationAlwaysDenied,
  }) async {
    // First check and request basic location permission
    final locationStatus = await Permission.location.status;

    if (locationStatus.isDenied) {
      final requestResult = await Permission.location.request();
      if (requestResult.isDenied) {
        onLocationDenied?.call();
        return false;
      } else if (requestResult.isPermanentlyDenied) {
        onLocationDenied?.call();
        return false;
      }
    } else if (locationStatus.isPermanentlyDenied) {
      onLocationDenied?.call();
      return false;
    }

    // Now check and request "always" location permission for geofencing
    final locationAlwaysStatus = await Permission.locationAlways.status;

    if (locationAlwaysStatus.isDenied) {
      final alwaysRequestResult = await Permission.locationAlways.request();
      if (alwaysRequestResult.isDenied) {
        onLocationAlwaysDenied?.call();
        return false;
      } else if (alwaysRequestResult.isPermanentlyDenied) {
        onLocationAlwaysDenied?.call();
        return false;
      }
    } else if (locationAlwaysStatus.isPermanentlyDenied) {
      onLocationAlwaysDenied?.call();
      return false;
    }

    // Both permissions are granted
    return true;
  }

  Future<bool> _requestPermission({
    required Permission permission,
    Function? onDenied,
    Function? onPermanentlyDenied,
  }) async {
    final status = await permission.request();

    log('Permission ${permission.toString()} status: $status');

    if (!status.isGranted) {
      if (status.isPermanentlyDenied) {
        onPermanentlyDenied?.call();
        return false;
      } else {
        onDenied?.call();
        return false;
      }
    } else {
      return true;
    }
  }
}
