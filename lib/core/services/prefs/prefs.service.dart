import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';
import 'prefs.protocol.dart';

class PrefsService implements PrefsProtocol {
  PrefsService({required SharedPreferences prefs}) : _prefs = prefs;

  final SharedPreferences _prefs;

  @override
  Future<void> save<T>(String key, T value) async {
    return switch (value) {
      String() => await () async {
        await _prefs.setString(key, value);
      }(),
      int() => await () async {
        await _prefs.setInt(key, value);
      }(),
      double() => await () async {
        await _prefs.setDouble(key, value);
      }(),
      bool() => await () async {
        await _prefs.setBool(key, value);
      }(),
      List<String>() => await () async {
        await _prefs.setStringList(key, value);
      }(),
      _ => await () async {
        await _prefs.setString(key, jsonEncode(value));
      }(),
    };
  }

  @override
  Future<T?> get<T>(String key, [T? defaultValue]) async {
    if (T == String) {
      return (_prefs.getString(key) as T?) ?? defaultValue;
    } else if (T == int) {
      return (_prefs.getInt(key) as T?) ?? defaultValue;
    } else if (T == double) {
      return (_prefs.getDouble(key) as T?) ?? defaultValue;
    } else if (T == bool) {
      return (_prefs.getBool(key) as T?) ?? defaultValue;
    } else if (T == List<String>) {
      return (_prefs.getStringList(key) as T?) ?? defaultValue;
    } else {
      try {
        return jsonDecode(_prefs.getString(key) ?? '') as T? ?? defaultValue;
      } catch (e) {
        return null;
      }
    }
  }

  @override
  Future<void> remove(String key) async {
    await _prefs.remove(key);
  }

  @override
  Future<void> clear() async {
    await _prefs.clear();
  }
}
