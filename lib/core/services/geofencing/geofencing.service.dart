import 'dart:convert';
import 'dart:async';
import 'dart:developer';
import 'dart:isolate';
import 'dart:ui';

import 'package:dio/dio.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:hrms_tst/core/config/environment.dart';
import 'package:hrms_tst/core/services/auth/token.service.dart';
import 'package:hrms_tst/core/services/network/network.service.dart';
import 'package:hrms_tst/core/services/permissions/permissions.protocol.dart';
import 'package:hrms_tst/core/services/prefs/prefs.service.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:native_geofence/native_geofence.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AutoClockInUIMessage {
  final bool isSuccess;
  final String message;

  AutoClockInUIMessage({required this.isSuccess, required this.message});

  Map<String, dynamic> to<PERSON><PERSON>() {
    return {'isSuccess': isSuccess, 'message': message};
  }

  factory AutoClockInUIMessage.fromJson(Map<String, dynamic> json) {
    return AutoClockInUIMessage(
      isSuccess: json['isSuccess'] as bool,
      message: json['message'] as String,
    );
  }
}

@pragma('vm:entry-point')
Future<void> geofenceTriggered(GeofenceCallbackParams params) async {
  log('Geofence triggered : ${params.toString()}');

  final env = Environment.dev;

  final sharedPrefs = await SharedPreferences.getInstance();
  final prefsService = PrefsService(prefs: sharedPrefs);
  final tokenService = TokenService(prefs: prefsService);
  final accessToken = await tokenService.accessToken;

  final SendPort? send = IsolateNameServer.lookupPortByName(
    'com.tsttechnology.hrms:auto_clock_in_port',
  );

  if (send == null) {
    log('SendPort not found, cannot send message to main isolate');
  }

  log('Token Obtained from Local Storage: $accessToken');

  if (accessToken == null) {
    log('Authentication token is null, cannot proceed with geofence action');
  }

  final networkService = NetworkService(dio: Dio(), env: env);

  final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
  await setupLocalNotificationForIsolate(flutterLocalNotificationsPlugin);

  if (params.event == GeofenceEvent.enter) {
    log('User Entered Office Location Trigger Clock In');

    final geofence = params.geofences.first;
    final result = await networkService.post(
      '/attendances/auto-clock-in',
      options: Options(
        headers: {'Authorization': 'Bearer ${await tokenService.accessToken}'},
      ),
      data: {
        "lat": params.location?.latitude ?? geofence.location.latitude,
        "long": params.location?.longitude ?? geofence.location.longitude,
      },
      mapper: (response) {
        return;
      },
    );

    await result.fold(
      onSuccess: (onSuccess) async {},
      onFailure: (message) async {},
    );

    if (result.isSuccess) {
      log('Clock In Success');

      send?.send(
        AutoClockInUIMessage(
          isSuccess: true,
          message: 'Clocked In Automatically',
        ).toJson(),
      );

      await showLocalNotificationFromIsolate(
        flutterLocalNotificationsPlugin,
        0,
        'Auto Clock In Successfull',
        'You have ${params.event.name} the office location', // e.g., "entered" or "exited"
        null,
      );
    } else {
      log('Clock In Failed: ${result.failure.message}');

      send?.send(
        AutoClockInUIMessage(
          isSuccess: false,
          message: 'Unable to auto-clock in',
        ).toJson(),
      );
    }
  } else {
    log('User Exited Office Location');
  }
}

class GeofencingService {
  final permissionService = getIt.get<PermissionsProtocol>();

  final _manager = NativeGeofenceManager.instance;
  final ReceivePort _receivePort = ReceivePort();

  final _eventStreamController =
      StreamController<AutoClockInUIMessage>.broadcast();

  Stream<AutoClockInUIMessage> get eventStream => _eventStreamController.stream;

  GeofencingService() {
    IsolateNameServer.registerPortWithName(
      _receivePort.sendPort,
      'com.tsttechnology.hrms:auto_clock_in_port',
    );

    _receivePort.listen(
      (message) =>
          _eventStreamController.add(AutoClockInUIMessage.fromJson(message)),
    );
  }

  Future<Result<void>> init({required Function(String message) onError}) async {
    try {
      final available = await permissionService.requestGeofencePermission(
        onLocationDenied: () {
          onError(
            'Location permission is required for automatic clock in. Please grant location access in settings.',
          );
        },
        onLocationAlwaysDenied: () {
          onError(
            'Background location permission is required for automatic clock in. Please enable "Allow all the time" in location settings.',
          );
        },
      );

      if (!available) {
        return Failure(
          'Unable to initialize geofencing - permissions not granted',
        );
      }

      await _manager.initialize();

      log('Geofencing service initialized successfully');

      return Success(null);
    } catch (e) {
      final errorMessage =
          'Failed to initialize geofencing service: ${e.toString()}';
      onError(errorMessage);
      return Failure(errorMessage);
    }
  }

  Future<Result<List<ActiveGeofence>>> getGeofences() async {
    return await _manager.getRegisteredGeofences().asResult();
  }

  Future<Result<void>> reCreateAfterReboot() async {
    return await _manager.reCreateAfterReboot().asResult();
  }

  Future<Result<void>> addGeofence({
    required String id,
    required double latitude,
    required double longitude,
    required double radiusInMeters,
  }) async {
    log('Add geofence : $id');
    return await _manager
        .createGeofence(
          Geofence(
            id: id,
            location: Location(latitude: latitude, longitude: longitude),
            radiusMeters: radiusInMeters,
            triggers: {GeofenceEvent.enter},
            iosSettings: IosGeofenceSettings(),
            androidSettings: AndroidGeofenceSettings(
              initialTriggers: {GeofenceEvent.enter},
            ),
          ),
          geofenceTriggered,
        )
        .asResult();
  }

  Future<Result<void>> removeGeofence({required String id}) async {
    return await _manager.removeGeofenceById(id).asResult();
  }

  Future<Result<void>> removeAllGeofences() async {
    return await _manager.removeAllGeofences().asResult();
  }
}

Future<void> showLocalNotificationFromIsolate(
  FlutterLocalNotificationsPlugin plugin,
  int id,
  String title,
  String body,
  Map<String, dynamic>? payload,
) async {
  const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
    'geofence_channel',
    'Geofence Notifications',
    importance: Importance.max,
    priority: Priority.high,
  );

  const NotificationDetails notificationDetails = NotificationDetails(
    android: androidDetails,
    iOS: DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    ),
  );

  await plugin.show(
    id,
    title,
    body, // e.g., "entered" or "exited"
    payload: jsonEncode(payload),
    notificationDetails,
  );
}

Future<void> setupLocalNotificationForIsolate(
  FlutterLocalNotificationsPlugin plugin,
) async {
  await plugin
      .resolvePlatformSpecificImplementation<
        IOSFlutterLocalNotificationsPlugin
      >()
      ?.requestPermissions(alert: true, badge: true, sound: true);

  const AndroidInitializationSettings androidSettings =
      AndroidInitializationSettings('@mipmap/ic_launcher');

  const InitializationSettings settings = InitializationSettings(
    android: androidSettings,
    iOS: DarwinInitializationSettings(),
  );

  await plugin.initialize(settings);
}
