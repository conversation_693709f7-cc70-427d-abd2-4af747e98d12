extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }

  String stripHTMLTags() => replaceAll(RegExp(r'<[^>]*>'), '');

  String cleanExtraBRTags() {
    String html = replaceAllMapped(
      RegExp(
        r'</h[1-6]>\s*<p>\s*(<br\s*/?>\s*){1}\s*</p>\s*<h[1-6]>',
        caseSensitive: false,
      ),
      (match) {
        return match
            .group(0)!
            .replaceAll(
              RegExp(r'<p>\s*(<br\s*/?>\s*){1}\s*</p>', caseSensitive: false),
              '',
            );
      },
    );

    html = html.replaceFirst(
      RegExp(r'(<br\s*/?>\s*)+(</?p>\s*)*$', caseSensitive: false),
      '',
    );

    html = html.replaceAllMapped(
      RegExp(r'<(h[1-6])>(.*?)<\/\1>', caseSensitive: false, dotAll: true),
      (match) {
        final tag = match.group(1)!;
        var content = match.group(2)!;
        content = content.replaceFirst(
          RegExp(r'^(\s*<br\s*/?>\s*)+', caseSensitive: false),
          '',
        );
        content = content.replaceFirst(
          RegExp(r'(<br\s*/?>\s*)+$', caseSensitive: false),
          '',
        );
        return '<$tag>$content</$tag>';
      },
    );

    html = html.replaceAllMapped(
      RegExp(r'([^>])(<h[1-6]>)', caseSensitive: false),
      (match) => '${match.group(1)}<br>${match.group(2)}',
    );

    html = html.replaceAll(
      RegExp(r'<p><br></p>', caseSensitive: false),
      '<br/>',
    );

    return html;
  }
}
