import 'package:flutter/material.dart';

extension ContextExtension on BuildContext {
  ColorScheme get colors => Theme.of(this).colorScheme;
  TextTheme get textStyles => Theme.of(this).textTheme;

  Size get screenSize => MediaQuery.sizeOf(this);
  double get screenHeight => screenSize.height;
  double get screenWidth => screenSize.width;

  bool get isDarkMode => Theme.brightnessOf(this) == Brightness.dark;
}

extension CommonColors on BuildContext {
  Color get appTextFieldFillColor =>
      isDarkMode ? const Color(0xFF273437) : const Color(0xFFF5F5F5);

  Color get appTextFieldBorderColor =>
      isDarkMode ? const Color(0xFF4A5A5F) : const Color(0xFFE1E1E1);
}
