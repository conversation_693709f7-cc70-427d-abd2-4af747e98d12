import 'dart:async';
import 'dart:developer';

import 'package:dio/dio.dart';

sealed class Result<T> {
  bool get isSuccess => this is Success;
  bool get isFailure => this is Failure;

  Success<T> get success => this as Success<T>;
  Failure get failure => this as Failure;
}

class Success<T> extends Result<T> {
  final T data;

  Success(this.data);
}

class Failure<T> extends Result<T> {
  final String message;

  Failure([this.message = "Some Unexpected Error Occured"]);
}

extension ResultExtension<T> on Result<T> {
  /// Handle the result based on the success or failure
  R fold<R>({
    required Function(T data) onSuccess,
    required Function(String message) onFailure,
  }) {
    return switch (this) {
      Success() => onSuccess(success.data),
      Failure() => onFailure(failure.message),
    };
  }

  /// Logs the error message if the result is a failure
  T ignoreFailure() {
    return fold(
      onSuccess: (data) => data,
      onFailure: (message) {
        log('Ignored Failure: ====================================');
        log(message, stackTrace: StackTrace.current);
        log('=====================================================');
      },
    );
  }
}

extension ErrorHandler<T> on Future<T> {
  /// Handles a Future with Try-Catch while you can specify your own exception by using CustomException
  Future<Result<T>> asResult() async {
    try {
      return Success<T>(await this);
    } on DioException catch (e) {
      return Failure<T>(e.message ?? e.toString());
    } on CustomException catch (e) {
      return Failure<T>(e.message);
    } catch (e) {
      return Failure<T>(e.toString());
    }
  }

  Future<Result<R>> mapToResult<R>(R Function(T value) map) async {
    try {
      return Success<R>(map(await this));
    } on DioException catch (e) {
      final failure = e.response?.data['message'];
      return Failure<R>(
        failure is List
            ? failure.join('\n')
            : failure ?? e.message ?? e.toString(),
      );
    } on CustomException catch (e) {
      return Failure<R>(e.message);
    } catch (e) {
      return Failure<R>(e.toString());
    }
  }
}

class CustomException implements Exception {
  final String message;

  const CustomException(this.message);
}
