import 'dart:developer';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get_it/get_it.dart';
import 'package:hrms_tst/core/config/environment.dart';
import 'package:hrms_tst/core/services/auth/protocol/auth.protocol.dart';
import 'package:hrms_tst/core/services/auth/protocol/token.protocol.dart';
import 'package:hrms_tst/core/services/auth/token.service.dart';
import 'package:hrms_tst/core/services/geofencing/geofencing.service.dart';
import 'package:hrms_tst/features/employee/data/employee.protocol.dart';
import 'package:hrms_tst/features/employee/data/employee.repository.dart';
import 'package:hrms_tst/features/request/data/request.protocol.dart';
import 'package:hrms_tst/features/request/data/request.repository.dart';
import 'package:hrms_tst/features/teammates/data/team_progress.protocol.dart';
import 'package:hrms_tst/features/teammates/data/team_progress.repository.dart';
import 'package:hrms_tst/features/teammates/data/teammates.protocol.dart';
import 'package:hrms_tst/features/teammates/data/teammates.repository.dart';
import 'package:hrms_tst/features/notifications/data/notifications.protocol.dart';
import 'package:hrms_tst/features/notifications/data/notifications.repository.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:talker_dio_logger/talker_dio_logger_interceptor.dart';
import 'package:talker_dio_logger/talker_dio_logger_settings.dart';

import 'core/services/auth/auth.interceptor.dart';
import 'core/services/auth/auth.service.dart';
import 'core/services/device_info/device_info.service.dart';
import 'core/services/media_picker/media_picker.protocol.dart';
import 'core/services/media_picker/media_picker.service.dart';
import 'core/services/network/network.service.dart';
import 'core/services/notifications/local_notifications/local_notifications.protocol.dart';
import 'core/services/notifications/local_notifications/local_notifications.service.dart';
import 'core/services/notifications/push_notifications/push_notifications.service.dart';
import 'core/services/permissions/permissions.protocol.dart';
import 'core/services/permissions/permissions.service.dart';
import 'core/services/prefs/prefs.protocol.dart';
import 'core/services/prefs/prefs.service.dart';
import 'features/app_update/data/app_config.protocol.dart';
import 'features/app_update/data/app_config.repository.dart';
import 'features/home/<USER>/home.protocol.dart';
import 'features/home/<USER>/home.repository.dart';

final getIt = GetIt.instance;

Future<void> setupDependencies() async {
  getIt.registerLazySingleton<DeviceInfoService>(
    () => DeviceInfoService(deviceInfoPlugin: DeviceInfoPlugin()),
  );
  final sharedPrefs = await SharedPreferences.getInstance();
  getIt.registerSingleton<PrefsProtocol>(PrefsService(prefs: sharedPrefs));

  final token = await getIt.get<PrefsProtocol>().get<String>(
    'access_token',
    null,
  );
  log('USER TOKEN: $token');

  getIt.registerSingleton<NetworkService>(
    NetworkService(dio: Dio(), env: getIt.get<Environment>()),
  );

  await getIt.get<NetworkService>().addInterceptor(
    (networkService) =>
        TalkerDioLogger(settings: TalkerDioLoggerSettings(enabled: true)),
  );

  getIt.registerSingleton<PermissionsProtocol>(
    PermissionsService(deviceInfoService: getIt.get<DeviceInfoService>()),
  );

  await _initMediaPicker();

  getIt.registerSingleton<AppConfigProtocol>(AppConfigRepository());

  await _initNotifications();
  await _initAuth();

  getIt.registerSingleton<HomeProtocol>(HomeRepository());
  getIt.registerSingleton<GeofencingService>(GeofencingService());

  getIt.registerSingleton<TeammatesProtocol>(TeammatesRepository());
  getIt.registerSingleton<TeamProgressProtocol>(TeamProgressRepository());
  getIt.registerSingleton<EmployeeProtocol>(EmployeeRepository());
  getIt.registerSingleton<RequestProtocol>(RequestRepository());
  getIt.registerSingleton<NotificationsProtocol>(NotificationsRepository());
}

Future<void> _initAuth() async {
  getIt.registerSingleton<TokenProtocol>(
    TokenService(prefs: getIt.get<PrefsProtocol>()),
  );
  await getIt.get<TokenProtocol>().init();
  getIt.registerSingleton<AuthProtocol>(
    AuthService(
      tokenService: getIt.get<TokenProtocol>(),
      networkService: getIt.get<NetworkService>(),
    ),
  );

  getIt.get<NetworkService>().addInterceptor(
    (networkService) => AuthInterceptor(
      networkService: networkService,
      authService: getIt.get(),
      tokenService: getIt.get(),
    ),
  );
}

Future<void> _initNotifications() async {
  final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
  getIt.registerSingleton<LocalNotificationsProtocol>(
    LocalNotificationsService(
      flutterLocalNotificationsPlugin: flutterLocalNotificationsPlugin,
      permissions: getIt.get(),
    ),
  );
  await getIt.get<LocalNotificationsProtocol>().init();

  getIt.registerSingleton<PushNotificationsService>(
    PushNotificationsService(localNotificationsService: getIt.get()),
  );
  await getIt.get<PushNotificationsService>().init();
}

Future<void> _initMediaPicker() async {
  getIt.registerSingleton<MediaPickerProtocol>(
    MediaPickerService(permissions: getIt.get<PermissionsProtocol>()),
  );
}
