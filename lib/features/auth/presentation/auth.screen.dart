import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/snackbar.helper.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/providers/auth.provider.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';

class AuthScreen extends ConsumerWidget {
  const AuthScreen({super.key});

  static const String name = 'Auth';
  static const String path = '/auth';

  static GoRoute route({List<RouteBase> routes = const []}) => GoRoute(
    path: path,
    name: name,
    pageBuilder: (context, state) => const MaterialPage(child: AuthScreen()),
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notifier = ref.read(authProvider.notifier);

    ref.listen(authProvider, (previous, next) {
      if (next.hasError) {
        context.showError(next.error.toString());
      }
    });

    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    'Welcome at',
                    style: context.textStyles.displaySmall?.copyWith(
                      color: context.colors.secondary,
                      fontWeight: FontWeight.w700,
                      fontSize: 32,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  Space.y(12),
                  SVGImage(
                    Assets.svgs.tstTechnologyFullName,
                    applyColor: false,
                  ),
                  Space.y(71),
                  Text(
                    'Please Login with your Google Account',
                    style: context.textStyles.titleMedium?.copyWith(
                      fontSize: 16,
                      color: context.colors.secondary.withValues(alpha: 0.87),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  Padding(
                    padding: const EdgeInsets.all(22.0),
                    child: ElevatedButton.icon(
                      onPressed: () async {
                        await notifier.signInWithGoogle();
                      },
                      icon: SVGImage(Assets.svgs.googleLogo, applyColor: false),
                      label: const Text('Continue with Google'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: context.colors.surface,
                        foregroundColor: context.colors.onSurface,
                        textStyle: context.textStyles.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: context.colors.onSurface.withValues(
                            alpha: 0.54,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
