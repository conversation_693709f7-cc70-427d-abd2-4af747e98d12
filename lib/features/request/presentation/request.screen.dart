import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/features/request/presentation/widgets/request_tab_options.dart';
import 'package:hrms_tst/features/request/presentation/widgets/tabs/approvals_tab.dart';
import 'package:hrms_tst/features/request/presentation/widgets/tabs/leave_request_tab.dart';
import 'package:hrms_tst/features/request/presentation/widgets/tabs/wfh_tab.dart';

class RequestScreen extends HookConsumerWidget {
  const RequestScreen({super.key});

  static const String name = 'Request';
  static const String path = '/request';

  static GoRoute route({List<RouteBase> routes = const []}) => GoRoute(
    path: path,
    name: name,
    routes: routes,
    pageBuilder: (context, state) =>
        const NoTransitionPage(child: RequestScreen()),
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedTab = useState(RequestTab.leave);
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: RequestTabOptions(
                selectedTab: selectedTab.value,
                onTabSelected: (tab) {
                  selectedTab.value = tab;
                },
              ),
            ),
            Expanded(
              child: switch (selectedTab.value) {
                RequestTab.leave => LeaveRequestTab(),
                RequestTab.wfh => WFHTab(),
                RequestTab.approvals => ApprovalsTab(),
              },
            ),
          ],
        ),
      ),
    );
  }
}
