import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/extensions/string.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/request/domain/request.controller.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';
import 'package:hrms_tst/shared/widgets/filters_panel.dart';
import 'package:hrms_tst/shared/widgets/month_range_selection.dart';
import 'package:hrms_tst/shared/widgets/no_data_view.dart';
import 'package:hrms_tst/shared/widgets/popup_filter_button.dart';
import 'package:intl/intl.dart';

class ApprovalsTab extends ConsumerWidget {
  const ApprovalsTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16).copyWith(bottom: 16),
      child: CardContainer(
        elevation: 1,
        child: RefreshIndicator.adaptive(
          onRefresh: () async {
            ref.invalidate(approvalRecordsProvider);
          },
          child: HookBuilder(
            builder: (context) {
              final startDate = useState(
                DateTime.now().copyWith(day: 1).toUtc(),
              );
              final endDate = useState(
                startDate.value
                    .copyWith(month: startDate.value.month + 1)
                    .subtract(Duration(days: 1))
                    .toUtc(),
              );

              final typeFilter = useState<String?>(null);
              final leaveTypeIdFilter = useState<int?>(null);
              return ListView(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0).copyWith(bottom: 0),
                    child: Row(
                      children: [
                        Text(
                          'Approvals',
                          style: context.textStyles.titleMedium?.copyWith(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: context.colors.secondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Space.y(12),
                  FiltersPanel(
                    filters: [
                      PopupFilterButton(
                        filterName: 'Request Type',
                        initialValue: getTypeFromFilters(
                          typeFilter.value,
                          leaveTypeIdFilter.value,
                        ),
                        options: [
                          'Sick Leave',
                          'Paid Leave',
                          'Unpaid Leave',
                          'Work From Home',
                        ],
                        stringValue: (value) => value,
                        onSelected: (option) {
                          if (option == 'Work From Home') {
                            typeFilter.value = 'wfh';
                            leaveTypeIdFilter.value = null;
                          } else {
                            typeFilter.value = 'leave';
                            leaveTypeIdFilter.value = switch (option) {
                              'Paid Leave' => 1,
                              'Sick Leave' => 2,
                              'Unpaid Leave' => 3,
                              _ => null,
                            };
                          }
                        },
                      ),
                    ],
                    trailing: [
                      MonthRangeSelection(
                        filterButtonColor: context.colors.surface,
                        startDate: startDate.value,
                        endDate: endDate.value,
                        onFilterSelected: (start, end) {
                          startDate.value = start;
                          endDate.value = end;

                          context.pop();
                        },
                      ),
                    ],
                  ),
                  Space.y(8),
                  switch (ref.watch(
                    approvalRecordsProvider(
                      startDate.value,
                      endDate.value,
                      typeFilter.value,
                      leaveTypeIdFilter.value,
                    ),
                  )) {
                    AsyncData(:final value) => Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        if (value.isEmpty)
                          Padding(
                            padding: const EdgeInsets.all(24.0),
                            child: NoDataView(),
                          ),
                        ...value.map((record) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(
                              vertical: 8,
                              horizontal: 16,
                            ),
                            child: Container(
                              padding: EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: context.colors.outlineVariant,
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  Row(
                                    children: [
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Text(
                                            record.startDate != null
                                                ? DateFormat('dd MMM').format(
                                                    record.startDate!.toLocal(),
                                                  )
                                                : '',
                                            style: context.textStyles.bodyMedium
                                                ?.copyWith(
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                          ),
                                          Text(
                                            record.type == 'wfh'
                                                ? 'Work From Home'
                                                : record.leaveType?.name ?? '',
                                            style: context.textStyles.bodyMedium
                                                ?.copyWith(
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w400,
                                                ),
                                          ),
                                        ],
                                      ),
                                      Spacer(),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Text(
                                            record.status?.capitalize() ?? '',
                                            style: context.textStyles.bodyMedium
                                                ?.copyWith(
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w600,
                                                  color: context.colors.primary,
                                                ),
                                          ),
                                          Text(
                                            record.reviewer?.displayName ?? '',
                                            style: context.textStyles.bodyMedium
                                                ?.copyWith(
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w400,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  Space.y(16),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      vertical: 8,
                                      horizontal: 16,
                                    ),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      color: context.colors.outline,
                                    ),
                                    child: Text(
                                      record.reason ?? '',
                                      style: context.textStyles.titleSmall
                                          ?.copyWith(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                            color: context.colors.onSurface,
                                          ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }),
                        Space.y(8),
                      ],
                    ),
                    AsyncError(:final error) => Center(
                      child: Text(error.toString()),
                    ),
                    _ => Center(
                      child: Padding(
                        padding: const EdgeInsets.all(24.0),
                        child: CircularProgressIndicator.adaptive(),
                      ),
                    ),
                  },
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  String? getTypeFromFilters(String? type, int? leaveTypeId) {
    if (type == 'wfh') {
      return 'Work From Home';
    } else if (type == 'leave') {
      return switch (leaveTypeId) {
        1 => 'Paid Leave',
        2 => 'Sick Leave',
        3 => 'Unpaid Leave',
        _ => null,
      };
    } else {
      return null;
    }
  }
}
