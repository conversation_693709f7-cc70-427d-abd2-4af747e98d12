import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/extensions/string.extension.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/core/utils/helpers/snackbar.helper.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/request/presentation/widgets/wfh_request_dialog.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';
import 'package:hrms_tst/shared/widgets/confirmation_dialog.dart';
import 'package:hrms_tst/shared/widgets/filters_panel.dart';
import 'package:hrms_tst/shared/widgets/month_range_selection.dart';
import 'package:hrms_tst/shared/widgets/no_data_view.dart';
import 'package:hrms_tst/shared/widgets/popup_filter_button.dart';
import 'package:intl/intl.dart';

import '../../../domain/request.controller.dart';

class WFHTab extends ConsumerWidget {
  const WFHTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      floatingActionButton: FloatingActionButton.extended(
        heroTag: 'wfh-request',
        onPressed: () {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => WFHRequestDialog(),
          );
        },
        label: Text('Request'),
        icon: Icon(Icons.add),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
        ).copyWith(bottom: 16),
        child: CardContainer(
          elevation: 1,
          child: RefreshIndicator.adaptive(
            onRefresh: () async {
              ref.invalidate(wfhRecordsProvider);
            },
            child: HookBuilder(
              builder: (context) {
                final startDate = useState(
                  DateTime.now().copyWith(day: 1).toUtc(),
                );
                final endDate = useState(
                  startDate.value
                      .copyWith(month: startDate.value.month + 1)
                      .subtract(Duration(days: 1))
                      .toUtc(),
                );

                final wfhStatusFilter = useState<String?>(null);
                return ListView(
                  children: [
                    Space.y(16),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: [
                          Text(
                            'WFH History',
                            style: context.textStyles.titleMedium?.copyWith(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: context.colors.secondary,
                            ),
                          ),
                          Spacer(),
                        ],
                      ),
                    ),
                    Space.y(12),
                    FiltersPanel(
                      filters: [
                        PopupFilterButton(
                          filterName: 'Status',
                          initialValue: wfhStatusFilter.value,
                          options: ['Approved', 'Rejected', 'Pending'],
                          stringValue: (value) => value,
                          onSelected: (option) {
                            wfhStatusFilter.value = option;
                          },
                        ),
                      ],
                      trailing: [
                        MonthRangeSelection(
                          filterButtonColor: context.colors.surface,
                          startDate: startDate.value,
                          endDate: endDate.value,
                          onFilterSelected: (start, end) {
                            startDate.value = start;
                            endDate.value = end;

                            context.pop();
                          },
                        ),
                      ],
                    ),
                    Space.y(8),
                    switch (ref.watch(
                      wfhRecordsProvider(
                        fromDate: startDate.value,
                        toDate: endDate.value,
                        status: wfhStatusFilter.value,
                      ),
                    )) {
                      AsyncData(:final value) => Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          if (value.isEmpty)
                            Padding(
                              padding: const EdgeInsets.all(24.0),
                              child: NoDataView(),
                            ),
                          ...value.map((record) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: 8,
                                horizontal: 16,
                              ),
                              child: Container(
                                padding: EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: context.colors.outlineVariant,
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.stretch,
                                  children: [
                                    Row(
                                      children: [
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Text(
                                              record.startDate != null
                                                  ? DateFormat('dd MMM').format(
                                                      record.startDate!
                                                          .toLocal(),
                                                    )
                                                  : '',
                                              style: context
                                                  .textStyles
                                                  .bodyMedium
                                                  ?.copyWith(
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                            ),
                                            Text(
                                              record.startDate != null
                                                  ? DateFormat('EEE').format(
                                                      record.startDate!
                                                          .toLocal(),
                                                    )
                                                  : '',
                                              style: context
                                                  .textStyles
                                                  .bodyMedium
                                                  ?.copyWith(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                            ),
                                          ],
                                        ),
                                        Spacer(),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Text(
                                              (record.status ?? '')
                                                  .capitalize(),
                                              style: context
                                                  .textStyles
                                                  .bodyMedium
                                                  ?.copyWith(
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w600,
                                                    color:
                                                        context.colors.primary,
                                                  ),
                                            ),
                                            Text(
                                              record.reviewer?.displayName ??
                                                  '',
                                              style: context
                                                  .textStyles
                                                  .bodyMedium
                                                  ?.copyWith(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    Space.y(16),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Container(
                                            padding: EdgeInsets.symmetric(
                                              vertical: 8,
                                              horizontal: 16,
                                            ),
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              color: context.colors.outline,
                                            ),
                                            child: Text(
                                              (record.reason ?? '')
                                                  .capitalize(),
                                              style: context
                                                  .textStyles
                                                  .titleSmall
                                                  ?.copyWith(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.w500,
                                                    color: context
                                                        .colors
                                                        .onSurface,
                                                  ),
                                            ),
                                          ),
                                        ),
                                        if (record.status == 'pending')
                                          IconButton(
                                            onPressed: () {
                                              showAdaptiveDialog(
                                                context: context,
                                                builder: (context) =>
                                                    ConfirmationDialog(
                                                      title:
                                                          'Cancel WFH Request',
                                                      cancelText: 'No',
                                                      confirmText: 'Yes',
                                                      onConfirm: () async {
                                                        final result = await ref
                                                            .read(
                                                              wfhRequestProvider
                                                                  .notifier,
                                                            )
                                                            .deleteRequest(
                                                              requestId:
                                                                  record.id,
                                                            );
                                                        result.fold(
                                                          onSuccess: (data) {},
                                                          onFailure:
                                                              (
                                                                message,
                                                              ) => context
                                                                  .showError(
                                                                    message,
                                                                  ),
                                                        );
                                                      },
                                                    ),
                                              );
                                            },
                                            icon: Icon(
                                              Icons.delete,
                                              color: context.colors.error,
                                            ),
                                          ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }),
                        ],
                      ),
                      AsyncError(:final error) => Center(
                        child: Text(error.toString()),
                      ),
                      _ => Center(
                        child: Padding(
                          padding: const EdgeInsets.all(24.0),
                          child: CircularProgressIndicator.adaptive(),
                        ),
                      ),
                    },
                    Space.y(80),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
