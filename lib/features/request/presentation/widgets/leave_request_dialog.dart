import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/extensions/number.extension.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/core/utils/helpers/snackbar.helper.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/core/utils/helpers/validators.dart';
import 'package:hrms_tst/features/home/<USER>/models/leave_balance.model.dart';
import 'package:hrms_tst/features/request/domain/request.controller.dart';
import 'package:hrms_tst/features/teammates/data/models/teammate.model.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/app_dropdown_field.dart';
import 'package:hrms_tst/shared/widgets/app_text_field.dart';
import 'package:hrms_tst/shared/widgets/cached_image.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';
import 'package:intl/intl.dart';

import 'request_date_picker_section.dart';

class LeaveRequestDialog extends HookConsumerWidget {
  const LeaveRequestDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(leaveRequestProvider);
    final notifier = ref.read(leaveRequestProvider.notifier);

    final formKey = useState(GlobalKey<FormState>());

    final notifyToController = useTextEditingController();

    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 20),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
        child: Form(
          key: formKey.value,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Request Leave',
                      style: context.textStyles.titleLarge?.copyWith(
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                        color: context.colors.secondary,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        context.pop();
                      },
                      icon: SVGImage(
                        Assets.svgs.closeIcon,
                        height: 24,
                        width: 24,
                        color: context.colors.secondary,
                      ),
                    ),
                  ],
                ),
                Space.y(10),
                FormField(
                  validator: (value) {
                    if (state.startDate == null || state.endDate == null) {
                      return 'Please select leave dates';
                    }
                    if (state.startDate!.isAfter(state.endDate!)) {
                      return 'Start date should be before End date';
                    }
                    return null;
                  },
                  builder: (field) => RequestDatePickerSection(
                    fieldState: field,
                    startDate: state.startDate,
                    endDate: state.endDate,
                    onStartDateSelected: (startDate) {
                      notifier.setDate(startDate, state.endDate);
                    },
                    onEndDateSelected: (endDate) {
                      notifier.setDate(state.startDate, endDate);
                    },
                    isDateSelectable: state.isDateSelectable,
                  ),
                ),
                Space.y(10),
                Text(
                  'Select Leave Type',
                  style: context.textStyles.bodyMedium?.copyWith(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF7A7A7A),
                  ),
                ),
                Space.y(4),
                AppDropdownField<AvailableLeaveType>(
                  enabled: true,
                  readOnly: false,
                  validator: (leaveType) {
                    if (leaveType == null) return 'Please select leave type';
                    return null;
                  },
                  selectedItemBuilder: (context) {
                    return state.availableLeaveTypes
                        .map((e) => Text(e.name))
                        .toList();
                  },
                  items: state.availableLeaveTypes.map((e) {
                    final isEnabled =
                        (_getDaysDifference(state.startDate, state.endDate) ??
                            366) <=
                        e.balance;
                    return DropdownMenuItem<AvailableLeaveType>(
                      enabled: isEnabled,
                      value: e,

                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            e.name,
                            style: context.textStyles.bodyMedium?.copyWith(
                              color: isEnabled
                                  ? context.colors.onSurface
                                  : context.colors.outlineVariant,
                            ),
                          ),
                          Text(
                            '${e.balance == 365 ? '' : e.balance.showFractionIfAvailable} Available',
                            style: context.textStyles.bodyMedium?.copyWith(
                              color: isEnabled
                                  ? context.colors.onSurface
                                  : context.colors.outlineVariant,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),

                  fillColor: context.colors.surface,
                  onChanged: (leaveType) {
                    if (leaveType == null) return;
                    notifier.setLeaveType(leaveType);
                  },
                ),
                Space.y(10),
                Row(
                  children: [
                    Expanded(
                      child: AppDropdownField<String>(
                        enabled: true,
                        readOnly: false,
                        hintText: 'Leave Period',
                        labelText: state.startDate != null
                            ? DateFormat(
                                'dd-MM-yyyy',
                              ).format(state.startDate!.toLocal())
                            : null,
                        items: ['First Half', 'Second Half', 'Full Day']
                            .map(
                              (e) => DropdownMenuItem(value: e, child: Text(e)),
                            )
                            .toList(),
                        fillColor: context.colors.surface,
                        validator: Validator(
                          fieldName: 'Leave Period',
                          validations: [
                            Validations.required(
                              error: 'Select period for leave',
                            ),
                          ],
                        ).build,
                        onChanged: (value) {
                          if (value == 'First Half') {
                            notifier.setStartDaySlot('first-half');
                          } else if (value == 'Second Half') {
                            notifier.setStartDaySlot('second-half');
                          } else if (value == 'Full Day') {
                            notifier.setStartDaySlot('full-day');
                          }
                        },
                      ),
                    ),

                    if ((_getDaysDifference(state.startDate, state.endDate) ??
                            0) >
                        0) ...[
                      Space.x(8),
                      Expanded(
                        child: AppDropdownField<String>(
                          enabled: true,
                          readOnly: false,
                          hintText: 'Leave Period',
                          labelText: state.endDate != null
                              ? DateFormat(
                                  'dd-MM-yyyy',
                                ).format(state.endDate!.toLocal())
                              : null,
                          items: ['First Half', 'Full Day']
                              .map(
                                (e) =>
                                    DropdownMenuItem(value: e, child: Text(e)),
                              )
                              .toList(),
                          fillColor: context.colors.surface,
                          onChanged: (value) {
                            if (value == 'First Half') {
                              notifier.setEndDaySlot('first-half');
                            } else if (value == 'Full Day') {
                              notifier.setStartDaySlot('full-day');
                            }
                          },
                        ),
                      ),
                    ],
                  ],
                ),
                Space.y(10),
                AppTextField(
                  maxLines: 3,
                  onChanged: notifier.setReason,
                  labelText: 'Reason',
                  fillColor: context.colors.surface,
                  hintText: 'Type Here',
                  validator: Validator(
                    fieldName: 'Reason',
                    validations: [
                      Validations.required(
                        error: 'Please enter a reason for leave',
                      ),
                    ],
                  ).build,
                ),
                Space.y(6),
                TypeAheadField<TeammateModel>(
                  controller: notifyToController,
                  autoFlipDirection: true,
                  direction: VerticalDirection.up,
                  hideOnSelect: false,

                  constraints: BoxConstraints(
                    maxHeight: context.screenHeight * 0.4,
                  ),
                  builder: (context, controller, focusNode) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Wrap(
                          runAlignment: WrapAlignment.start,
                          spacing: 8,

                          children: state.notifyTo
                              .map(
                                (e) => Chip(
                                  shape: StadiumBorder(),
                                  label: Text(e.user.firstName ?? ''),
                                  deleteIcon: Icon(Icons.cancel),
                                  onDeleted: () {
                                    notifier.removeNotifyTo(e);
                                  },
                                ),
                              )
                              .toList(),
                        ),
                        Space.y(4),
                        AppTextField(
                          autofocus: false,
                          controller: controller,
                          focusNode: focusNode,
                          contentPadding: EdgeInsets.symmetric(
                            vertical: 12,
                            horizontal: 16,
                          ),
                          hintText: 'Notify to',
                          fillColor: context.colors.surface,
                        ),
                      ],
                    );
                  },
                  onSelected: (value) {
                    notifier.addNotifyTo(value);
                    notifyToController.clear();
                  },

                  suggestionsCallback: (search) {
                    return state.availableTeammates
                        .where(
                          (element) =>
                              element.user.displayName?.toLowerCase().contains(
                                search.toLowerCase(),
                              ) ??
                              false,
                        )
                        .toList();
                  },
                  itemBuilder: (context, teammate) => ListTile(
                    leading: CachedImage(
                      url: teammate.user.image ?? '',
                      height: 44,
                      width: 44,
                      decoration: BoxDecoration(shape: BoxShape.circle),
                    ),
                    title: Text(
                      teammate.user.displayName ?? '',
                      style: context.textStyles.bodyMedium?.copyWith(
                        color: context.colors.onSurface,
                      ),
                    ),
                  ),
                ),
                Space.y(10),
                OutlinedButton(
                  onPressed: () async {
                    if (!formKey.value.currentState!.validate()) {
                      return;
                    }

                    final result = await notifier.requestLeave();

                    result.fold(
                      onSuccess: (_) {
                        context.pop();
                        context.showSuccess('Leave request sent successfully');
                      },
                      onFailure: (message) {
                        context.pop();
                        context.showError(message);
                      },
                    );
                  },
                  child: Text(
                    'Apply Leave',
                    style: context.textStyles.titleMedium?.copyWith(
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                      color: context.colors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  int? _getDaysDifference(DateTime? startDate, DateTime? endDate) {
    if (startDate == null || endDate == null) return null;
    return endDate.difference(startDate).inDays;
  }
}
