import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/core/utils/helpers/snackbar.helper.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/core/utils/helpers/validators.dart';
import 'package:hrms_tst/features/request/domain/request.controller.dart';
import 'package:hrms_tst/features/teammates/data/models/teammate.model.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/app_text_field.dart';
import 'package:hrms_tst/shared/widgets/cached_image.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';
import 'request_date_picker_section.dart';

class WFHRequestDialog extends HookConsumerWidget {
  const WFHRequestDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(wfhRequestProvider);
    final notifier = ref.read(wfhRequestProvider.notifier);

    final formKey = useState(GlobalKey<FormState>());

    final notifyToController = useTextEditingController();

    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 20),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
        child: Form(
          key: formKey.value,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'WFH Request',
                    style: context.textStyles.titleLarge?.copyWith(
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                      color: context.colors.secondary,
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      context.pop();
                    },
                    icon: SVGImage(
                      Assets.svgs.closeIcon,
                      height: 24,
                      width: 24,
                      color: context.colors.secondary,
                    ),
                  ),
                ],
              ),
              Space.y(10),
              FormField(
                validator: (value) {
                  if (state.startDate == null || state.endDate == null) {
                    return 'Please select WFH dates';
                  }
                  if (state.startDate!.isAfter(state.endDate!)) {
                    return 'Start date should be before End date';
                  }
                  return null;
                },
                builder: (field) => RequestDatePickerSection(
                  fieldState: field,
                  startDate: state.startDate,
                  endDate: state.endDate,
                  onStartDateSelected: (startDate) {
                    notifier.setDate(startDate, state.endDate);
                  },
                  onEndDateSelected: (endDate) {
                    notifier.setDate(state.startDate, endDate);
                  },
                  isDateSelectable: state.isDateSelectable,
                ),
              ),
              Space.y(10),

              AppTextField(
                maxLines: 3,
                onChanged: notifier.setReason,
                labelText: 'Reason',
                fillColor: context.colors.surface,
                hintText: 'Type Here',
                validator: Validator(
                  fieldName: 'Reason',
                  validations: [
                    Validations.required(
                      error: 'Please enter a reason for leave',
                    ),
                  ],
                ).build,
              ),
              Space.y(10),
              TypeAheadField<TeammateModel>(
                controller: notifyToController,
                autoFlipDirection: true,
                direction: VerticalDirection.up,
                hideOnSelect: false,

                constraints: BoxConstraints(
                  maxHeight: context.screenHeight * 0.4,
                ),
                builder: (context, controller, focusNode) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Wrap(
                        runAlignment: WrapAlignment.start,
                        spacing: 8,

                        children: state.notifyTo
                            .map(
                              (e) => Chip(
                                shape: StadiumBorder(),
                                label: Text(e.user.firstName ?? ''),
                                deleteIcon: Icon(Icons.cancel),
                                onDeleted: () {
                                  notifier.removeNotifyTo(e);
                                },
                              ),
                            )
                            .toList(),
                      ),
                      Space.y(4),
                      AppTextField(
                        autofocus: false,
                        controller: controller,
                        focusNode: focusNode,
                        contentPadding: EdgeInsets.symmetric(
                          vertical: 12,
                          horizontal: 16,
                        ),
                        hintText: 'Notify to',
                        fillColor: context.colors.surface,
                      ),
                    ],
                  );
                },
                onSelected: (value) {
                  notifier.addNotifyTo(value);
                  notifyToController.clear();
                },

                suggestionsCallback: (search) {
                  return state.availableTeammates
                      .where(
                        (element) =>
                            element.user.displayName?.toLowerCase().contains(
                              search.toLowerCase(),
                            ) ??
                            false,
                      )
                      .toList();
                },
                itemBuilder: (context, teammate) => ListTile(
                  leading: CachedImage(
                    url: teammate.user.image ?? '',
                    height: 44,
                    width: 44,
                    decoration: BoxDecoration(shape: BoxShape.circle),
                  ),
                  title: Text(
                    teammate.user.displayName ?? '',
                    style: context.textStyles.bodyMedium?.copyWith(
                      color: context.colors.onSurface,
                    ),
                  ),
                ),
              ),
              Space.y(10),
              OutlinedButton(
                onPressed: () async {
                  if (!formKey.value.currentState!.validate()) {
                    return;
                  }

                  final result = await notifier.requestWFH();

                  result.fold(
                    onSuccess: (_) {
                      context.pop();
                      context.showSuccess('WFH request sent successfully');
                    },
                    onFailure: (message) {
                      context.pop();
                      context.showError(message);
                    },
                  );
                },
                child: Text(
                  'Apply WFH',
                  style: context.textStyles.titleMedium?.copyWith(
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                    color: context.colors.primary,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
