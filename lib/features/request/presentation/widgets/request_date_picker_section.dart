import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:intl/intl.dart';

class RequestDatePickerSection extends ConsumerWidget {
  const RequestDatePickerSection({
    super.key,
    required this.fieldState,
    required this.startDate,
    required this.endDate,
    required this.onStartDateSelected,
    required this.onEndDateSelected,
    required this.isDateSelectable,
  });

  final FormFieldState<Object?> fieldState;
  final DateTime? startDate;
  final DateTime? endDate;
  final Function(DateTime? startDate) onStartDateSelected;
  final Function(DateTime? endDate) onEndDateSelected;
  final Function(DateTime date) isDateSelectable;

  Future<DateTime?> selectDate({
    required BuildContext context,
    required DateTime firstDate,
    required DateTime lastDate,
  }) async {
    return await showDatePicker(
      context: context,
      firstDate: firstDate,
      lastDate: lastDate,
      selectableDayPredicate: (day) => isDateSelectable(day),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final borderColor = fieldState.hasError
        ? context.colors.error
        : context.colors.outline;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5),
            border: BoxBorder.all(width: 1, color: borderColor),
          ),
          child: IntrinsicHeight(
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: InkWell(
                    highlightColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    onTap: () async {
                      final startDate = await selectDate(
                        context: context,
                        firstDate: DateTime.now(),
                        lastDate: DateTime(DateTime.now().year + 1),
                      );
                      onStartDateSelected(startDate);
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'From',
                            style: context.textStyles.labelSmall?.copyWith(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: context.colors.secondary,
                            ),
                          ),
                          Text(
                            startDate != null
                                ? DateFormat(
                                    'dd-MM-yyyy',
                                  ).format(startDate!.toLocal())
                                : 'Select date',
                            style: context.textStyles.labelMedium?.copyWith(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: context.colors.onSurface.withValues(
                                alpha: startDate != null ? 1 : 0.6,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                VerticalDivider(thickness: 1, color: borderColor),
                Expanded(
                  child: Center(
                    child: Text(
                      endDate == null || startDate == null
                          ? '0 days'
                          : '${(endDate!).difference(startDate!).inDays + 1} days',
                      style: context.textStyles.labelLarge?.copyWith(
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        color: context.colors.onSurface.withValues(alpha: 0.8),
                      ),
                    ),
                  ),
                ),
                VerticalDivider(thickness: 1, color: borderColor),
                Expanded(
                  flex: 2,
                  child: InkWell(
                    highlightColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    onTap: () async {
                      final endDate = await selectDate(
                        context: context,
                        firstDate: startDate ?? DateTime.now(),
                        lastDate: DateTime.now().add(Duration(days: 365)),
                      );

                      onEndDateSelected(endDate);
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            'To',
                            style: context.textStyles.labelSmall?.copyWith(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: context.colors.secondary,
                            ),
                          ),
                          Text(
                            endDate != null
                                ? DateFormat(
                                    'dd-MM-yyyy',
                                  ).format(endDate!.toLocal())
                                : 'Select date',
                            style: context.textStyles.labelMedium?.copyWith(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: context.colors.onSurface.withValues(
                                alpha: endDate != null ? 1 : 0.6,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        if (fieldState.hasError)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8),
            child: Text(
              '${fieldState.errorText}',
              style: context.textStyles.labelMedium?.copyWith(
                color: context.colors.error,
              ),
            ),
          ),
      ],
    );
  }
}
