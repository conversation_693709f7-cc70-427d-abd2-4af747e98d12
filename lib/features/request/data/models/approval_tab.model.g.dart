// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'approval_tab.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ApprovalRecordModel _$ApprovalRecordModelFromJson(Map<String, dynamic> json) =>
    _ApprovalRecordModel(
      id: (json['id'] as num).toInt(),
      type: json['type'] as String?,
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      startDaySlot: json['startDaySlot'] as String?,
      endDaySlot: json['endDaySlot'] as String?,
      reason: json['reason'] as String?,
      rejectReason: json['rejectReason'] as String?,
      status: json['status'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      leaveType: json['leaveType'] == null
          ? null
          : LeaveType.fromJson(json['leaveType'] as Map<String, dynamic>),
      reviewer: reviewerFromJson(json['reviewer'] as Map<String, dynamic>?),
    );

Map<String, dynamic> _$ApprovalRecordModelToJson(
  _ApprovalRecordModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'type': instance.type,
  'startDate': instance.startDate?.toIso8601String(),
  'endDate': instance.endDate?.toIso8601String(),
  'startDaySlot': instance.startDaySlot,
  'endDaySlot': instance.endDaySlot,
  'reason': instance.reason,
  'rejectReason': instance.rejectReason,
  'status': instance.status,
  'createdAt': instance.createdAt?.toIso8601String(),
  'leaveType': instance.leaveType,
  'reviewer': instance.reviewer,
};
