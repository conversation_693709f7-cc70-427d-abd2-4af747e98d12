import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../home/<USER>/models/holiday.model.dart';

part 'request_util.model.freezed.dart';
part 'request_util.model.g.dart';

@freezed
abstract class DisabledDatesModel with _$DisabledDatesModel {
  const factory DisabledDatesModel({
    @JsonKey(name: 'timetable') required final WeekDaysModel timetable,
    required final List<HolidayTileData> holidays,
    @<PERSON>sonKey(name: 'leaveWfhRequests')
    required final List<AlreadyPresentRequest> requests,
  }) = _DisabledDatesModel;

  factory DisabledDatesModel.fromJson(Map<String, dynamic> json) =>
      _$DisabledDatesModelFromJson(json);
}

@freezed
abstract class WeekDaysModel with _$WeekDaysModel {
  const factory WeekDaysModel({
    final int? id,
    final int? sun,
    final int? mon,
    final int? tue,
    final int? wed,
    final int? thu,
    final int? fri,
    final int? sat,
  }) = _WeekDaysModel;

  factory WeekDaysModel.fromJson(Map<String, dynamic> json) =>
      _$WeekDaysModelFromJson(json);
}

@freezed
abstract class AlreadyPresentRequest with _$AlreadyPresentRequest {
  const factory AlreadyPresentRequest({
    required final int id,
    required final String type,
    required final DateTime startDate,
    required final DateTime endDate,
    required final String status,
  }) = _AlreadyPresentRequest;

  factory AlreadyPresentRequest.fromJson(Map<String, dynamic> json) =>
      _$AlreadyPresentRequestFromJson(json);
}
