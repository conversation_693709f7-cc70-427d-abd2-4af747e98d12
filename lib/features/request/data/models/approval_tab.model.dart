import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hrms_tst/features/employee/data/models/leave_tab.model.dart';
import 'package:hrms_tst/features/home/<USER>/models/leave_balance.model.dart';

part 'approval_tab.model.freezed.dart';
part 'approval_tab.model.g.dart';

@freezed
abstract class ApprovalRecordModel with _$ApprovalRecordModel {
  const factory ApprovalRecordModel({
    required final int id,
    final String? type,
    final DateTime? startDate,
    final DateTime? endDate,
    final String? startDaySlot,
    final String? endDaySlot,
    final String? reason,
    final String? rejectReason,
    final String? status,
    final DateTime? createdAt,
    final LeaveType? leaveType,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'reviewer', fromJson: reviewerFromJson)
    final LeaveReviewerModel? reviewer,
  }) = _ApprovalRecordModel;

  factory ApprovalRecordModel.fromJson(Map<String, dynamic> json) =>
      _$ApprovalRecordModelFrom<PERSON>son(json);
}
