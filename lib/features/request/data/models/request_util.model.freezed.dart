// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'request_util.model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DisabledDatesModel {

@JsonKey(name: 'timetable') WeekDaysModel get timetable; List<HolidayTileData> get holidays;@JsonKey(name: 'leaveWfhRequests') List<AlreadyPresentRequest> get requests;
/// Create a copy of DisabledDatesModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DisabledDatesModelCopyWith<DisabledDatesModel> get copyWith => _$DisabledDatesModelCopyWithImpl<DisabledDatesModel>(this as DisabledDatesModel, _$identity);

  /// Serializes this DisabledDatesModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DisabledDatesModel&&(identical(other.timetable, timetable) || other.timetable == timetable)&&const DeepCollectionEquality().equals(other.holidays, holidays)&&const DeepCollectionEquality().equals(other.requests, requests));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,timetable,const DeepCollectionEquality().hash(holidays),const DeepCollectionEquality().hash(requests));

@override
String toString() {
  return 'DisabledDatesModel(timetable: $timetable, holidays: $holidays, requests: $requests)';
}


}

/// @nodoc
abstract mixin class $DisabledDatesModelCopyWith<$Res>  {
  factory $DisabledDatesModelCopyWith(DisabledDatesModel value, $Res Function(DisabledDatesModel) _then) = _$DisabledDatesModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'timetable') WeekDaysModel timetable, List<HolidayTileData> holidays,@JsonKey(name: 'leaveWfhRequests') List<AlreadyPresentRequest> requests
});


$WeekDaysModelCopyWith<$Res> get timetable;

}
/// @nodoc
class _$DisabledDatesModelCopyWithImpl<$Res>
    implements $DisabledDatesModelCopyWith<$Res> {
  _$DisabledDatesModelCopyWithImpl(this._self, this._then);

  final DisabledDatesModel _self;
  final $Res Function(DisabledDatesModel) _then;

/// Create a copy of DisabledDatesModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? timetable = null,Object? holidays = null,Object? requests = null,}) {
  return _then(_self.copyWith(
timetable: null == timetable ? _self.timetable : timetable // ignore: cast_nullable_to_non_nullable
as WeekDaysModel,holidays: null == holidays ? _self.holidays : holidays // ignore: cast_nullable_to_non_nullable
as List<HolidayTileData>,requests: null == requests ? _self.requests : requests // ignore: cast_nullable_to_non_nullable
as List<AlreadyPresentRequest>,
  ));
}
/// Create a copy of DisabledDatesModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WeekDaysModelCopyWith<$Res> get timetable {
  
  return $WeekDaysModelCopyWith<$Res>(_self.timetable, (value) {
    return _then(_self.copyWith(timetable: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _DisabledDatesModel implements DisabledDatesModel {
  const _DisabledDatesModel({@JsonKey(name: 'timetable') required this.timetable, required final  List<HolidayTileData> holidays, @JsonKey(name: 'leaveWfhRequests') required final  List<AlreadyPresentRequest> requests}): _holidays = holidays,_requests = requests;
  factory _DisabledDatesModel.fromJson(Map<String, dynamic> json) => _$DisabledDatesModelFromJson(json);

@override@JsonKey(name: 'timetable') final  WeekDaysModel timetable;
 final  List<HolidayTileData> _holidays;
@override List<HolidayTileData> get holidays {
  if (_holidays is EqualUnmodifiableListView) return _holidays;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_holidays);
}

 final  List<AlreadyPresentRequest> _requests;
@override@JsonKey(name: 'leaveWfhRequests') List<AlreadyPresentRequest> get requests {
  if (_requests is EqualUnmodifiableListView) return _requests;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_requests);
}


/// Create a copy of DisabledDatesModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DisabledDatesModelCopyWith<_DisabledDatesModel> get copyWith => __$DisabledDatesModelCopyWithImpl<_DisabledDatesModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DisabledDatesModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DisabledDatesModel&&(identical(other.timetable, timetable) || other.timetable == timetable)&&const DeepCollectionEquality().equals(other._holidays, _holidays)&&const DeepCollectionEquality().equals(other._requests, _requests));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,timetable,const DeepCollectionEquality().hash(_holidays),const DeepCollectionEquality().hash(_requests));

@override
String toString() {
  return 'DisabledDatesModel(timetable: $timetable, holidays: $holidays, requests: $requests)';
}


}

/// @nodoc
abstract mixin class _$DisabledDatesModelCopyWith<$Res> implements $DisabledDatesModelCopyWith<$Res> {
  factory _$DisabledDatesModelCopyWith(_DisabledDatesModel value, $Res Function(_DisabledDatesModel) _then) = __$DisabledDatesModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'timetable') WeekDaysModel timetable, List<HolidayTileData> holidays,@JsonKey(name: 'leaveWfhRequests') List<AlreadyPresentRequest> requests
});


@override $WeekDaysModelCopyWith<$Res> get timetable;

}
/// @nodoc
class __$DisabledDatesModelCopyWithImpl<$Res>
    implements _$DisabledDatesModelCopyWith<$Res> {
  __$DisabledDatesModelCopyWithImpl(this._self, this._then);

  final _DisabledDatesModel _self;
  final $Res Function(_DisabledDatesModel) _then;

/// Create a copy of DisabledDatesModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? timetable = null,Object? holidays = null,Object? requests = null,}) {
  return _then(_DisabledDatesModel(
timetable: null == timetable ? _self.timetable : timetable // ignore: cast_nullable_to_non_nullable
as WeekDaysModel,holidays: null == holidays ? _self._holidays : holidays // ignore: cast_nullable_to_non_nullable
as List<HolidayTileData>,requests: null == requests ? _self._requests : requests // ignore: cast_nullable_to_non_nullable
as List<AlreadyPresentRequest>,
  ));
}

/// Create a copy of DisabledDatesModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WeekDaysModelCopyWith<$Res> get timetable {
  
  return $WeekDaysModelCopyWith<$Res>(_self.timetable, (value) {
    return _then(_self.copyWith(timetable: value));
  });
}
}


/// @nodoc
mixin _$WeekDaysModel {

 int? get id; int? get sun; int? get mon; int? get tue; int? get wed; int? get thu; int? get fri; int? get sat;
/// Create a copy of WeekDaysModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WeekDaysModelCopyWith<WeekDaysModel> get copyWith => _$WeekDaysModelCopyWithImpl<WeekDaysModel>(this as WeekDaysModel, _$identity);

  /// Serializes this WeekDaysModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WeekDaysModel&&(identical(other.id, id) || other.id == id)&&(identical(other.sun, sun) || other.sun == sun)&&(identical(other.mon, mon) || other.mon == mon)&&(identical(other.tue, tue) || other.tue == tue)&&(identical(other.wed, wed) || other.wed == wed)&&(identical(other.thu, thu) || other.thu == thu)&&(identical(other.fri, fri) || other.fri == fri)&&(identical(other.sat, sat) || other.sat == sat));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sun,mon,tue,wed,thu,fri,sat);

@override
String toString() {
  return 'WeekDaysModel(id: $id, sun: $sun, mon: $mon, tue: $tue, wed: $wed, thu: $thu, fri: $fri, sat: $sat)';
}


}

/// @nodoc
abstract mixin class $WeekDaysModelCopyWith<$Res>  {
  factory $WeekDaysModelCopyWith(WeekDaysModel value, $Res Function(WeekDaysModel) _then) = _$WeekDaysModelCopyWithImpl;
@useResult
$Res call({
 int? id, int? sun, int? mon, int? tue, int? wed, int? thu, int? fri, int? sat
});




}
/// @nodoc
class _$WeekDaysModelCopyWithImpl<$Res>
    implements $WeekDaysModelCopyWith<$Res> {
  _$WeekDaysModelCopyWithImpl(this._self, this._then);

  final WeekDaysModel _self;
  final $Res Function(WeekDaysModel) _then;

/// Create a copy of WeekDaysModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? sun = freezed,Object? mon = freezed,Object? tue = freezed,Object? wed = freezed,Object? thu = freezed,Object? fri = freezed,Object? sat = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,sun: freezed == sun ? _self.sun : sun // ignore: cast_nullable_to_non_nullable
as int?,mon: freezed == mon ? _self.mon : mon // ignore: cast_nullable_to_non_nullable
as int?,tue: freezed == tue ? _self.tue : tue // ignore: cast_nullable_to_non_nullable
as int?,wed: freezed == wed ? _self.wed : wed // ignore: cast_nullable_to_non_nullable
as int?,thu: freezed == thu ? _self.thu : thu // ignore: cast_nullable_to_non_nullable
as int?,fri: freezed == fri ? _self.fri : fri // ignore: cast_nullable_to_non_nullable
as int?,sat: freezed == sat ? _self.sat : sat // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _WeekDaysModel implements WeekDaysModel {
  const _WeekDaysModel({this.id, this.sun, this.mon, this.tue, this.wed, this.thu, this.fri, this.sat});
  factory _WeekDaysModel.fromJson(Map<String, dynamic> json) => _$WeekDaysModelFromJson(json);

@override final  int? id;
@override final  int? sun;
@override final  int? mon;
@override final  int? tue;
@override final  int? wed;
@override final  int? thu;
@override final  int? fri;
@override final  int? sat;

/// Create a copy of WeekDaysModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WeekDaysModelCopyWith<_WeekDaysModel> get copyWith => __$WeekDaysModelCopyWithImpl<_WeekDaysModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WeekDaysModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WeekDaysModel&&(identical(other.id, id) || other.id == id)&&(identical(other.sun, sun) || other.sun == sun)&&(identical(other.mon, mon) || other.mon == mon)&&(identical(other.tue, tue) || other.tue == tue)&&(identical(other.wed, wed) || other.wed == wed)&&(identical(other.thu, thu) || other.thu == thu)&&(identical(other.fri, fri) || other.fri == fri)&&(identical(other.sat, sat) || other.sat == sat));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sun,mon,tue,wed,thu,fri,sat);

@override
String toString() {
  return 'WeekDaysModel(id: $id, sun: $sun, mon: $mon, tue: $tue, wed: $wed, thu: $thu, fri: $fri, sat: $sat)';
}


}

/// @nodoc
abstract mixin class _$WeekDaysModelCopyWith<$Res> implements $WeekDaysModelCopyWith<$Res> {
  factory _$WeekDaysModelCopyWith(_WeekDaysModel value, $Res Function(_WeekDaysModel) _then) = __$WeekDaysModelCopyWithImpl;
@override @useResult
$Res call({
 int? id, int? sun, int? mon, int? tue, int? wed, int? thu, int? fri, int? sat
});




}
/// @nodoc
class __$WeekDaysModelCopyWithImpl<$Res>
    implements _$WeekDaysModelCopyWith<$Res> {
  __$WeekDaysModelCopyWithImpl(this._self, this._then);

  final _WeekDaysModel _self;
  final $Res Function(_WeekDaysModel) _then;

/// Create a copy of WeekDaysModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? sun = freezed,Object? mon = freezed,Object? tue = freezed,Object? wed = freezed,Object? thu = freezed,Object? fri = freezed,Object? sat = freezed,}) {
  return _then(_WeekDaysModel(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,sun: freezed == sun ? _self.sun : sun // ignore: cast_nullable_to_non_nullable
as int?,mon: freezed == mon ? _self.mon : mon // ignore: cast_nullable_to_non_nullable
as int?,tue: freezed == tue ? _self.tue : tue // ignore: cast_nullable_to_non_nullable
as int?,wed: freezed == wed ? _self.wed : wed // ignore: cast_nullable_to_non_nullable
as int?,thu: freezed == thu ? _self.thu : thu // ignore: cast_nullable_to_non_nullable
as int?,fri: freezed == fri ? _self.fri : fri // ignore: cast_nullable_to_non_nullable
as int?,sat: freezed == sat ? _self.sat : sat // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}


/// @nodoc
mixin _$AlreadyPresentRequest {

 int get id; String get type; DateTime get startDate; DateTime get endDate; String get status;
/// Create a copy of AlreadyPresentRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AlreadyPresentRequestCopyWith<AlreadyPresentRequest> get copyWith => _$AlreadyPresentRequestCopyWithImpl<AlreadyPresentRequest>(this as AlreadyPresentRequest, _$identity);

  /// Serializes this AlreadyPresentRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AlreadyPresentRequest&&(identical(other.id, id) || other.id == id)&&(identical(other.type, type) || other.type == type)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.status, status) || other.status == status));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,type,startDate,endDate,status);

@override
String toString() {
  return 'AlreadyPresentRequest(id: $id, type: $type, startDate: $startDate, endDate: $endDate, status: $status)';
}


}

/// @nodoc
abstract mixin class $AlreadyPresentRequestCopyWith<$Res>  {
  factory $AlreadyPresentRequestCopyWith(AlreadyPresentRequest value, $Res Function(AlreadyPresentRequest) _then) = _$AlreadyPresentRequestCopyWithImpl;
@useResult
$Res call({
 int id, String type, DateTime startDate, DateTime endDate, String status
});




}
/// @nodoc
class _$AlreadyPresentRequestCopyWithImpl<$Res>
    implements $AlreadyPresentRequestCopyWith<$Res> {
  _$AlreadyPresentRequestCopyWithImpl(this._self, this._then);

  final AlreadyPresentRequest _self;
  final $Res Function(AlreadyPresentRequest) _then;

/// Create a copy of AlreadyPresentRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? type = null,Object? startDate = null,Object? endDate = null,Object? status = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _AlreadyPresentRequest implements AlreadyPresentRequest {
  const _AlreadyPresentRequest({required this.id, required this.type, required this.startDate, required this.endDate, required this.status});
  factory _AlreadyPresentRequest.fromJson(Map<String, dynamic> json) => _$AlreadyPresentRequestFromJson(json);

@override final  int id;
@override final  String type;
@override final  DateTime startDate;
@override final  DateTime endDate;
@override final  String status;

/// Create a copy of AlreadyPresentRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AlreadyPresentRequestCopyWith<_AlreadyPresentRequest> get copyWith => __$AlreadyPresentRequestCopyWithImpl<_AlreadyPresentRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AlreadyPresentRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AlreadyPresentRequest&&(identical(other.id, id) || other.id == id)&&(identical(other.type, type) || other.type == type)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.status, status) || other.status == status));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,type,startDate,endDate,status);

@override
String toString() {
  return 'AlreadyPresentRequest(id: $id, type: $type, startDate: $startDate, endDate: $endDate, status: $status)';
}


}

/// @nodoc
abstract mixin class _$AlreadyPresentRequestCopyWith<$Res> implements $AlreadyPresentRequestCopyWith<$Res> {
  factory _$AlreadyPresentRequestCopyWith(_AlreadyPresentRequest value, $Res Function(_AlreadyPresentRequest) _then) = __$AlreadyPresentRequestCopyWithImpl;
@override @useResult
$Res call({
 int id, String type, DateTime startDate, DateTime endDate, String status
});




}
/// @nodoc
class __$AlreadyPresentRequestCopyWithImpl<$Res>
    implements _$AlreadyPresentRequestCopyWith<$Res> {
  __$AlreadyPresentRequestCopyWithImpl(this._self, this._then);

  final _AlreadyPresentRequest _self;
  final $Res Function(_AlreadyPresentRequest) _then;

/// Create a copy of AlreadyPresentRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? type = null,Object? startDate = null,Object? endDate = null,Object? status = null,}) {
  return _then(_AlreadyPresentRequest(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
