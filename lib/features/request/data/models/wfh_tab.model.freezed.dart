// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'wfh_tab.model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WFHRecordModel {

 int get id; String? get type; DateTime? get startDate; DateTime? get endDate; String? get startDaySlot; String? get endDaySlot; String? get reason; String? get rejectReason; String? get status; DateTime? get createdAt;@JsonKey(name: 'reviewer', fromJson: reviewerFromJson) LeaveReviewerModel? get reviewer;
/// Create a copy of WFHRecordModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WFHRecordModelCopyWith<WFHRecordModel> get copyWith => _$WFHRecordModelCopyWithImpl<WFHRecordModel>(this as WFHRecordModel, _$identity);

  /// Serializes this WFHRecordModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WFHRecordModel&&(identical(other.id, id) || other.id == id)&&(identical(other.type, type) || other.type == type)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.startDaySlot, startDaySlot) || other.startDaySlot == startDaySlot)&&(identical(other.endDaySlot, endDaySlot) || other.endDaySlot == endDaySlot)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.rejectReason, rejectReason) || other.rejectReason == rejectReason)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.reviewer, reviewer) || other.reviewer == reviewer));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,type,startDate,endDate,startDaySlot,endDaySlot,reason,rejectReason,status,createdAt,reviewer);

@override
String toString() {
  return 'WFHRecordModel(id: $id, type: $type, startDate: $startDate, endDate: $endDate, startDaySlot: $startDaySlot, endDaySlot: $endDaySlot, reason: $reason, rejectReason: $rejectReason, status: $status, createdAt: $createdAt, reviewer: $reviewer)';
}


}

/// @nodoc
abstract mixin class $WFHRecordModelCopyWith<$Res>  {
  factory $WFHRecordModelCopyWith(WFHRecordModel value, $Res Function(WFHRecordModel) _then) = _$WFHRecordModelCopyWithImpl;
@useResult
$Res call({
 int id, String? type, DateTime? startDate, DateTime? endDate, String? startDaySlot, String? endDaySlot, String? reason, String? rejectReason, String? status, DateTime? createdAt,@JsonKey(name: 'reviewer', fromJson: reviewerFromJson) LeaveReviewerModel? reviewer
});


$LeaveReviewerModelCopyWith<$Res>? get reviewer;

}
/// @nodoc
class _$WFHRecordModelCopyWithImpl<$Res>
    implements $WFHRecordModelCopyWith<$Res> {
  _$WFHRecordModelCopyWithImpl(this._self, this._then);

  final WFHRecordModel _self;
  final $Res Function(WFHRecordModel) _then;

/// Create a copy of WFHRecordModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? type = freezed,Object? startDate = freezed,Object? endDate = freezed,Object? startDaySlot = freezed,Object? endDaySlot = freezed,Object? reason = freezed,Object? rejectReason = freezed,Object? status = freezed,Object? createdAt = freezed,Object? reviewer = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,type: freezed == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String?,startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,startDaySlot: freezed == startDaySlot ? _self.startDaySlot : startDaySlot // ignore: cast_nullable_to_non_nullable
as String?,endDaySlot: freezed == endDaySlot ? _self.endDaySlot : endDaySlot // ignore: cast_nullable_to_non_nullable
as String?,reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,rejectReason: freezed == rejectReason ? _self.rejectReason : rejectReason // ignore: cast_nullable_to_non_nullable
as String?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,reviewer: freezed == reviewer ? _self.reviewer : reviewer // ignore: cast_nullable_to_non_nullable
as LeaveReviewerModel?,
  ));
}
/// Create a copy of WFHRecordModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeaveReviewerModelCopyWith<$Res>? get reviewer {
    if (_self.reviewer == null) {
    return null;
  }

  return $LeaveReviewerModelCopyWith<$Res>(_self.reviewer!, (value) {
    return _then(_self.copyWith(reviewer: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _WFHRecordModel implements WFHRecordModel {
  const _WFHRecordModel({required this.id, this.type, this.startDate, this.endDate, this.startDaySlot, this.endDaySlot, this.reason, this.rejectReason, this.status, this.createdAt, @JsonKey(name: 'reviewer', fromJson: reviewerFromJson) this.reviewer});
  factory _WFHRecordModel.fromJson(Map<String, dynamic> json) => _$WFHRecordModelFromJson(json);

@override final  int id;
@override final  String? type;
@override final  DateTime? startDate;
@override final  DateTime? endDate;
@override final  String? startDaySlot;
@override final  String? endDaySlot;
@override final  String? reason;
@override final  String? rejectReason;
@override final  String? status;
@override final  DateTime? createdAt;
@override@JsonKey(name: 'reviewer', fromJson: reviewerFromJson) final  LeaveReviewerModel? reviewer;

/// Create a copy of WFHRecordModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WFHRecordModelCopyWith<_WFHRecordModel> get copyWith => __$WFHRecordModelCopyWithImpl<_WFHRecordModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WFHRecordModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WFHRecordModel&&(identical(other.id, id) || other.id == id)&&(identical(other.type, type) || other.type == type)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.startDaySlot, startDaySlot) || other.startDaySlot == startDaySlot)&&(identical(other.endDaySlot, endDaySlot) || other.endDaySlot == endDaySlot)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.rejectReason, rejectReason) || other.rejectReason == rejectReason)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.reviewer, reviewer) || other.reviewer == reviewer));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,type,startDate,endDate,startDaySlot,endDaySlot,reason,rejectReason,status,createdAt,reviewer);

@override
String toString() {
  return 'WFHRecordModel(id: $id, type: $type, startDate: $startDate, endDate: $endDate, startDaySlot: $startDaySlot, endDaySlot: $endDaySlot, reason: $reason, rejectReason: $rejectReason, status: $status, createdAt: $createdAt, reviewer: $reviewer)';
}


}

/// @nodoc
abstract mixin class _$WFHRecordModelCopyWith<$Res> implements $WFHRecordModelCopyWith<$Res> {
  factory _$WFHRecordModelCopyWith(_WFHRecordModel value, $Res Function(_WFHRecordModel) _then) = __$WFHRecordModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String? type, DateTime? startDate, DateTime? endDate, String? startDaySlot, String? endDaySlot, String? reason, String? rejectReason, String? status, DateTime? createdAt,@JsonKey(name: 'reviewer', fromJson: reviewerFromJson) LeaveReviewerModel? reviewer
});


@override $LeaveReviewerModelCopyWith<$Res>? get reviewer;

}
/// @nodoc
class __$WFHRecordModelCopyWithImpl<$Res>
    implements _$WFHRecordModelCopyWith<$Res> {
  __$WFHRecordModelCopyWithImpl(this._self, this._then);

  final _WFHRecordModel _self;
  final $Res Function(_WFHRecordModel) _then;

/// Create a copy of WFHRecordModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? type = freezed,Object? startDate = freezed,Object? endDate = freezed,Object? startDaySlot = freezed,Object? endDaySlot = freezed,Object? reason = freezed,Object? rejectReason = freezed,Object? status = freezed,Object? createdAt = freezed,Object? reviewer = freezed,}) {
  return _then(_WFHRecordModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,type: freezed == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String?,startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,startDaySlot: freezed == startDaySlot ? _self.startDaySlot : startDaySlot // ignore: cast_nullable_to_non_nullable
as String?,endDaySlot: freezed == endDaySlot ? _self.endDaySlot : endDaySlot // ignore: cast_nullable_to_non_nullable
as String?,reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,rejectReason: freezed == rejectReason ? _self.rejectReason : rejectReason // ignore: cast_nullable_to_non_nullable
as String?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,reviewer: freezed == reviewer ? _self.reviewer : reviewer // ignore: cast_nullable_to_non_nullable
as LeaveReviewerModel?,
  ));
}

/// Create a copy of WFHRecordModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeaveReviewerModelCopyWith<$Res>? get reviewer {
    if (_self.reviewer == null) {
    return null;
  }

  return $LeaveReviewerModelCopyWith<$Res>(_self.reviewer!, (value) {
    return _then(_self.copyWith(reviewer: value));
  });
}
}

// dart format on
