// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wfh_tab.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WFHRecordModel _$WFHRecordModelFromJson(Map<String, dynamic> json) =>
    _WFHRecordModel(
      id: (json['id'] as num).toInt(),
      type: json['type'] as String?,
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      startDaySlot: json['startDaySlot'] as String?,
      endDaySlot: json['endDaySlot'] as String?,
      reason: json['reason'] as String?,
      rejectReason: json['rejectReason'] as String?,
      status: json['status'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      reviewer: reviewerFromJson(json['reviewer'] as Map<String, dynamic>?),
    );

Map<String, dynamic> _$WFHRecordModelToJson(_WFHRecordModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'startDate': instance.startDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'startDaySlot': instance.startDaySlot,
      'endDaySlot': instance.endDaySlot,
      'reason': instance.reason,
      'rejectReason': instance.rejectReason,
      'status': instance.status,
      'createdAt': instance.createdAt?.toIso8601String(),
      'reviewer': instance.reviewer,
    };
