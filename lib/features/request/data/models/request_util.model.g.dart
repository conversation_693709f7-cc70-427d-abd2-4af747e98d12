// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'request_util.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_DisabledDatesModel _$DisabledDatesModelFromJson(Map<String, dynamic> json) =>
    _DisabledDatesModel(
      timetable: WeekDaysModel.fromJson(
        json['timetable'] as Map<String, dynamic>,
      ),
      holidays: (json['holidays'] as List<dynamic>)
          .map((e) => HolidayTileData.fromJson(e as Map<String, dynamic>))
          .toList(),
      requests: (json['leaveWfhRequests'] as List<dynamic>)
          .map((e) => AlreadyPresentRequest.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$DisabledDatesModelToJson(_DisabledDatesModel instance) =>
    <String, dynamic>{
      'timetable': instance.timetable,
      'holidays': instance.holidays,
      'leaveWfhRequests': instance.requests,
    };

_WeekDaysModel _$WeekDaysModelFromJson(Map<String, dynamic> json) =>
    _WeekDaysModel(
      id: (json['id'] as num?)?.toInt(),
      sun: (json['sun'] as num?)?.toInt(),
      mon: (json['mon'] as num?)?.toInt(),
      tue: (json['tue'] as num?)?.toInt(),
      wed: (json['wed'] as num?)?.toInt(),
      thu: (json['thu'] as num?)?.toInt(),
      fri: (json['fri'] as num?)?.toInt(),
      sat: (json['sat'] as num?)?.toInt(),
    );

Map<String, dynamic> _$WeekDaysModelToJson(_WeekDaysModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sun': instance.sun,
      'mon': instance.mon,
      'tue': instance.tue,
      'wed': instance.wed,
      'thu': instance.thu,
      'fri': instance.fri,
      'sat': instance.sat,
    };

_AlreadyPresentRequest _$AlreadyPresentRequestFromJson(
  Map<String, dynamic> json,
) => _AlreadyPresentRequest(
  id: (json['id'] as num).toInt(),
  type: json['type'] as String,
  startDate: DateTime.parse(json['startDate'] as String),
  endDate: DateTime.parse(json['endDate'] as String),
  status: json['status'] as String,
);

Map<String, dynamic> _$AlreadyPresentRequestToJson(
  _AlreadyPresentRequest instance,
) => <String, dynamic>{
  'id': instance.id,
  'type': instance.type,
  'startDate': instance.startDate.toIso8601String(),
  'endDate': instance.endDate.toIso8601String(),
  'status': instance.status,
};
