import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../employee/data/models/leave_tab.model.dart';

part 'wfh_tab.model.freezed.dart';
part 'wfh_tab.model.g.dart';

@freezed
abstract class WFHRecordModel with _$WFHRecordModel {
  const factory WFHRecordModel({
    required final int id,
    final String? type,
    final DateTime? startDate,
    final DateTime? endDate,
    final String? startDaySlot,
    final String? endDaySlot,
    final String? reason,
    final String? rejectReason,
    final String? status,
    final DateTime? createdAt,
    @Json<PERSON>ey(name: 'reviewer', fromJson: reviewerFromJson)
    final LeaveReviewerModel? reviewer,
  }) = _WFHRecordModel;

  factory WFHRecordModel.fromJson(Map<String, dynamic> json) =>
      _$WFHRecordModelFromJson(json);
}
