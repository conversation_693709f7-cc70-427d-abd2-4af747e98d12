import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/features/home/<USER>/models/leave_balance.model.dart';
import 'package:hrms_tst/features/request/data/models/request_util.model.dart';
import 'package:hrms_tst/features/request/data/models/wfh_tab.model.dart';

import '../domain/leave_request.state.dart';
import '../domain/wfh_request.state.dart';
import 'models/approval_tab.model.dart';

abstract interface class RequestProtocol {
  Future<Result<DisabledDatesModel>> getDisabledDates();
  Future<Result<List<AvailableLeaveType>>> getAvailableLeaveTypes({
    required DateTime startDate,
    required DateTime endDate,
  });

  Future<Result<void>> requestLeave({required LeaveRequestState leaveRequest});
  Future<Result<void>> requestWFH({required WFHRequestState wfhRequest});

  Future<Result<List<WFHRecordModel>>> getWFHRecords({
    required DateTime? fromDate,
    required DateTime? toDate,
    required String? status,
  });
  Future<Result<List<ApprovalRecordModel>>> getApprovalRecords({
    required DateTime? fromDate,
    required DateTime? toDate,
    required String? type,
    required int? leaveTypeId,
  });

  Future<Result<void>> deleteRequest({required int requestId});
}
