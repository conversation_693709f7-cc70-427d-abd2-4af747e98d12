import 'package:hrms_tst/core/services/network/network.service.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:hrms_tst/features/home/<USER>/models/leave_balance.model.dart';
import 'package:hrms_tst/features/request/data/models/approval_tab.model.dart';
import 'package:hrms_tst/features/request/data/models/wfh_tab.model.dart';
import 'package:hrms_tst/features/request/domain/wfh_request.state.dart';

import '../domain/leave_request.state.dart';
import 'models/request_util.model.dart';
import 'request.protocol.dart';

class RequestRepository implements RequestProtocol {
  final networkService = getIt.get<NetworkService>();

  @override
  Future<Result<List<WFHRecordModel>>> getWFHRecords({
    required DateTime? fromDate,
    required DateTime? toDate,
    required String? status,
  }) async {
    return await networkService.get(
      '/leave-wfh-requests/my',
      queryParameters: {
        'type': 'wfh',
        if (fromDate != null) 'fromDate': fromDate.toIso8601String(),
        if (toDate != null) 'toDate': toDate.toIso8601String(),
        if (status != null) 'status': status,
      },
      mapper: (response) {
        return (response.data['data']['leaveWfhRequestRecords']
                as List<dynamic>)
            .map((e) => WFHRecordModel.fromJson(e))
            .toList();
      },
    );
  }

  @override
  Future<Result<List<ApprovalRecordModel>>> getApprovalRecords({
    required DateTime? fromDate,
    required DateTime? toDate,
    required String? type,
    required int? leaveTypeId,
  }) async {
    return await networkService.get(
      '/leave-wfh-requests/my',
      queryParameters: {
        'status': 'approved',
        if (type != null) 'type': type,
        if (type == 'leave' && leaveTypeId != null) 'LeaveTypeId': leaveTypeId,
        if (fromDate != null) 'fromDate': fromDate.toIso8601String(),
        if (toDate != null) 'toDate': toDate.toIso8601String(),
      },
      mapper: (response) {
        return (response.data['data']['leaveWfhRequestRecords']
                as List<dynamic>)
            .map((e) => ApprovalRecordModel.fromJson(e))
            .toList();
      },
    );
  }

  @override
  Future<Result<DisabledDatesModel>> getDisabledDates() async {
    return await networkService.get(
      '/general/disabled-dates-for-requests',
      mapper: (response) {
        return DisabledDatesModel.fromJson(response.data['data']);
      },
    );
  }

  @override
  Future<Result<List<AvailableLeaveType>>> getAvailableLeaveTypes({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    return await networkService.get(
      '/leave-wfh-requests/my-balance',
      queryParameters: {
        'date': startDate.toIso8601String(),
        'monthEndDate': endDate.toIso8601String(),
      },
      mapper: (response) {
        return (response.data['data']['balance']['availableLeaveTypes']
                as List<dynamic>)
            .map((e) => AvailableLeaveType.fromJson(e))
            .toList();
      },
    );
  }

  @override
  Future<Result<void>> requestLeave({required LeaveRequestState leaveRequest}) {
    return networkService.post(
      '/leave-wfh-requests',
      data: {
        'type': 'leave',
        'LeaveTypeId': leaveRequest.leaveType?.id,
        'startDate': leaveRequest.startDate!.toIso8601String(),
        'endDate': leaveRequest.endDate!.toIso8601String(),
        'reason': leaveRequest.reason,
        'totalDays':
            leaveRequest.endDate!.difference(leaveRequest.startDate!).inDays +
            1,
        'startDaySlot': leaveRequest.startDaySlot,
        'endDaySlot': leaveRequest.endDaySlot,
        'notifyTo': leaveRequest.notifyTo.map((e) => e.id).toList(),
      },
      mapper: (response) {
        return;
      },
    );
  }

  @override
  Future<Result<void>> requestWFH({required WFHRequestState wfhRequest}) async {
    return networkService.post(
      '/leave-wfh-requests',
      data: {
        'type': 'wfh',
        'startDate': wfhRequest.startDate!.toIso8601String(),
        'endDate': wfhRequest.endDate!.toIso8601String(),
        'reason': wfhRequest.reason,
        'totalDays':
            wfhRequest.endDate!.difference(wfhRequest.startDate!).inDays + 1,
        'startDaySlot': wfhRequest.startDaySlot,
        'endDaySlot': wfhRequest.endDaySlot,
        'notifyTo': wfhRequest.notifyTo.map((e) => e.id).toList(),
      },
      mapper: (response) {
        return;
      },
    );
  }

  @override
  Future<Result<void>> deleteRequest({required int requestId}) async {
    return networkService.delete(
      '/leave-wfh-requests/my/$requestId',
      mapper: (response) {
        return;
      },
    );
  }
}
