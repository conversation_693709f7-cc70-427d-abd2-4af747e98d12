// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'request.controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$wfhRecordsHash() => r'5c7259cd684b91e3db2e47bef2863eaf8cdacc5d';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [wfhRecords].
@ProviderFor(wfhRecords)
const wfhRecordsProvider = WfhRecordsFamily();

/// See also [wfhRecords].
class WfhRecordsFamily extends Family<AsyncValue<List<WFHRecordModel>>> {
  /// See also [wfhRecords].
  const WfhRecordsFamily();

  /// See also [wfhRecords].
  WfhRecordsProvider call({
    DateTime? fromDate,
    DateTime? toDate,
    String? status,
  }) {
    return WfhRecordsProvider(
      fromDate: fromDate,
      toDate: toDate,
      status: status,
    );
  }

  @override
  WfhRecordsProvider getProviderOverride(
    covariant WfhRecordsProvider provider,
  ) {
    return call(
      fromDate: provider.fromDate,
      toDate: provider.toDate,
      status: provider.status,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'wfhRecordsProvider';
}

/// See also [wfhRecords].
class WfhRecordsProvider extends FutureProvider<List<WFHRecordModel>> {
  /// See also [wfhRecords].
  WfhRecordsProvider({DateTime? fromDate, DateTime? toDate, String? status})
    : this._internal(
        (ref) => wfhRecords(
          ref as WfhRecordsRef,
          fromDate: fromDate,
          toDate: toDate,
          status: status,
        ),
        from: wfhRecordsProvider,
        name: r'wfhRecordsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$wfhRecordsHash,
        dependencies: WfhRecordsFamily._dependencies,
        allTransitiveDependencies: WfhRecordsFamily._allTransitiveDependencies,
        fromDate: fromDate,
        toDate: toDate,
        status: status,
      );

  WfhRecordsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.fromDate,
    required this.toDate,
    required this.status,
  }) : super.internal();

  final DateTime? fromDate;
  final DateTime? toDate;
  final String? status;

  @override
  Override overrideWith(
    FutureOr<List<WFHRecordModel>> Function(WfhRecordsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: WfhRecordsProvider._internal(
        (ref) => create(ref as WfhRecordsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        fromDate: fromDate,
        toDate: toDate,
        status: status,
      ),
    );
  }

  @override
  FutureProviderElement<List<WFHRecordModel>> createElement() {
    return _WfhRecordsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is WfhRecordsProvider &&
        other.fromDate == fromDate &&
        other.toDate == toDate &&
        other.status == status;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, fromDate.hashCode);
    hash = _SystemHash.combine(hash, toDate.hashCode);
    hash = _SystemHash.combine(hash, status.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin WfhRecordsRef on FutureProviderRef<List<WFHRecordModel>> {
  /// The parameter `fromDate` of this provider.
  DateTime? get fromDate;

  /// The parameter `toDate` of this provider.
  DateTime? get toDate;

  /// The parameter `status` of this provider.
  String? get status;
}

class _WfhRecordsProviderElement
    extends FutureProviderElement<List<WFHRecordModel>>
    with WfhRecordsRef {
  _WfhRecordsProviderElement(super.provider);

  @override
  DateTime? get fromDate => (origin as WfhRecordsProvider).fromDate;
  @override
  DateTime? get toDate => (origin as WfhRecordsProvider).toDate;
  @override
  String? get status => (origin as WfhRecordsProvider).status;
}

String _$approvalRecordsHash() => r'f163805285f1587a451a427866162cad8a4ad44f';

/// See also [approvalRecords].
@ProviderFor(approvalRecords)
const approvalRecordsProvider = ApprovalRecordsFamily();

/// See also [approvalRecords].
class ApprovalRecordsFamily
    extends Family<AsyncValue<List<ApprovalRecordModel>>> {
  /// See also [approvalRecords].
  const ApprovalRecordsFamily();

  /// See also [approvalRecords].
  ApprovalRecordsProvider call(
    DateTime? fromDate,
    DateTime? toDate,
    String? type,
    int? leaveTypeId,
  ) {
    return ApprovalRecordsProvider(fromDate, toDate, type, leaveTypeId);
  }

  @override
  ApprovalRecordsProvider getProviderOverride(
    covariant ApprovalRecordsProvider provider,
  ) {
    return call(
      provider.fromDate,
      provider.toDate,
      provider.type,
      provider.leaveTypeId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'approvalRecordsProvider';
}

/// See also [approvalRecords].
class ApprovalRecordsProvider
    extends FutureProvider<List<ApprovalRecordModel>> {
  /// See also [approvalRecords].
  ApprovalRecordsProvider(
    DateTime? fromDate,
    DateTime? toDate,
    String? type,
    int? leaveTypeId,
  ) : this._internal(
        (ref) => approvalRecords(
          ref as ApprovalRecordsRef,
          fromDate,
          toDate,
          type,
          leaveTypeId,
        ),
        from: approvalRecordsProvider,
        name: r'approvalRecordsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$approvalRecordsHash,
        dependencies: ApprovalRecordsFamily._dependencies,
        allTransitiveDependencies:
            ApprovalRecordsFamily._allTransitiveDependencies,
        fromDate: fromDate,
        toDate: toDate,
        type: type,
        leaveTypeId: leaveTypeId,
      );

  ApprovalRecordsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.fromDate,
    required this.toDate,
    required this.type,
    required this.leaveTypeId,
  }) : super.internal();

  final DateTime? fromDate;
  final DateTime? toDate;
  final String? type;
  final int? leaveTypeId;

  @override
  Override overrideWith(
    FutureOr<List<ApprovalRecordModel>> Function(ApprovalRecordsRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ApprovalRecordsProvider._internal(
        (ref) => create(ref as ApprovalRecordsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        fromDate: fromDate,
        toDate: toDate,
        type: type,
        leaveTypeId: leaveTypeId,
      ),
    );
  }

  @override
  FutureProviderElement<List<ApprovalRecordModel>> createElement() {
    return _ApprovalRecordsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ApprovalRecordsProvider &&
        other.fromDate == fromDate &&
        other.toDate == toDate &&
        other.type == type &&
        other.leaveTypeId == leaveTypeId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, fromDate.hashCode);
    hash = _SystemHash.combine(hash, toDate.hashCode);
    hash = _SystemHash.combine(hash, type.hashCode);
    hash = _SystemHash.combine(hash, leaveTypeId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ApprovalRecordsRef on FutureProviderRef<List<ApprovalRecordModel>> {
  /// The parameter `fromDate` of this provider.
  DateTime? get fromDate;

  /// The parameter `toDate` of this provider.
  DateTime? get toDate;

  /// The parameter `type` of this provider.
  String? get type;

  /// The parameter `leaveTypeId` of this provider.
  int? get leaveTypeId;
}

class _ApprovalRecordsProviderElement
    extends FutureProviderElement<List<ApprovalRecordModel>>
    with ApprovalRecordsRef {
  _ApprovalRecordsProviderElement(super.provider);

  @override
  DateTime? get fromDate => (origin as ApprovalRecordsProvider).fromDate;
  @override
  DateTime? get toDate => (origin as ApprovalRecordsProvider).toDate;
  @override
  String? get type => (origin as ApprovalRecordsProvider).type;
  @override
  int? get leaveTypeId => (origin as ApprovalRecordsProvider).leaveTypeId;
}

String _$leaveRequestHash() => r'664000950c65c7154680f8daae983d8418dccf25';

/// See also [LeaveRequest].
@ProviderFor(LeaveRequest)
final leaveRequestProvider =
    AutoDisposeNotifierProvider<LeaveRequest, LeaveRequestState>.internal(
      LeaveRequest.new,
      name: r'leaveRequestProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$leaveRequestHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$LeaveRequest = AutoDisposeNotifier<LeaveRequestState>;
String _$wfhRequestHash() => r'284add2706a94de7cf6f93f423683a3ca64e9c5f';

/// See also [WfhRequest].
@ProviderFor(WfhRequest)
final wfhRequestProvider =
    AutoDisposeNotifierProvider<WfhRequest, WFHRequestState>.internal(
      WfhRequest.new,
      name: r'wfhRequestProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$wfhRequestHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$WfhRequest = AutoDisposeNotifier<WFHRequestState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
