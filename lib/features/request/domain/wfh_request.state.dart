import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hrms_tst/features/teammates/data/models/teammate.model.dart';

part 'wfh_request.state.freezed.dart';

@freezed
abstract class WFHRequestState with _$WFHRequestState {
  const factory WFHRequestState({
    required final DateTime? startDate,
    required final DateTime? endDate,
    required final List<TeammateModel> availableTeammates,
    required final List<TeammateModel> notifyTo,
    required final String reason,
    required final bool Function(DateTime date) isDateSelectable,
    required final String? startDaySlot,
    required final String? endDaySlot,
  }) = _WFHRequestState;
}
