// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'wfh_request.state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$WFHRequestState {

 DateTime? get startDate; DateTime? get endDate; List<TeammateModel> get availableTeammates; List<TeammateModel> get notifyTo; String get reason; bool Function(DateTime date) get isDateSelectable; String? get startDaySlot; String? get endDaySlot;
/// Create a copy of WFHRequestState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WFHRequestStateCopyWith<WFHRequestState> get copyWith => _$WFHRequestStateCopyWithImpl<WFHRequestState>(this as WFHRequestState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WFHRequestState&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&const DeepCollectionEquality().equals(other.availableTeammates, availableTeammates)&&const DeepCollectionEquality().equals(other.notifyTo, notifyTo)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.isDateSelectable, isDateSelectable) || other.isDateSelectable == isDateSelectable)&&(identical(other.startDaySlot, startDaySlot) || other.startDaySlot == startDaySlot)&&(identical(other.endDaySlot, endDaySlot) || other.endDaySlot == endDaySlot));
}


@override
int get hashCode => Object.hash(runtimeType,startDate,endDate,const DeepCollectionEquality().hash(availableTeammates),const DeepCollectionEquality().hash(notifyTo),reason,isDateSelectable,startDaySlot,endDaySlot);

@override
String toString() {
  return 'WFHRequestState(startDate: $startDate, endDate: $endDate, availableTeammates: $availableTeammates, notifyTo: $notifyTo, reason: $reason, isDateSelectable: $isDateSelectable, startDaySlot: $startDaySlot, endDaySlot: $endDaySlot)';
}


}

/// @nodoc
abstract mixin class $WFHRequestStateCopyWith<$Res>  {
  factory $WFHRequestStateCopyWith(WFHRequestState value, $Res Function(WFHRequestState) _then) = _$WFHRequestStateCopyWithImpl;
@useResult
$Res call({
 DateTime? startDate, DateTime? endDate, List<TeammateModel> availableTeammates, List<TeammateModel> notifyTo, String reason, bool Function(DateTime date) isDateSelectable, String? startDaySlot, String? endDaySlot
});




}
/// @nodoc
class _$WFHRequestStateCopyWithImpl<$Res>
    implements $WFHRequestStateCopyWith<$Res> {
  _$WFHRequestStateCopyWithImpl(this._self, this._then);

  final WFHRequestState _self;
  final $Res Function(WFHRequestState) _then;

/// Create a copy of WFHRequestState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? startDate = freezed,Object? endDate = freezed,Object? availableTeammates = null,Object? notifyTo = null,Object? reason = null,Object? isDateSelectable = null,Object? startDaySlot = freezed,Object? endDaySlot = freezed,}) {
  return _then(_self.copyWith(
startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,availableTeammates: null == availableTeammates ? _self.availableTeammates : availableTeammates // ignore: cast_nullable_to_non_nullable
as List<TeammateModel>,notifyTo: null == notifyTo ? _self.notifyTo : notifyTo // ignore: cast_nullable_to_non_nullable
as List<TeammateModel>,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,isDateSelectable: null == isDateSelectable ? _self.isDateSelectable : isDateSelectable // ignore: cast_nullable_to_non_nullable
as bool Function(DateTime date),startDaySlot: freezed == startDaySlot ? _self.startDaySlot : startDaySlot // ignore: cast_nullable_to_non_nullable
as String?,endDaySlot: freezed == endDaySlot ? _self.endDaySlot : endDaySlot // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc


class _WFHRequestState implements WFHRequestState {
  const _WFHRequestState({required this.startDate, required this.endDate, required final  List<TeammateModel> availableTeammates, required final  List<TeammateModel> notifyTo, required this.reason, required this.isDateSelectable, required this.startDaySlot, required this.endDaySlot}): _availableTeammates = availableTeammates,_notifyTo = notifyTo;
  

@override final  DateTime? startDate;
@override final  DateTime? endDate;
 final  List<TeammateModel> _availableTeammates;
@override List<TeammateModel> get availableTeammates {
  if (_availableTeammates is EqualUnmodifiableListView) return _availableTeammates;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_availableTeammates);
}

 final  List<TeammateModel> _notifyTo;
@override List<TeammateModel> get notifyTo {
  if (_notifyTo is EqualUnmodifiableListView) return _notifyTo;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_notifyTo);
}

@override final  String reason;
@override final  bool Function(DateTime date) isDateSelectable;
@override final  String? startDaySlot;
@override final  String? endDaySlot;

/// Create a copy of WFHRequestState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WFHRequestStateCopyWith<_WFHRequestState> get copyWith => __$WFHRequestStateCopyWithImpl<_WFHRequestState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WFHRequestState&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&const DeepCollectionEquality().equals(other._availableTeammates, _availableTeammates)&&const DeepCollectionEquality().equals(other._notifyTo, _notifyTo)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.isDateSelectable, isDateSelectable) || other.isDateSelectable == isDateSelectable)&&(identical(other.startDaySlot, startDaySlot) || other.startDaySlot == startDaySlot)&&(identical(other.endDaySlot, endDaySlot) || other.endDaySlot == endDaySlot));
}


@override
int get hashCode => Object.hash(runtimeType,startDate,endDate,const DeepCollectionEquality().hash(_availableTeammates),const DeepCollectionEquality().hash(_notifyTo),reason,isDateSelectable,startDaySlot,endDaySlot);

@override
String toString() {
  return 'WFHRequestState(startDate: $startDate, endDate: $endDate, availableTeammates: $availableTeammates, notifyTo: $notifyTo, reason: $reason, isDateSelectable: $isDateSelectable, startDaySlot: $startDaySlot, endDaySlot: $endDaySlot)';
}


}

/// @nodoc
abstract mixin class _$WFHRequestStateCopyWith<$Res> implements $WFHRequestStateCopyWith<$Res> {
  factory _$WFHRequestStateCopyWith(_WFHRequestState value, $Res Function(_WFHRequestState) _then) = __$WFHRequestStateCopyWithImpl;
@override @useResult
$Res call({
 DateTime? startDate, DateTime? endDate, List<TeammateModel> availableTeammates, List<TeammateModel> notifyTo, String reason, bool Function(DateTime date) isDateSelectable, String? startDaySlot, String? endDaySlot
});




}
/// @nodoc
class __$WFHRequestStateCopyWithImpl<$Res>
    implements _$WFHRequestStateCopyWith<$Res> {
  __$WFHRequestStateCopyWithImpl(this._self, this._then);

  final _WFHRequestState _self;
  final $Res Function(_WFHRequestState) _then;

/// Create a copy of WFHRequestState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? startDate = freezed,Object? endDate = freezed,Object? availableTeammates = null,Object? notifyTo = null,Object? reason = null,Object? isDateSelectable = null,Object? startDaySlot = freezed,Object? endDaySlot = freezed,}) {
  return _then(_WFHRequestState(
startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,availableTeammates: null == availableTeammates ? _self._availableTeammates : availableTeammates // ignore: cast_nullable_to_non_nullable
as List<TeammateModel>,notifyTo: null == notifyTo ? _self._notifyTo : notifyTo // ignore: cast_nullable_to_non_nullable
as List<TeammateModel>,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,isDateSelectable: null == isDateSelectable ? _self.isDateSelectable : isDateSelectable // ignore: cast_nullable_to_non_nullable
as bool Function(DateTime date),startDaySlot: freezed == startDaySlot ? _self.startDaySlot : startDaySlot // ignore: cast_nullable_to_non_nullable
as String?,endDaySlot: freezed == endDaySlot ? _self.endDaySlot : endDaySlot // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
