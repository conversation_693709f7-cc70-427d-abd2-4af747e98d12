import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hrms_tst/features/home/<USER>/models/leave_balance.model.dart';
import 'package:hrms_tst/features/teammates/data/models/teammate.model.dart';

part 'leave_request.state.freezed.dart';

@freezed
abstract class LeaveRequestState with _$LeaveRequestState {
  const factory LeaveRequestState({
    required final DateTime? startDate,
    required final DateTime? endDate,
    required final AvailableLeaveType? leaveType,
    required final List<AvailableLeaveType> availableLeaveTypes,
    required final List<TeammateModel> availableTeammates,
    required final List<TeammateModel> notifyTo,
    required final String reason,
    required final bool Function(DateTime date) isDateSelectable,
    required final String? startDaySlot,
    required final String? endDaySlot,
  }) = _LeaveRequestState;
}
