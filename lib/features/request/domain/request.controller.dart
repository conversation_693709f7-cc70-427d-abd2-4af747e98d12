import 'dart:developer';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:hrms_tst/features/employee/domain/employee.controller.dart';
import 'package:hrms_tst/features/home/<USER>/home.controller.dart';
import 'package:hrms_tst/features/request/data/models/approval_tab.model.dart';
import 'package:hrms_tst/features/request/data/request.protocol.dart';
import 'package:hrms_tst/features/request/domain/wfh_request.state.dart';
import 'package:hrms_tst/features/teammates/data/models/teammate.model.dart';
import 'package:hrms_tst/features/teammates/domain/teammates.controller.dart';
import 'package:hrms_tst/shared/providers/auth.provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../home/<USER>/models/leave_balance.model.dart';
import '../data/models/wfh_tab.model.dart';
import 'leave_request.state.dart';

part 'request.controller.g.dart';

@riverpod
class LeaveRequest extends _$LeaveRequest {
  @override
  LeaveRequestState build() {
    reflectDisabledDates();
    reflectTeammates();
    return LeaveRequestState(
      startDate: null,
      endDate: null,
      leaveType: null,
      reason: '',
      availableLeaveTypes: [],
      availableTeammates: [],
      notifyTo: [],
      isDateSelectable: (date) => false,
      startDaySlot: null,
      endDaySlot: null,
    );
  }

  void reflectDisabledDates() async {
    log('Disabled Days');
    final disabledDatesModel =
        (await getIt.get<RequestProtocol>().getDisabledDates()).ignoreFailure();
    state = state.copyWith(
      isDateSelectable: (date) {
        if (date.weekday == DateTime.sunday) return false;
        for (final holiday in disabledDatesModel.holidays) {
          if (isDateBetween(date, holiday.startDate, holiday.endDate)) {
            return false;
          }
        }
        for (final request in disabledDatesModel.requests) {
          if (isDateBetween(date, request.startDate, request.endDate)) {
            return false;
          }
        }
        return true;
      },
    );
  }

  void reflectTeammates() async {
    final teammates = await ref.read(teammatesProvider.future);
    state = state.copyWith(
      availableTeammates: teammates
        ..removeWhere(
          (element) =>
              element.user.id == ref.read(authProvider).requireValue!.id,
        ),
    );
  }

  void setDate(DateTime? startDate, DateTime? endDate) {
    state = state.copyWith(startDate: startDate, endDate: endDate);

    if (state.startDate != null && state.endDate != null) {
      reflectAvailableLeaveTypes();
    }
  }

  void reflectAvailableLeaveTypes() async {
    final availableLeaveTypes =
        (await getIt.get<RequestProtocol>().getAvailableLeaveTypes(
          startDate: state.startDate!,
          endDate: state.endDate!,
        )).ignoreFailure();
    state = state.copyWith(availableLeaveTypes: availableLeaveTypes);
  }

  void setLeaveType(AvailableLeaveType leaveType) {
    state = state.copyWith(leaveType: leaveType);
  }

  void setStartDaySlot(String startDaySlot) {
    state = state.copyWith(startDaySlot: startDaySlot);
  }

  void setEndDaySlot(String endDaySlot) {
    state = state.copyWith(endDaySlot: endDaySlot);
  }

  void setReason(String reason) {
    state = state.copyWith(reason: reason);
  }

  void addNotifyTo(TeammateModel teammate) {
    state = state.copyWith(notifyTo: {...state.notifyTo, teammate}.toList());
  }

  void removeNotifyTo(TeammateModel teammate) {
    final removed = List<TeammateModel>.from(state.notifyTo)
      ..removeWhere((e) => e.id == teammate.id);
    state = state.copyWith(notifyTo: removed);
  }

  Future<Result<void>> requestLeave() async {
    final requestResult = await getIt.get<RequestProtocol>().requestLeave(
      leaveRequest: state,
    );
    if (requestResult.isSuccess) {
      ref.invalidate(leaveBalanceProvider);
      ref.invalidate(leaveRequestRecordsProvider);
    }
    return requestResult;
  }

  bool isDateBetween(DateTime oprand, DateTime startDate, DateTime endDate) {
    DateTime onlyDate = DateTime(oprand.year, oprand.month, oprand.day);
    DateTime onlyStart = DateTime(
      startDate.year,
      startDate.month,
      startDate.day,
    );
    DateTime onlyEnd = DateTime(endDate.year, endDate.month, endDate.day);

    return onlyDate.isAtSameMomentAs(onlyStart) ||
        onlyDate.isAtSameMomentAs(onlyEnd) ||
        (onlyDate.isAfter(onlyStart) && onlyDate.isBefore(onlyEnd));
  }

  Future<Result<void>> deleteRequest({required int requestId}) async {
    final requestResult = await getIt.get<RequestProtocol>().deleteRequest(
      requestId: requestId,
    );
    if (requestResult.isSuccess) {
      ref.invalidate(leaveBalanceProvider);
      ref.invalidate(leaveRequestRecordsProvider);
    }
    return requestResult;
  }
}

@riverpod
class WfhRequest extends _$WfhRequest {
  @override
  WFHRequestState build() {
    reflectDisabledDates();
    reflectTeammates();
    return WFHRequestState(
      startDate: null,
      endDate: null,
      reason: '',
      isDateSelectable: (date) => false,
      availableTeammates: [],
      notifyTo: [],
      startDaySlot: null,
      endDaySlot: null,
    );
  }

  void reflectDisabledDates() async {
    log('Disabled Days');
    final disabledDatesModel =
        (await getIt.get<RequestProtocol>().getDisabledDates()).ignoreFailure();
    state = state.copyWith(
      isDateSelectable: (date) {
        if (date.weekday == DateTime.sunday) return false;
        for (final holiday in disabledDatesModel.holidays) {
          if (isDateBetween(date, holiday.startDate, holiday.endDate)) {
            return false;
          }
        }
        for (final request in disabledDatesModel.requests) {
          if (isDateBetween(date, request.startDate, request.endDate)) {
            return false;
          }
        }
        return true;
      },
    );
  }

  void reflectTeammates() async {
    final teammates = await ref.read(teammatesProvider.future);
    state = state.copyWith(
      availableTeammates: teammates
        ..removeWhere(
          (element) =>
              element.user.id == ref.read(authProvider).requireValue!.id,
        ),
    );
  }

  void setDate(DateTime? startDate, DateTime? endDate) {
    state = state.copyWith(startDate: startDate, endDate: endDate);
  }

  void setStartDaySlot(String startDaySlot) {
    state = state.copyWith(startDaySlot: startDaySlot);
  }

  void setReason(String reason) {
    state = state.copyWith(reason: reason);
  }

  void addNotifyTo(TeammateModel teammate) {
    state = state.copyWith(notifyTo: {...state.notifyTo, teammate}.toList());
  }

  void removeNotifyTo(TeammateModel teammate) {
    final removed = List<TeammateModel>.from(state.notifyTo)
      ..removeWhere((e) => e.id == teammate.id);
    state = state.copyWith(notifyTo: removed);
  }

  Future<Result<void>> requestWFH() async {
    final requestResult = await getIt.get<RequestProtocol>().requestWFH(
      wfhRequest: state,
    );
    if (requestResult.isSuccess) {
      ref.invalidate(wfhRecordsProvider);
    }
    return requestResult;
  }

  bool isDateBetween(DateTime oprand, DateTime startDate, DateTime endDate) {
    DateTime onlyDate = DateTime(oprand.year, oprand.month, oprand.day);
    DateTime onlyStart = DateTime(
      startDate.year,
      startDate.month,
      startDate.day,
    );
    DateTime onlyEnd = DateTime(endDate.year, endDate.month, endDate.day);

    return onlyDate.isAtSameMomentAs(onlyStart) ||
        onlyDate.isAtSameMomentAs(onlyEnd) ||
        (onlyDate.isAfter(onlyStart) && onlyDate.isBefore(onlyEnd));
  }

  Future<Result<void>> deleteRequest({required int requestId}) async {
    final requestResult = await getIt.get<RequestProtocol>().deleteRequest(
      requestId: requestId,
    );
    if (requestResult.isSuccess) {
      ref.invalidate(wfhRecordsProvider);
    }
    return requestResult;
  }
}

@Riverpod(keepAlive: true)
FutureOr<List<WFHRecordModel>> wfhRecords(
  Ref ref, {
  DateTime? fromDate,
  DateTime? toDate,
  String? status,
}) async {
  return (await getIt.get<RequestProtocol>().getWFHRecords(
    fromDate: fromDate,
    toDate: toDate,
    status: status,
  )).ignoreFailure();
}

@Riverpod(keepAlive: true)
FutureOr<List<ApprovalRecordModel>> approvalRecords(
  Ref ref,
  DateTime? fromDate,
  DateTime? toDate,
  String? type,
  int? leaveTypeId,
) async {
  return (await getIt.get<RequestProtocol>().getApprovalRecords(
    fromDate: fromDate,
    toDate: toDate,
    type: type,
    leaveTypeId: leaveTypeId,
  )).ignoreFailure();
}
