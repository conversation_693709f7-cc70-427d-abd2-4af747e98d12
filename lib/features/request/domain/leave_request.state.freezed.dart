// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'leave_request.state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$LeaveRequestState {

 DateTime? get startDate; DateTime? get endDate; AvailableLeaveType? get leaveType; List<AvailableLeaveType> get availableLeaveTypes; List<TeammateModel> get availableTeammates; List<TeammateModel> get notifyTo; String get reason; bool Function(DateTime date) get isDateSelectable; String? get startDaySlot; String? get endDaySlot;
/// Create a copy of LeaveRequestState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LeaveRequestStateCopyWith<LeaveRequestState> get copyWith => _$LeaveRequestStateCopyWithImpl<LeaveRequestState>(this as LeaveRequestState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LeaveRequestState&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.leaveType, leaveType) || other.leaveType == leaveType)&&const DeepCollectionEquality().equals(other.availableLeaveTypes, availableLeaveTypes)&&const DeepCollectionEquality().equals(other.availableTeammates, availableTeammates)&&const DeepCollectionEquality().equals(other.notifyTo, notifyTo)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.isDateSelectable, isDateSelectable) || other.isDateSelectable == isDateSelectable)&&(identical(other.startDaySlot, startDaySlot) || other.startDaySlot == startDaySlot)&&(identical(other.endDaySlot, endDaySlot) || other.endDaySlot == endDaySlot));
}


@override
int get hashCode => Object.hash(runtimeType,startDate,endDate,leaveType,const DeepCollectionEquality().hash(availableLeaveTypes),const DeepCollectionEquality().hash(availableTeammates),const DeepCollectionEquality().hash(notifyTo),reason,isDateSelectable,startDaySlot,endDaySlot);

@override
String toString() {
  return 'LeaveRequestState(startDate: $startDate, endDate: $endDate, leaveType: $leaveType, availableLeaveTypes: $availableLeaveTypes, availableTeammates: $availableTeammates, notifyTo: $notifyTo, reason: $reason, isDateSelectable: $isDateSelectable, startDaySlot: $startDaySlot, endDaySlot: $endDaySlot)';
}


}

/// @nodoc
abstract mixin class $LeaveRequestStateCopyWith<$Res>  {
  factory $LeaveRequestStateCopyWith(LeaveRequestState value, $Res Function(LeaveRequestState) _then) = _$LeaveRequestStateCopyWithImpl;
@useResult
$Res call({
 DateTime? startDate, DateTime? endDate, AvailableLeaveType? leaveType, List<AvailableLeaveType> availableLeaveTypes, List<TeammateModel> availableTeammates, List<TeammateModel> notifyTo, String reason, bool Function(DateTime date) isDateSelectable, String? startDaySlot, String? endDaySlot
});


$AvailableLeaveTypeCopyWith<$Res>? get leaveType;

}
/// @nodoc
class _$LeaveRequestStateCopyWithImpl<$Res>
    implements $LeaveRequestStateCopyWith<$Res> {
  _$LeaveRequestStateCopyWithImpl(this._self, this._then);

  final LeaveRequestState _self;
  final $Res Function(LeaveRequestState) _then;

/// Create a copy of LeaveRequestState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? startDate = freezed,Object? endDate = freezed,Object? leaveType = freezed,Object? availableLeaveTypes = null,Object? availableTeammates = null,Object? notifyTo = null,Object? reason = null,Object? isDateSelectable = null,Object? startDaySlot = freezed,Object? endDaySlot = freezed,}) {
  return _then(_self.copyWith(
startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,leaveType: freezed == leaveType ? _self.leaveType : leaveType // ignore: cast_nullable_to_non_nullable
as AvailableLeaveType?,availableLeaveTypes: null == availableLeaveTypes ? _self.availableLeaveTypes : availableLeaveTypes // ignore: cast_nullable_to_non_nullable
as List<AvailableLeaveType>,availableTeammates: null == availableTeammates ? _self.availableTeammates : availableTeammates // ignore: cast_nullable_to_non_nullable
as List<TeammateModel>,notifyTo: null == notifyTo ? _self.notifyTo : notifyTo // ignore: cast_nullable_to_non_nullable
as List<TeammateModel>,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,isDateSelectable: null == isDateSelectable ? _self.isDateSelectable : isDateSelectable // ignore: cast_nullable_to_non_nullable
as bool Function(DateTime date),startDaySlot: freezed == startDaySlot ? _self.startDaySlot : startDaySlot // ignore: cast_nullable_to_non_nullable
as String?,endDaySlot: freezed == endDaySlot ? _self.endDaySlot : endDaySlot // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of LeaveRequestState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AvailableLeaveTypeCopyWith<$Res>? get leaveType {
    if (_self.leaveType == null) {
    return null;
  }

  return $AvailableLeaveTypeCopyWith<$Res>(_self.leaveType!, (value) {
    return _then(_self.copyWith(leaveType: value));
  });
}
}


/// @nodoc


class _LeaveRequestState implements LeaveRequestState {
  const _LeaveRequestState({required this.startDate, required this.endDate, required this.leaveType, required final  List<AvailableLeaveType> availableLeaveTypes, required final  List<TeammateModel> availableTeammates, required final  List<TeammateModel> notifyTo, required this.reason, required this.isDateSelectable, required this.startDaySlot, required this.endDaySlot}): _availableLeaveTypes = availableLeaveTypes,_availableTeammates = availableTeammates,_notifyTo = notifyTo;
  

@override final  DateTime? startDate;
@override final  DateTime? endDate;
@override final  AvailableLeaveType? leaveType;
 final  List<AvailableLeaveType> _availableLeaveTypes;
@override List<AvailableLeaveType> get availableLeaveTypes {
  if (_availableLeaveTypes is EqualUnmodifiableListView) return _availableLeaveTypes;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_availableLeaveTypes);
}

 final  List<TeammateModel> _availableTeammates;
@override List<TeammateModel> get availableTeammates {
  if (_availableTeammates is EqualUnmodifiableListView) return _availableTeammates;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_availableTeammates);
}

 final  List<TeammateModel> _notifyTo;
@override List<TeammateModel> get notifyTo {
  if (_notifyTo is EqualUnmodifiableListView) return _notifyTo;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_notifyTo);
}

@override final  String reason;
@override final  bool Function(DateTime date) isDateSelectable;
@override final  String? startDaySlot;
@override final  String? endDaySlot;

/// Create a copy of LeaveRequestState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LeaveRequestStateCopyWith<_LeaveRequestState> get copyWith => __$LeaveRequestStateCopyWithImpl<_LeaveRequestState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LeaveRequestState&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.leaveType, leaveType) || other.leaveType == leaveType)&&const DeepCollectionEquality().equals(other._availableLeaveTypes, _availableLeaveTypes)&&const DeepCollectionEquality().equals(other._availableTeammates, _availableTeammates)&&const DeepCollectionEquality().equals(other._notifyTo, _notifyTo)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.isDateSelectable, isDateSelectable) || other.isDateSelectable == isDateSelectable)&&(identical(other.startDaySlot, startDaySlot) || other.startDaySlot == startDaySlot)&&(identical(other.endDaySlot, endDaySlot) || other.endDaySlot == endDaySlot));
}


@override
int get hashCode => Object.hash(runtimeType,startDate,endDate,leaveType,const DeepCollectionEquality().hash(_availableLeaveTypes),const DeepCollectionEquality().hash(_availableTeammates),const DeepCollectionEquality().hash(_notifyTo),reason,isDateSelectable,startDaySlot,endDaySlot);

@override
String toString() {
  return 'LeaveRequestState(startDate: $startDate, endDate: $endDate, leaveType: $leaveType, availableLeaveTypes: $availableLeaveTypes, availableTeammates: $availableTeammates, notifyTo: $notifyTo, reason: $reason, isDateSelectable: $isDateSelectable, startDaySlot: $startDaySlot, endDaySlot: $endDaySlot)';
}


}

/// @nodoc
abstract mixin class _$LeaveRequestStateCopyWith<$Res> implements $LeaveRequestStateCopyWith<$Res> {
  factory _$LeaveRequestStateCopyWith(_LeaveRequestState value, $Res Function(_LeaveRequestState) _then) = __$LeaveRequestStateCopyWithImpl;
@override @useResult
$Res call({
 DateTime? startDate, DateTime? endDate, AvailableLeaveType? leaveType, List<AvailableLeaveType> availableLeaveTypes, List<TeammateModel> availableTeammates, List<TeammateModel> notifyTo, String reason, bool Function(DateTime date) isDateSelectable, String? startDaySlot, String? endDaySlot
});


@override $AvailableLeaveTypeCopyWith<$Res>? get leaveType;

}
/// @nodoc
class __$LeaveRequestStateCopyWithImpl<$Res>
    implements _$LeaveRequestStateCopyWith<$Res> {
  __$LeaveRequestStateCopyWithImpl(this._self, this._then);

  final _LeaveRequestState _self;
  final $Res Function(_LeaveRequestState) _then;

/// Create a copy of LeaveRequestState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? startDate = freezed,Object? endDate = freezed,Object? leaveType = freezed,Object? availableLeaveTypes = null,Object? availableTeammates = null,Object? notifyTo = null,Object? reason = null,Object? isDateSelectable = null,Object? startDaySlot = freezed,Object? endDaySlot = freezed,}) {
  return _then(_LeaveRequestState(
startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,leaveType: freezed == leaveType ? _self.leaveType : leaveType // ignore: cast_nullable_to_non_nullable
as AvailableLeaveType?,availableLeaveTypes: null == availableLeaveTypes ? _self._availableLeaveTypes : availableLeaveTypes // ignore: cast_nullable_to_non_nullable
as List<AvailableLeaveType>,availableTeammates: null == availableTeammates ? _self._availableTeammates : availableTeammates // ignore: cast_nullable_to_non_nullable
as List<TeammateModel>,notifyTo: null == notifyTo ? _self._notifyTo : notifyTo // ignore: cast_nullable_to_non_nullable
as List<TeammateModel>,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,isDateSelectable: null == isDateSelectable ? _self.isDateSelectable : isDateSelectable // ignore: cast_nullable_to_non_nullable
as bool Function(DateTime date),startDaySlot: freezed == startDaySlot ? _self.startDaySlot : startDaySlot // ignore: cast_nullable_to_non_nullable
as String?,endDaySlot: freezed == endDaySlot ? _self.endDaySlot : endDaySlot // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of LeaveRequestState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AvailableLeaveTypeCopyWith<$Res>? get leaveType {
    if (_self.leaveType == null) {
    return null;
  }

  return $AvailableLeaveTypeCopyWith<$Res>(_self.leaveType!, (value) {
    return _then(_self.copyWith(leaveType: value));
  });
}
}

// dart format on
