import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/teammates/presentation/widgets/teammate_detail_tabs/about_section.dart';
import 'package:hrms_tst/features/teammates/presentation/widgets/teammate_detail_tabs/job_section.dart';
import 'package:hrms_tst/features/teammates/presentation/widgets/teammate_detail_tabs/profile_section.dart';
import 'package:hrms_tst/features/teammates/presentation/widgets/teammate_tile.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';
import 'package:hrms_tst/shared/widgets/loading_placeholder.dart';
import 'package:hrms_tst/shared/widgets/screen_back_button.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';

import '../domain/teammates.controller.dart';

class TeammateDetailScreen extends ConsumerWidget {
  final int id;

  const TeammateDetailScreen({super.key, required this.id});

  static const String name = 'Teammate Detail';
  static const String path = 'teammate-detail/:id';

  static GoRoute route({List<RouteBase> routes = const []}) => GoRoute(
    path: path,
    name: name,
    pageBuilder: (context, state) => NoTransitionPage(
      child: TeammateDetailScreen(id: int.parse(state.pathParameters['id']!)),
    ),
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final teammateDetail = ref.watch(teammateDetailProvider(id));
    return Scaffold(
      body: SafeArea(
        child: ListView(
          padding: EdgeInsets.all(16),
          children: [
            ScreenBackButton(),
            Space.y(8),
            switch (teammateDetail) {
              AsyncData(:final value) => TeammateTile(
                image: value.user.image,
                name: value.user.displayName,
                empNo: value.user.details?.empNo ?? 0,
                jobRole: value.user.details?.jobRoles
                    ?.map((e) => e.name)
                    .join(', '),
                department: value.user.details?.departments
                    ?.map((e) => e.name)
                    .join(', '),
                designation: value.user.details?.designation?.name,
                office: value.user.details?.office?.name,
              ),
              AsyncError(:final error) => Center(child: Text(error.toString())),
              _ => CardContainer(
                elevation: 1,
                child: Padding(
                  padding: const EdgeInsets.all(14.0),
                  child: Row(
                    spacing: 12,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 2,
                        child: AspectRatio(
                          aspectRatio: 1,
                          child: LoadingPlaceholder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 3,
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: LoadingPlaceholder(
                                    height: 24,
                                    width: 120,
                                  ),
                                ),
                                Space.x(16),
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4),
                                    color: context.colors.outline,
                                  ),
                                  padding: EdgeInsets.all(4),
                                  child: LoadingPlaceholder(
                                    height: 12,
                                    width: 12,
                                  ),
                                ),
                              ],
                            ),
                            Divider(color: context.colors.outline, height: 8),
                            Space.y(4),
                            Column(
                              spacing: 4,

                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: List.generate(
                                4,
                                (index) => LoadingPlaceholder(
                                  height: 14,
                                  width: double.infinity,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            },
            Space.y(8),
            CardContainer(
              elevation: 1,
              child: Column(
                children: [
                  ListTile(
                    dense: true,
                    leading: SVGImage(Assets.svgs.emailIcon),
                    title: switch (teammateDetail) {
                      AsyncData(:final value) => Text(
                        value.user.details?.email ?? '',
                        style: context.textStyles.bodyMedium?.copyWith(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: context.colors.secondary,
                        ),
                      ),
                      AsyncError(:final error) => Text(error.toString()),
                      _ => LoadingPlaceholder(
                        height: 12,
                        width: double.infinity,
                      ),
                    },
                  ),
                  Divider(height: 1, indent: 54, endIndent: 24),
                  ListTile(
                    dense: true,
                    leading: SVGImage(Assets.svgs.phoneIcon),
                    title: switch (teammateDetail) {
                      AsyncData(:final value) => Text(
                        '+${value.user.countryCode ?? '91'} ${value.user.mobile}',
                        style: context.textStyles.bodyMedium?.copyWith(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: context.colors.secondary,
                        ),
                      ),
                      AsyncError(:final error) => Text(error.toString()),
                      _ => LoadingPlaceholder(
                        height: 12,
                        width: double.infinity,
                      ),
                    },
                  ),
                ],
              ),
            ),
            Space.y(8),
            CardContainer(
              elevation: 1,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: HookBuilder(
                  builder: (context) {
                    final selectedFilter = useState(
                      EmployeeDetailsFilter.about,
                    );
                    return Column(
                      children: [
                        EmployeeDetailsFilterOptions(
                          selectedFilter: selectedFilter.value,
                          onFilterSelected: (filter) {
                            selectedFilter.value = filter;
                          },
                        ),
                        switch (teammateDetail) {
                          AsyncData(:final value) =>
                            switch (selectedFilter.value) {
                              EmployeeDetailsFilter.about =>
                                EmployeeAboutSection(teammate: value),
                              EmployeeDetailsFilter.profile =>
                                EmployeeProfileSection(teammate: value),
                              EmployeeDetailsFilter.job => EmployeeJobSection(
                                teammate: value,
                              ),
                            },
                          AsyncError(:final error) => Center(
                            child: Text(error.toString()),
                          ),
                          _ => Center(
                            child: Padding(
                              padding: EdgeInsets.only(top: 16.0),
                              child: LinearProgressIndicator(
                                color: context.colors.outline.withValues(),
                              ),
                            ),
                          ),
                        },
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

enum EmployeeDetailsFilter {
  about('About'),
  profile('Profile'),
  job('Job');

  final String label;
  const EmployeeDetailsFilter(this.label);
}

class EmployeeDetailsFilterOptions extends StatelessWidget {
  const EmployeeDetailsFilterOptions({
    super.key,
    required this.selectedFilter,
    required this.onFilterSelected,
  });

  final EmployeeDetailsFilter selectedFilter;
  final Function(EmployeeDetailsFilter filter) onFilterSelected;

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 8,
      children: EmployeeDetailsFilter.values.map((e) {
        final isSelected = e == selectedFilter;
        return GestureDetector(
          onTap: () {
            onFilterSelected(e);
          },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: isSelected
                  ? context.colors.secondary.withValues(alpha: 0.8)
                  : context.colors.outline,
            ),
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            child: Text(
              e.label,
              style: context.textStyles.titleMedium?.copyWith(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: isSelected
                    ? context.colors.surface
                    : context.colors.secondary,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
