import 'package:flutter/material.dart';

import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/bottom_navigation/widgets/main_app_bar.dart';
import 'package:hrms_tst/features/teammates/presentation/widgets/teammate_tile.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/providers/auth.provider.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';
import 'package:hrms_tst/shared/widgets/loading_placeholder.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';

import '../domain/teammates.controller.dart';
import 'team_progress.screen.dart';
import 'widgets/teammates_list.dart';

class TeammatesScreen extends ConsumerWidget {
  const TeammatesScreen({super.key});

  static const String name = 'Teammates';
  static const String path = '/teammates';

  static GoRoute route({List<RouteBase> routes = const []}) => GoRoute(
    path: path,
    name: name,
    routes: routes,
    pageBuilder: (context, state) =>
        const NoTransitionPage(child: TeammatesScreen()),
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: MainAppBar(),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            children: [
              switch (ref.watch(
                teammateDetailProvider(ref.read(authProvider).requireValue!.id),
              )) {
                AsyncData(:final value) => Builder(
                  builder: (context) {
                    final teammate = value.user;
                    return TeammateTile(
                      image: teammate.image,
                      name: teammate.displayName,
                      empNo: teammate.details?.empNo,
                      jobRole: teammate.details?.jobRoles
                          ?.map((e) => e.name)
                          .join(','),
                      department: teammate.details?.departments
                          ?.map((e) => e.name)
                          .join(','),
                      designation: teammate.details?.designation?.name,
                      office: teammate.details?.office?.name,
                    );
                  },
                ),
                AsyncError(:final error) => Text(error.toString()),
                _ => CardContainer(
                  elevation: 1,
                  child: Padding(
                    padding: const EdgeInsets.all(14.0),
                    child: Row(
                      spacing: 12,
                      children: [
                        Expanded(
                          flex: 2,
                          child: AspectRatio(
                            aspectRatio: 1,
                            child: LoadingPlaceholder(
                              decoration: BoxDecoration(
                                color: context.colors.outline,
                                borderRadius: BorderRadius.circular(16),
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Row(
                                children: [
                                  LoadingPlaceholder(
                                    decoration: BoxDecoration(
                                      color: context.colors.outline,
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    height: 24,
                                    width: 120,
                                  ),
                                  Spacer(),
                                  LoadingPlaceholder(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(4),
                                      color: context.colors.outline,
                                    ),
                                    padding: EdgeInsets.all(4),
                                    child: LoadingPlaceholder(
                                      decoration: BoxDecoration(
                                        color: context.colors.outline,
                                        borderRadius: BorderRadius.circular(16),
                                      ),
                                      height: 14,
                                      width: 20,
                                    ),
                                  ),
                                ],
                              ),
                              Divider(color: context.colors.outline, height: 8),
                              Space.y(4),
                              Column(
                                spacing: 4,

                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: List.generate(
                                  4,
                                  (index) => LoadingPlaceholder(
                                    decoration: BoxDecoration(
                                      color: context.colors.outline,
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    height: 14,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              },
              Space.y(8),
              GestureDetector(
                onTap: () => context.pushNamed(TeamProgressScreen.name),
                child: CardContainer(
                  elevation: 1,
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Row(
                      children: [
                        Text(
                          'Team Progress',
                          style: context.textStyles.titleLarge?.copyWith(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: context.colors.onSurface,
                          ),
                        ),
                        Spacer(),
                        SVGImage(
                          Assets.svgs.chevronRight,
                          width: 24,
                          height: 24,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Space.y(8),
              Expanded(child: TeammatesList()),
              Space.y(16),
            ],
          ),
        ),
      ),
    );
  }
}
