import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/teammates/data/models/teammate_detail.model.dart';
import 'package:hrms_tst/features/teammates/domain/edit_profile.controller.dart';
import 'package:hrms_tst/features/teammates/presentation/edit_profile.screen.dart';
import 'package:hrms_tst/features/teammates/presentation/widgets/teammate_detail_tabs/about_section.dart';
import 'package:hrms_tst/features/teammates/presentation/widgets/teammate_detail_tabs/job_section.dart';
import 'package:hrms_tst/features/teammates/presentation/widgets/teammate_detail_tabs/profile_section.dart';
import 'package:hrms_tst/features/teammates/presentation/widgets/teammate_tile.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';
import 'package:hrms_tst/shared/widgets/screen_back_button.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';

class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  static const String name = 'Profile';
  static const String path = '/profile';

  static GoRoute route({List<RouteBase> routes = const []}) => GoRoute(
    path: path,
    name: name,
    pageBuilder: (context, state) => MaterialPage(child: ProfileScreen()),
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(profileDataProvider);
    return Scaffold(
      body: SafeArea(
        child: ListView(
          padding: EdgeInsets.all(16),
          children: [
            Space.y(8),
            Row(
              children: [
                ScreenBackButton(),
                Spacer(),
                GestureDetector(
                  onTap: () {
                    context.pushNamed(EditProfileScreen.name);
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(
                        color: context.colors.secondary.withValues(alpha: 0.8),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      'Edit Profile',
                      style: context.textStyles.bodyMedium?.copyWith(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            Space.y(16),
            TeammateTile(
              image: user.image,
              name: user.displayName,
              empNo: user.details?.empNo ?? 0,
              jobRole: user.details?.jobRoles?.map((e) => e.name).join(', '),
              department: user.details?.departments
                  ?.map((e) => e.name)
                  .join(', '),
              designation: user.details?.designation?.name,
              office: user.details?.office?.name,
            ),
            Space.y(8),
            CardContainer(
              elevation: 1,
              child: Column(
                children: [
                  ListTile(
                    dense: true,
                    leading: SVGImage(Assets.svgs.emailIcon),
                    title: Text(
                      user.details?.email ?? '',
                      style: context.textStyles.bodyMedium?.copyWith(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: context.colors.secondary,
                      ),
                    ),
                  ),
                  Divider(height: 1, indent: 54, endIndent: 24),
                  ListTile(
                    dense: true,
                    leading: SVGImage(Assets.svgs.phoneIcon),
                    title: Text(
                      '+${user.countryCode ?? '91'} ${user.mobile}',
                      style: context.textStyles.bodyMedium?.copyWith(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: context.colors.secondary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Space.y(8),
            CardContainer(
              elevation: 1,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: HookBuilder(
                  builder: (context) {
                    final selectedFilter = useState(
                      EmployeeDetailsFilter.about,
                    );
                    return Column(
                      children: [
                        EmployeeDetailsFilterOptions(
                          selectedFilter: selectedFilter.value,
                          onFilterSelected: (filter) {
                            selectedFilter.value = filter;
                          },
                        ),
                        switch (selectedFilter.value) {
                          EmployeeDetailsFilter.about => EmployeeAboutSection(
                            teammate: TeammateDetailModel(
                              currentStatus: '',
                              user: user,
                            ),
                          ),
                          EmployeeDetailsFilter.profile =>
                            EmployeeProfileSection(
                              teammate: TeammateDetailModel(
                                currentStatus: '',
                                user: user,
                              ),
                            ),
                          EmployeeDetailsFilter.job => EmployeeJobSection(
                            teammate: TeammateDetailModel(
                              currentStatus: '',
                              user: user,
                            ),
                          ),
                        },
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

enum EmployeeDetailsFilter {
  about('About'),
  profile('Profile'),
  job('Job');

  final String label;
  const EmployeeDetailsFilter(this.label);
}

class EmployeeDetailsFilterOptions extends StatelessWidget {
  const EmployeeDetailsFilterOptions({
    super.key,
    required this.selectedFilter,
    required this.onFilterSelected,
  });

  final EmployeeDetailsFilter selectedFilter;
  final Function(EmployeeDetailsFilter filter) onFilterSelected;

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 8,
      children: EmployeeDetailsFilter.values.map((e) {
        final isSelected = e == selectedFilter;
        return GestureDetector(
          onTap: () {
            onFilterSelected(e);
          },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: isSelected
                  ? context.colors.secondary.withValues(alpha: 0.8)
                  : context.colors.outline,
            ),
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            child: Text(
              e.label,
              style: context.textStyles.titleMedium?.copyWith(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: isSelected
                    ? context.colors.surface
                    : context.colors.secondary,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
