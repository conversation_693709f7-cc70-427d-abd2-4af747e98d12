import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/teammates/domain/team_progress.controller.dart';
import 'package:hrms_tst/features/teammates/presentation/widgets/team_progress_tabs/book_progress_tab_view.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';
import 'package:hrms_tst/shared/widgets/month_range_selection.dart';
import 'package:hrms_tst/shared/widgets/screen_back_button.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';

import 'widgets/team_progress_tabs/course_progress_tab_view.dart';
import 'widgets/team_progress_tabs/event_progress_tab_view.dart';
import 'widgets/team_progress_tabs/gd_progress_tab_view.dart';
import 'widgets/team_progress_tabs/session_progress_tab_view.dart';

class TeamProgressScreen extends HookConsumerWidget {
  const TeamProgressScreen({super.key});

  static const String name = 'Team progress';
  static const String path = 'team-progress';

  static GoRoute route({List<RouteBase> routes = const []}) => GoRoute(
    path: path,
    name: name,
    routes: routes,
    pageBuilder: (context, state) =>
        const NoTransitionPage(child: TeamProgressScreen()),
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedTab = useState(TeamProgressTab.book);

    final startDate = useState<DateTime?>(null);
    final endDate = useState<DateTime?>(null);

    final bookProgress = ref.watch(
      bookProgressProvider(startDate.value, endDate.value, null),
    );
    final courseProgress = ref.watch(
      courseProgressProvider(startDate.value, endDate.value, null),
    );
    final gdProgress = ref.watch(
      gdProgressProvider(startDate.value, endDate.value, null),
    );
    final sessionProgress = ref.watch(
      sessionProgressProvider(startDate.value, endDate.value, null),
    );
    final eventProgress = ref.watch(
      eventProgressProvider(startDate.value, endDate.value, null),
    );
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            children: [
              ScreenBackButton(),
              Space.y(18),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: TeamProgressTab.values.map((e) {
                    final isSelected = e == selectedTab.value;
                    return GestureDetector(
                      onTap: () {
                        selectedTab.value = e;
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: context.colors.tertiary.withValues(
                            alpha: isSelected ? 1 : 0.1,
                          ),
                        ),
                        padding: EdgeInsets.all(12),
                        child: AnimatedSize(
                          duration: const Duration(milliseconds: 200),
                          curve: Curves.ease,
                          alignment: Alignment.centerLeft,
                          child: Row(
                            spacing: 4,
                            children: [
                              SVGImage(
                                e.iconPath,
                                height: 24,
                                width: 24,
                                color: isSelected
                                    ? context.colors.surface
                                    : context.colors.tertiary,
                              ),
                              if (isSelected)
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8.0,
                                  ),
                                  child: Text(
                                    e.label,
                                    style: context.textStyles.titleMedium
                                        ?.copyWith(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          color: context.colors.surface,
                                        ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
              Space.y(8),
              Expanded(
                child: CardContainer(
                  elevation: 1,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Row(
                          children: [
                            Text(
                              'Progress',
                              style: context.textStyles.titleMedium?.copyWith(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: context.colors.onSurface,
                              ),
                            ),
                            Spacer(),
                            MonthRangeSelection(
                              startDate: startDate.value,
                              endDate: endDate.value,
                              onFilterSelected: (start, end) {
                                startDate.value = start;
                                endDate.value = end;

                                context.pop();
                              },
                            ),
                          ],
                        ),
                        Space.y(8),
                        Expanded(
                          child: switch (selectedTab.value) {
                            TeamProgressTab.book => BookProgressTabView(
                              state: bookProgress,
                            ),
                            TeamProgressTab.course => CourseProgressTabView(
                              state: courseProgress,
                            ),
                            TeamProgressTab.gd => GdProgressTabView(
                              state: gdProgress,
                            ),
                            TeamProgressTab.session => SessionProgressTabView(
                              state: sessionProgress,
                            ),
                            TeamProgressTab.event => EventProgressTabView(
                              state: eventProgress,
                            ),
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

enum TeamProgressTab {
  book(label: 'Books'),
  course(label: 'Courses'),
  gd(label: 'GDs'),
  session(label: 'Sessions'),
  event(label: 'Events');

  final String label;

  const TeamProgressTab({required this.label});

  String get iconPath => switch (this) {
    TeamProgressTab.book => Assets.svgs.teamProgressBooks,
    TeamProgressTab.course => Assets.svgs.teamProgressCourses,
    TeamProgressTab.gd => Assets.svgs.teamProgressGds,
    TeamProgressTab.session => Assets.svgs.teamProgressSessions,
    TeamProgressTab.event => Assets.svgs.teamProgressEvents,
  };
}
