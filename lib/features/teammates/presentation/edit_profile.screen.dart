import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/extensions/string.extension.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/core/utils/helpers/snackbar.helper.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/core/utils/helpers/validators.dart';
import 'package:hrms_tst/features/teammates/domain/edit_profile.controller.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/models/user.model.dart';
import 'package:hrms_tst/shared/widgets/app_dropdown_field.dart';
import 'package:hrms_tst/shared/widgets/app_text_field.dart';
import 'package:hrms_tst/shared/widgets/screen_back_button.dart';
import 'package:hrms_tst/shared/widgets/app_html_editor.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';
import 'package:intl/intl.dart';

import '../domain/teammates.controller.dart';

class EditProfileScreen extends HookConsumerWidget {
  const EditProfileScreen({super.key});

  static const String name = 'Edit Profile';
  static const String path = '/profile-edit';

  static GoRoute route({List<RouteBase> routes = const []}) => GoRoute(
    path: path,
    name: name,
    pageBuilder: (context, state) => MaterialPage(child: EditProfileScreen()),
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useState(GlobalKey<FormState>());

    final user = ref.read(profileDataProvider);
    final labelStyle = context.textStyles.bodyMedium?.copyWith(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      color: context.colors.onSurface,
    );

    final expandedIndex = useState<int?>(null);

    final state = ref.watch(editProfileProvider);
    final notifier = ref.watch(editProfileProvider.notifier);

    return Form(
      key: formKey.value,
      child: Scaffold(
        body: SafeArea(
          child: CustomScrollView(
            slivers: [
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: ScreenBackButton(),
                ),
              ),
              DetailSection(
                index: 0,
                expandedIndex: expandedIndex,
                title: 'About',
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Space.y(30),
                    Text(
                      'About',
                      style: context.textStyles.bodyMedium?.copyWith(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF7A7A7A),
                      ),
                    ),
                    Space.y(4),
                    AppHTMLEditor(
                      initialValue: state.about,
                      onChanged: (value) => notifier.about = value,
                      characterLimit: 500,
                      backgroundColor: context.appTextFieldFillColor,
                    ),
                    Space.y(30),
                    Text(
                      'Interests',
                      style: context.textStyles.bodyMedium?.copyWith(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF7A7A7A),
                      ),
                    ),
                    Space.y(4),
                    AppHTMLEditor(
                      initialValue: state.interests,
                      onChanged: (value) => notifier.interests = value,
                      characterLimit: 500,
                      backgroundColor: context.appTextFieldFillColor,
                    ),
                    Space.y(30),
                    Text(
                      'Hobbies',
                      style: context.textStyles.bodyMedium?.copyWith(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF7A7A7A),
                      ),
                    ),
                    Space.y(4),
                    AppHTMLEditor(
                      initialValue: state.hobbies,
                      onChanged: (value) => notifier.hobbies = value,
                      characterLimit: 500,
                      backgroundColor: context.appTextFieldFillColor,
                    ),
                  ],
                ),
              ),
              SliverToBoxAdapter(child: Space.y(10)),
              DetailSection(
                index: 1,
                expandedIndex: expandedIndex,
                title: 'Profile Details',
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Space.y(30),
                    AppTextField(
                      labelText: 'First Name',
                      initialValue: state.firstName,
                      onChanged: (value) => notifier.firstName = value,
                      validator: Validator(
                        fieldName: 'First Name',
                        validations: [
                          Validations.required(error: 'First Name is required'),
                        ],
                      ).build,
                    ),
                    Space.y(10),
                    AppTextField(
                      labelText: 'Middle Name',
                      initialValue: state.middleName,
                      onChanged: (value) => notifier.middleName = value,
                    ),
                    Space.y(10),
                    AppTextField(
                      labelText: 'Last Name',
                      initialValue: state.lastName,
                      onChanged: (value) => notifier.lastName = value,
                      validator: Validator(
                        fieldName: 'Last Name',
                        validations: [
                          Validations.required(error: 'Last Name is required'),
                        ],
                      ).build,
                    ),
                    Space.y(10),
                    AppTextField(
                      labelText: 'Personal Mobile No.',
                      initialValue: state.personalMobile,
                      onChanged: (value) => notifier.personalMobile = value,
                    ),
                    Space.y(10),
                    AppTextField(
                      labelText: 'Personal Email',
                      initialValue: state.personalEmail,
                      onChanged: (value) => notifier.personalEmail = value,
                    ),
                    Space.y(30),
                    Text('Additional Details', style: labelStyle),
                    Space.y(10),
                    Row(
                      spacing: 12,
                      children: [
                        Expanded(
                          child: AppDropdownField<Gender>(
                            value: state.gender,
                            labelText: 'Gender',
                            contentPadding: EdgeInsets.symmetric(
                              vertical: 15,
                            ).copyWith(right: 16),
                            validator: (gender) {
                              if (gender == null) {
                                return 'Gender is required';
                              }
                              return null;
                            },
                            items: Gender.values
                                .map(
                                  (e) => DropdownMenuItem(
                                    value: e,
                                    child: Text(e.name.capitalize()),
                                  ),
                                )
                                .toList(),
                            onChanged: (value) => notifier.gender = value,
                          ),
                        ),
                        Expanded(
                          child: KeyedSubtree(
                            key: ValueKey(state.dob),
                            child: AppTextField(
                              readOnly: true,

                              onTap: () async {
                                notifier.dob =
                                    await showDatePicker(
                                      context: context,
                                      firstDate: DateTime(1000),
                                      lastDate: DateTime.now(),
                                    ) ??
                                    state.dob;

                                log('${state.dob?.toIso8601String()}');
                              },
                              labelText: 'Date of Birth',
                              initialValue: state.dob == null
                                  ? ''
                                  : DateFormat(
                                      'dd MMM yyyy',
                                    ).format(state.dob!.toLocal()),
                            ),
                          ),
                        ),
                      ],
                    ),
                    Space.y(10),
                    Row(
                      spacing: 12,
                      children: [
                        Expanded(
                          child: AppTextField(
                            labelText: 'Blood Group',
                            initialValue: state.bloodGroup,
                            onChanged: (value) => notifier.bloodGroup = value,
                          ),
                        ),
                        Expanded(
                          child: AppDropdownField<String>(
                            value: state.maritalStatus,
                            contentPadding: EdgeInsets.symmetric(
                              vertical: 15,
                            ).copyWith(right: 16),
                            labelText: 'Marital Status',
                            items: ['married', 'unmarried']
                                .map(
                                  (e) => DropdownMenuItem(
                                    value: e,
                                    child: Text(e.capitalize()),
                                  ),
                                )
                                .toList(),
                            onChanged: (value) =>
                                notifier.maritalStatus = value,
                          ),
                        ),
                      ],
                    ),
                    Space.y(30),
                    Text('Current Address', style: labelStyle),
                    Space.y(10),
                    AppTextField(
                      maxLines: 3,
                      initialValue: state.currentAddr,
                      onChanged: (value) => notifier.currentAddr = value,
                      validator: Validator(
                        fieldName: 'Current Address',
                        validations: [
                          Validations.required(
                            error: 'Current Address is required',
                          ),
                        ],
                      ).build,
                    ),
                    Space.y(10),
                    Row(
                      spacing: 12,
                      children: [
                        Expanded(
                          child: AppTextField(
                            labelText: 'City',
                            initialValue: state.city,
                            onChanged: (value) => notifier.city = value,
                            validator: Validator(
                              fieldName: 'City',
                              validations: [
                                Validations.required(error: 'City is required'),
                              ],
                            ).build,
                          ),
                        ),
                        Expanded(
                          child: AppTextField(
                            labelText: 'State',
                            initialValue: state.state,
                            onChanged: (value) => notifier.addressState = value,
                            validator: Validator(
                              fieldName: 'State',
                              validations: [
                                Validations.required(
                                  error: 'State is required',
                                ),
                              ],
                            ).build,
                          ),
                        ),
                      ],
                    ),
                    Space.y(10),
                    AppTextField(
                      labelText: 'Country',
                      initialValue: state.country,
                      onChanged: (value) => notifier.country = value,
                      validator: Validator(
                        fieldName: 'Country',
                        validations: [
                          Validations.required(error: 'Country is required'),
                        ],
                      ).build,
                    ),
                    Space.y(4),
                    Row(
                      children: [
                        Radio<bool>(
                          toggleable: true,
                          value: state.isAddressSame ?? false,
                          groupValue: true,
                          onChanged: (value) {
                            notifier.isAddressSame =
                                !(state.isAddressSame ?? false);
                          },
                          activeColor: context.colors.secondary.withValues(
                            alpha: 0.8,
                          ),
                        ),
                        Text(
                          'Current & Permanent Address are same',
                          style: context.textStyles.bodySmall?.copyWith(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                    Space.y(20),
                    if (!(state.isAddressSame ?? false)) ...[
                      Text('Permananent Address', style: labelStyle),
                      Space.y(10),
                      AppTextField(
                        maxLines: 3,
                        initialValue: state.permanentAddr,
                        onChanged: (value) => notifier.permanentAddr = value,
                        validator: Validator(
                          fieldName: 'Current Address',
                          validations: [
                            Validations.required(
                              error: 'Current Address is required',
                            ),
                          ],
                        ).build,
                      ),
                      Space.y(10),
                      Row(
                        spacing: 12,
                        children: [
                          Expanded(
                            child: AppTextField(
                              labelText: 'City',
                              initialValue: state.permanentCity,
                              onChanged: (value) =>
                                  notifier.permanentCity = value,
                              validator: Validator(
                                fieldName: 'City',
                                validations: [
                                  Validations.required(
                                    error: 'City is required',
                                  ),
                                ],
                              ).build,
                            ),
                          ),
                          Expanded(
                            child: AppTextField(
                              labelText: 'State',
                              initialValue: state.permanentState,
                              onChanged: (value) =>
                                  notifier.permanentState = value,
                              validator: Validator(
                                fieldName: 'State',
                                validations: [
                                  Validations.required(
                                    error: 'State is required',
                                  ),
                                ],
                              ).build,
                            ),
                          ),
                        ],
                      ),
                      Space.y(10),
                      AppTextField(
                        labelText: 'Country',
                        initialValue: state.permanentCountry,
                        onChanged: (value) => notifier.permanentCountry = value,
                        validator: Validator(
                          fieldName: 'Country',
                          validations: [
                            Validations.required(error: 'Country is required'),
                          ],
                        ).build,
                      ),
                    ],
                  ],
                ),
              ),
              SliverToBoxAdapter(child: Space.y(10)),
              DetailSection(
                index: 2,
                expandedIndex: expandedIndex,
                title: 'Job Details',
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Space.y(30),
                    Row(
                      spacing: 12,
                      children: [
                        Expanded(
                          child: AppTextField(
                            readOnly: true,
                            labelText: 'Joining Details',
                            controller: TextEditingController(
                              text: user.details?.joiningDate != null
                                  ? DateFormat('dd/MM/yyyy').format(
                                      user.details!.joiningDate!.toLocal(),
                                    )
                                  : '',
                            ),
                          ),
                        ),

                        Expanded(
                          child: AppTextField(
                            readOnly: true,
                            labelText: 'Time Type',
                            controller: TextEditingController(
                              text: (user.details?.partTime ?? false)
                                  ? 'Part Time'
                                  : 'Full Time',
                            ),
                          ),
                        ),
                      ],
                    ),
                    Space.y(10),
                    Row(
                      spacing: 12,
                      children: [
                        Expanded(
                          child: AppTextField(
                            readOnly: true,
                            labelText: 'Reporting Manager',
                            controller: TextEditingController(
                              text:
                                  ref
                                      .watch(
                                        teammateByIdProvider(
                                          user.details?.managerId,
                                        ),
                                      )
                                      .value
                                      ?.firstName ??
                                  '',
                            ),
                          ),
                        ),
                        Expanded(
                          child: AppTextField(
                            readOnly: true,
                            labelText: 'Designation',
                            controller: TextEditingController(
                              text: user.details?.designation?.name,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Space.y(30),
                    Text('Departments', style: labelStyle),
                    Space.y(10),
                    Wrap(
                      children:
                          user.details?.departments
                              ?.map(
                                (e) => Container(
                                  padding: EdgeInsets.symmetric(
                                    vertical: 4,
                                    horizontal: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4),
                                    color: context.colors.outline,
                                  ),
                                  child: Text(
                                    e.name,
                                    style: context.textStyles.bodySmall
                                        ?.copyWith(
                                          fontWeight: FontWeight.w500,
                                          fontSize: 12,
                                        ),
                                  ),
                                ),
                              )
                              .toList() ??
                          [],
                    ),
                    Space.y(20),
                    Text('Job Roles', style: labelStyle),
                    Space.y(10),
                    Wrap(
                      children:
                          user.details?.jobRoles
                              ?.map(
                                (e) => Container(
                                  padding: EdgeInsets.symmetric(
                                    vertical: 4,
                                    horizontal: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4),
                                    color: context.colors.outline,
                                  ),
                                  child: Text(
                                    e.name,
                                    style: context.textStyles.bodySmall
                                        ?.copyWith(
                                          fontWeight: FontWeight.w500,
                                          fontSize: 12,
                                        ),
                                  ),
                                ),
                              )
                              .toList() ??
                          [],
                    ),
                  ],
                ),
              ),
              SliverToBoxAdapter(child: Space.y(10)),
              DetailSection(
                index: 3,
                expandedIndex: expandedIndex,
                title: 'Work Office Details',
                child: Column(
                  children: [
                    Space.y(30),
                    AppTextField(
                      readOnly: true,
                      labelText: 'Office Name',
                      controller: TextEditingController(
                        text: user.details?.office?.name,
                      ),
                    ),
                    Space.y(10),
                    AppTextField(
                      readOnly: true,
                      labelText: 'Street Address',
                      controller: TextEditingController(text: ''),
                    ),
                    Space.y(10),
                    Row(
                      spacing: 12,
                      children: [
                        Expanded(
                          child: AppTextField(
                            readOnly: true,
                            labelText: 'City',
                            controller: TextEditingController(
                              text: user.details?.office?.city,
                            ),
                          ),
                        ),
                        Expanded(
                          child: AppTextField(
                            readOnly: true,
                            labelText: 'State',
                            controller: TextEditingController(
                              text: user.details?.office?.state,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Space.y(10),
                    AppTextField(
                      readOnly: true,
                      labelText: 'Country',
                      controller: TextEditingController(
                        text: user.details?.office?.country,
                      ),
                    ),
                  ],
                ),
              ),
              SliverToBoxAdapter(child: Space.y(30)),
            ],
          ),
        ),
        bottomNavigationBar: Container(
          color: context.colors.surface,
          padding: EdgeInsets.symmetric(vertical: 20.0, horizontal: 20.0),
          child: SafeArea(
            child: OutlinedButton(
              onPressed: () async {
                if (!formKey.value.currentState!.validate()) {
                  return;
                }

                final result = await notifier.save();
                await result.fold(
                  onSuccess: (data) {
                    context.pop();
                    context.showSuccess('Profile Updated Successfully');
                  },
                  onFailure: (message) {
                    context.showError(message);
                  },
                );
              },
              child: Text(
                'Save Changes',
                style: context.textStyles.titleMedium?.copyWith(
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  color: context.colors.primary,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class DetailSection extends HookConsumerWidget {
  const DetailSection({
    super.key,
    required this.expandedIndex,
    required this.index,
    required this.title,
    required this.child,
  });

  final ValueNotifier<int?> expandedIndex;
  final int index;
  final String title;
  final Widget child;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SliverToBoxAdapter(
      child: GestureDetector(
        onTap: () {
          expandedIndex.value = expandedIndex.value == index ? null : index;
        },
        child: Container(
          color: context.colors.surface,
          padding: EdgeInsets.symmetric(vertical: 30.0, horizontal: 20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                children: [
                  Text(
                    title,
                    style: context.textStyles.bodyLarge?.copyWith(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Spacer(),
                  AnimatedRotation(
                    duration: const Duration(milliseconds: 200),
                    curve: Curves.easeInOut,
                    turns: expandedIndex.value == index ? 0.0 : 0.5,
                    child: SVGImage(
                      Assets.svgs.dropdownArrowIcon,
                      height: 24,
                      width: 24,
                    ),
                  ),
                ],
              ),
              if (expandedIndex.value == index)
                GestureDetector(onTap: () {}, child: child),
            ],
          ),
        ),
      ),
    );
  }
}
