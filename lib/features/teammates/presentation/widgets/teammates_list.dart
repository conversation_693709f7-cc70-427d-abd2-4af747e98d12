import 'package:flutter/material.dart';

import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/teammates/domain/teammates.controller.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/cached_image.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';
import 'package:hrms_tst/shared/widgets/loading_placeholder.dart';
import 'package:hrms_tst/shared/widgets/no_data_view.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';

import '../../data/models/teammate.model.dart';
import '../teammate_detail.screen.dart';
import 'teammate_list_filter_sheet.dart';

class TeammatesList extends HookConsumerWidget {
  const TeammatesList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return switch (ref.watch(teammatesProvider)) {
      AsyncData(:final value) => CardContainer(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: Row(
                children: [
                  Text(
                    'Teammates',
                    style: context.textStyles.titleLarge?.copyWith(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: context.colors.onSurface,
                    ),
                  ),
                  Space.x(5),
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: context.colors.primary,
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    child: Text(
                      value.length.toString(),
                      style: context.textStyles.bodySmall?.copyWith(
                        fontSize: 8,
                        color: context.colors.surface,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                  Spacer(),
                  IconButton(
                    onPressed: () {
                      showModalBottomSheet(
                        useRootNavigator: true,
                        context: context,
                        clipBehavior: Clip.antiAlias,
                        backgroundColor: context.colors.surface,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(14),
                        ),
                        builder: (context) {
                          return TeammateListFilterSheet();
                        },
                      );
                    },
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    icon: Badge(
                      isLabelVisible: ref
                          .watch(teammatesProvider.notifier)
                          .isFilterApplied,
                      child: SVGImage(
                        Assets.svgs.teammateFilterIcon,
                        applyColor: false,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Divider(
              height: 1,
              thickness: 1,
              color: context.colors.outline,
              indent: 6,
              endIndent: 6,
            ),
            Expanded(
              child: value.isEmpty
                  ? NoDataView()
                  : ListView.builder(
                      itemCount: value.length,
                      itemBuilder: (context, index) => _teammateTile(
                        context: context,
                        teammate: value[index],
                      ),
                    ),
            ),
          ],
        ),
      ),
      AsyncError(:final error) => Text(error.toString()),
      _ => CardContainer(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: Row(
                children: [
                  Text(
                    'Teammates',
                    style: context.textStyles.titleLarge?.copyWith(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: context.colors.onSurface,
                    ),
                  ),
                  Space.x(5),
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: context.colors.primary,
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    child: Text(
                      '00',
                      style: context.textStyles.bodySmall?.copyWith(
                        fontSize: 8,
                        color: context.colors.surface,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                  Spacer(),
                  Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: LoadingPlaceholder(height: 24, width: 24),
                  ),
                ],
              ),
            ),
            Divider(
              height: 1,
              thickness: 1,
              color: context.colors.outline,
              indent: 6,
              endIndent: 6,
            ),
            Expanded(
              child: ListView.builder(
                itemCount: 10,
                itemBuilder: (context, index) => ListTile(
                  contentPadding: EdgeInsets.symmetric(horizontal: 10),
                  horizontalTitleGap: 10,

                  leading: LoadingPlaceholder(
                    height: 44,
                    width: 44,
                    decoration: ShapeDecoration(
                      shape: CircleBorder(),
                      color: context.colors.outline,
                    ),
                  ),

                  title: LoadingPlaceholder(height: 16, width: 120),
                  subtitle: LoadingPlaceholder(height: 10, width: 80),
                  dense: true,
                ),
              ),
            ),
          ],
        ),
      ),
    };
  }

  Widget _teammateTile({
    required BuildContext context,
    required TeammateModel teammate,
  }) {
    return ListTile(
      onTap: () {
        context.goNamed(
          TeammateDetailScreen.name,
          pathParameters: {'id': teammate.user.id.toString()},
        );
      },
      contentPadding: EdgeInsets.symmetric(horizontal: 10),
      horizontalTitleGap: 10,

      leading: CachedImage(
        url: teammate.user.image ?? '',
        height: 44,
        width: 44,
        decoration: ShapeDecoration(shape: CircleBorder()),
      ),

      title: Text(
        teammate.user.displayName ?? '',
        style: context.textStyles.bodyLarge?.copyWith(
          fontWeight: FontWeight.w500,
          fontSize: 16,
        ),
      ),
      subtitle: Text(
        teammate.designation?.name ?? '',
        style: context.textStyles.bodySmall?.copyWith(
          fontWeight: FontWeight.w500,
          fontSize: 10,
        ),
      ),
      dense: true,
    );
  }
}
