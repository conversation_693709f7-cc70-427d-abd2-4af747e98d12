import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/shared/widgets/cached_image.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';

class TeammateTile extends ConsumerWidget {
  const TeammateTile({
    super.key,
    required this.image,
    required this.name,
    required this.empNo,
    required this.jobRole,
    required this.department,
    required this.designation,
    required this.office,
  });

  final String? image;
  final String? name;
  final int? empNo;
  final String? jobRole;
  final String? department;
  final String? designation;
  final String? office;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return CardContainer(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 12,
          children: [
            Expanded(
              flex: 2,
              child: AspectRatio(
                aspectRatio: 1,
                child: CachedImage(url: image ?? ''),
              ),
            ),
            Expanded(
              flex: 3,
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Tooltip(
                          message: name ?? '',
                          triggerMode: TooltipTriggerMode.tap,
                          child: Text(
                            name ?? '',
                            style: context.textStyles.titleMedium?.copyWith(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      Space.x(4),
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          color: context.colors.outline,
                        ),
                        padding: EdgeInsets.all(4),
                        child: Text(
                          '# ${empNo.toString().padLeft(3, '0')}',
                          style: context.textStyles.bodySmall?.copyWith(
                            fontSize: 10,
                            color: context.colors.secondary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Divider(color: context.colors.outline, height: 8),
                  Space.y(4),
                  Column(
                    spacing: 4,

                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      if (jobRole != null && jobRole!.isNotEmpty)
                        _buildDetail(
                          context,
                          label: 'Job Role',
                          value: jobRole!,
                        ),
                      if (department != null && department!.isNotEmpty)
                        _buildDetail(
                          context,
                          label: 'Department',
                          value: department!,
                        ),
                      if (designation != null && designation!.isNotEmpty)
                        _buildDetail(
                          context,
                          label: 'Designation',
                          value: designation!,
                        ),
                      if (office != null && office!.isNotEmpty)
                        _buildDetail(context, label: 'Office', value: office!),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetail(
    BuildContext context, {
    required String label,
    required String value,
  }) {
    return Tooltip(
      triggerMode: TooltipTriggerMode.tap,
      message: '$label: $value',
      child: Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: '$label: ',
              style: context.textStyles.bodySmall?.copyWith(
                fontSize: 12,
                fontWeight: FontWeight.w300,
                color: context.colors.secondary.withValues(alpha: 0.8),
              ),
            ),
            TextSpan(
              text: value,
              style: context.textStyles.bodySmall?.copyWith(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: context.colors.secondary,
              ),
            ),
          ],
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}
