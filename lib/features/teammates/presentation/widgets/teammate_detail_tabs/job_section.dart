import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/teammates/data/models/teammate_detail.model.dart';
import 'package:hrms_tst/features/teammates/domain/teammates.controller.dart';
import 'package:hrms_tst/shared/widgets/app_text_field.dart';
import 'package:intl/intl.dart';

class EmployeeJobSection extends ConsumerWidget {
  final TeammateDetailModel teammate;

  const EmployeeJobSection({super.key, required this.teammate});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final labelStyle = context.textStyles.bodyMedium?.copyWith(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      color: context.colors.onSurface,
    );
    return Column(
      spacing: 16,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Space.y(0),
        Text('Job Details', style: labelStyle),
        AppTextField(
          readOnly: true,
          labelText: 'Office',
          controller: TextEditingController(
            text: teammate.user.details?.office?.name,
          ),
        ),
        AppTextField(
          readOnly: true,
          labelText: 'Reporting Manager',
          controller: TextEditingController(
            text:
                ref
                    .watch(
                      teammateByIdProvider(teammate.user.details?.managerId),
                    )
                    .value
                    ?.firstName ??
                '',
          ),
        ),
        AppTextField(
          readOnly: true,
          labelText: 'Designation',
          controller: TextEditingController(
            text: teammate.user.details?.designation?.name,
          ),
        ),
        Row(
          spacing: 12,
          children: [
            Expanded(
              child: AppTextField(
                readOnly: true,
                labelText: 'Joining Date',
                controller: TextEditingController(
                  text: teammate.user.details?.joiningDate == null
                      ? ''
                      : DateFormat(
                          'dd MMM yyyy',
                        ).format(teammate.user.details!.joiningDate!.toLocal()),
                ),
              ),
            ),
            Expanded(
              child: AppTextField(
                readOnly: true,
                labelText: 'Time Type',
                controller: TextEditingController(
                  text: (teammate.user.details?.partTime ?? false)
                      ? 'Part Time'
                      : 'Full Time',
                ),
              ),
            ),
          ],
        ),
        AppTextField(
          readOnly: true,
          labelText: 'Departments',
          controller: TextEditingController(
            text: teammate.user.details?.departments
                ?.map((e) => e.name)
                .join(', '),
          ),
        ),
        AppTextField(
          readOnly: true,
          labelText: 'Job Role',
          controller: TextEditingController(
            text: teammate.user.details?.jobRoles
                ?.map((e) => e.name)
                .join(', '),
          ),
        ),
      ],
    );
  }
}
