import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/teammates/data/models/teammate_detail.model.dart';
import 'package:hrms_tst/shared/widgets/app_html_view.dart';

class EmployeeAboutSection extends ConsumerWidget {
  final TeammateDetailModel teammate;

  const EmployeeAboutSection({super.key, required this.teammate});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final labelStyle = context.textStyles.bodyMedium?.copyWith(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      color: context.colors.onSurface,
    );
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text('About', style: labelStyle),
          Space.y(4),
          HtmlView(htmlContent: teammate.user.details?.about),
          Text('Interests', style: labelStyle),
          Space.y(4),
          HtmlView(htmlContent: teammate.user.details?.interests),
          Text('Hobbies', style: labelStyle),
          Space.y(4),
          HtmlView(htmlContent: teammate.user.details?.hobbies),
        ],
      ),
    );
  }
}
