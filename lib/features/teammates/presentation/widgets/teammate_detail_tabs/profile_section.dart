import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/extensions/string.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/teammates/data/models/teammate_detail.model.dart';
import 'package:hrms_tst/shared/widgets/app_text_field.dart';
import 'package:intl/intl.dart';

class EmployeeProfileSection extends ConsumerWidget {
  final TeammateDetailModel teammate;

  const EmployeeProfileSection({super.key, required this.teammate});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final labelStyle = context.textStyles.bodyMedium?.copyWith(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      color: context.colors.onSurface,
    );
    return Column(
      spacing: 16,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Space.y(0),
        Text('Primary Details', style: labelStyle),
        AppTextField(
          readOnly: true,
          labelText: 'First name',
          controller: TextEditingController(text: teammate.user.firstName),
        ),
        AppTextField(
          readOnly: true,
          labelText: 'Middle name',
          controller: TextEditingController(text: teammate.user.middleName),
        ),
        AppTextField(
          readOnly: true,
          labelText: 'Last name',
          controller: TextEditingController(text: teammate.user.lastName),
        ),
        AppTextField(
          readOnly: true,
          labelText: 'Personal Mobile No.',
          controller: TextEditingController(text: teammate.user.mobile),
        ),
        AppTextField(
          readOnly: true,
          labelText: 'Personal Email',
          controller: TextEditingController(text: teammate.user.personalEmail),
        ),
        Text('Additional Details', style: labelStyle),
        Row(
          spacing: 12,
          children: [
            Expanded(
              child: AppTextField(
                readOnly: true,
                labelText: 'Gender',
                controller: TextEditingController(
                  text: teammate.user.gender?.name.capitalize(),
                ),
              ),
            ),
            Expanded(
              child: AppTextField(
                readOnly: true,
                labelText: 'Date of Birth',
                controller: TextEditingController(
                  text: teammate.user.dob == null
                      ? ''
                      : DateFormat(
                          'dd MMM yyyy',
                        ).format(teammate.user.dob!.toLocal()),
                ),
              ),
            ),
          ],
        ),
        Row(
          spacing: 12,
          children: [
            Expanded(
              child: AppTextField(
                readOnly: true,
                labelText: 'Blood Group',
                controller: TextEditingController(
                  text: teammate.user.details?.bloodGroup,
                ),
              ),
            ),
            Expanded(
              child: AppTextField(
                readOnly: true,
                labelText: 'Marital Status',
                controller: TextEditingController(
                  text: teammate.user.details?.maritalStatus?.capitalize(),
                ),
              ),
            ),
          ],
        ),
        Text('Address', style: labelStyle),
        AppTextField(
          readOnly: true,
          maxLines: 3,
          controller: TextEditingController(
            text: teammate.user.details?.currentAddr,
          ),
        ),
      ],
    );
  }
}
