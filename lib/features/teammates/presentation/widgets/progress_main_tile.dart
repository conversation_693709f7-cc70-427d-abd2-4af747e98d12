import 'package:flutter/material.dart';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/extensions/string.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/app_html_view.dart';
import 'package:hrms_tst/shared/widgets/cached_image.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';
import 'package:url_launcher/url_launcher.dart';

class MainProgressTile extends ConsumerWidget {
  const MainProgressTile({
    super.key,
    this.header,
    this.userImage,
    this.userName,
    this.userSubtitle,
    this.userLabel,
    this.userLabelContent,
    this.content,
    this.contentLink,
    this.courseLink,
    this.body,
    this.bottomIconPath,
    this.bottomLabel,
    this.photos,
    this.status,
    this.otherOptionsForOwner,
  });

  final Widget? header;
  final String? userImage;
  final String? userName;
  final String? userSubtitle;
  final String? userLabel;
  final String? userLabelContent;
  final String? content;
  final String? contentLink;
  final String? courseLink;
  final String? body;
  final String? bottomIconPath;
  final String? bottomLabel;
  final List<String>? photos;
  final String? status;
  final Widget? otherOptionsForOwner;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: context.colors.outlineVariant),
        ),
        child: Column(
          children: [
            if (header != null) header!,
            _userTile(context: context),
            Space.y(16),
            _content(context: context),
            Space.y(8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Space.y(16),
                Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () => showDetails(context: context),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            vertical: 8,
                            horizontal: 16,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            color: context.colors.outline.withValues(
                              alpha: 0.4,
                            ),
                            border: Border.all(
                              color: context.colors.outlineVariant,
                            ),
                          ),
                          child: Row(
                            children: [
                              if (bottomIconPath != null)
                                SVGImage(
                                  bottomIconPath!,
                                  height: 24,
                                  width: 24,
                                  color: context.colors.secondary,
                                ),
                              Space.x(12),
                              Text(
                                bottomLabel ?? '',
                                style: context.textStyles.bodyLarge?.copyWith(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    if (status != null) ...[
                      Space.x(12),
                      Tooltip(
                        triggerMode: TooltipTriggerMode.tap,
                        message: '${status?.capitalize()}',
                        child: SVGImage(
                          switch (status) {
                            'verified' => Assets.svgs.recordVerified,
                            'pending' => Assets.svgs.recordVerificationPending,
                            _ => Assets.svgs.recordVerificationPending,
                          },
                          height: 22,
                          width: 22,
                          applyColor: false,
                        ),
                      ),
                      Space.x(8),
                      if (otherOptionsForOwner != null) otherOptionsForOwner!,
                    ],
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _userTile({required BuildContext context}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          CachedImage(
            url:
                userImage ??
                'https://images.pexels.com/photos/32695045/pexels-photo-32695045.jpeg',
            height: 44,
            width: 44,
            decoration: BoxDecoration(shape: BoxShape.circle),
          ),
          Space.x(8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                userName ?? '',
                style: context.textStyles.bodyLarge?.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: context.colors.onSurface,
                ),
              ),
              Text(
                userSubtitle ?? '',
                style: context.textStyles.bodySmall?.copyWith(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: context.colors.secondary,
                ),
              ),
            ],
          ),
          Spacer(),
          Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                userLabel ?? '',
                style: context.textStyles.bodySmall?.copyWith(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: context.colors.secondary,
                ),
              ),
              Text(
                userLabelContent ?? '',
                style: context.textStyles.bodySmall?.copyWith(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: context.colors.onSurface,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _content({required BuildContext context}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          Expanded(
            child: Text(
              content ?? '',
              style: context.textStyles.titleSmall?.copyWith(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: context.colors.onSurface,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (courseLink != null && courseLink!.isNotEmpty)
            GestureDetector(
              onTap: () async => await launchUrl(Uri.parse(courseLink!)),
              child: Icon(Icons.link, color: context.colors.primary),
            ),

          if (contentLink != null && contentLink!.isNotEmpty) ...[
            Space.x(8),
            GestureDetector(
              onTap: () async => await launchUrl(Uri.parse(contentLink!)),
              child: SVGImage(
                Assets.svgs.attachmentLink,
                height: 18,
                width: 18,
                color: context.colors.primary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  void showDetails({required BuildContext context}) async {
    return showModalBottomSheet(
      useRootNavigator: true,
      context: context,
      clipBehavior: Clip.antiAlias,
      backgroundColor: context.colors.surface,
      scrollControlDisabledMaxHeightRatio: 0.6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Align(
                child: Container(
                  height: 5,
                  width: 140,
                  margin: EdgeInsets.all(8),
                  decoration: ShapeDecoration(
                    shape: StadiumBorder(),
                    color: context.colors.outline,
                  ),
                ),
              ),
              Space.y(32),
              _userTile(context: context),
              Space.y(16),
              _content(context: context),
              if (photos != null && photos!.isNotEmpty)
                Padding(
                  padding: EdgeInsetsGeometry.only(top: 16),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      spacing: 8,
                      children:
                          photos
                              ?.map(
                                (e) => CachedImage(
                                  url: e,
                                  height: context.screenHeight * 0.18,
                                  width: context.screenHeight * 0.18,
                                ),
                              )
                              .toList() ??
                          [],
                    ),
                  ),
                ),
              Space.y(16),
              Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: context.colors.outline.withValues(alpha: 0.4),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Text('Note:', style: context.textStyles.bodyLarge),
                      Divider(height: 1),
                      Space.y(4),
                      Expanded(
                        child: SingleChildScrollView(
                          child: HtmlView(htmlContent: body),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Space.y(16),
            ],
          ),
        );
      },
    );
  }
}
