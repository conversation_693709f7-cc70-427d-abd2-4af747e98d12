import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/teammates/domain/teammates.controller.dart';

class TeammateListFilterSheet extends HookConsumerWidget {
  const TeammateListFilterSheet({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notifier = ref.read(teammatesProvider.notifier);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Align(
          child: Container(
            height: 5,
            width: 140,
            margin: EdgeInsets.all(8),
            decoration: ShapeDecoration(
              shape: StadiumBorder(),
              color: context.colors.outline,
            ),
          ),
        ),
        Expanded(
          child: StatefulBuilder(
            builder: (context, updateFilters) {
              return Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 13),
                      child: Row(
                        children: [
                          Text(
                            'Apply Filter',
                            style: context.textStyles.bodyLarge?.copyWith(
                              color: context.colors.onSurface,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Spacer(),
                          GestureDetector(
                            onTap: () async {
                              notifier.clearFilters();
                              notifier.applyFilters();
                              updateFilters(() {});
                              context.pop();
                            },
                            child: Text(
                              'Clear All',
                              style: context.textStyles.bodyLarge?.copyWith(
                                color: context.colors.primary,
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Space.y(16),
                    Expanded(child: TeammateFilterOptions()),
                    ElevatedButton.icon(
                      onPressed: () async {
                        notifier.applyFilters();
                        context.pop();
                      },
                      label: Text('Apply'),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

class TeammateFilterOptions extends HookConsumerWidget {
  const TeammateFilterOptions({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notifier = ref.read(teammatesProvider.notifier);

    final selectedFilterIndex = useState(0);
    return StatefulBuilder(
      builder: (context, updateFilters) {
        return Column(
          children: [
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                spacing: 8,
                children: notifier.filters.indexed.map((e) {
                  final hasValue = e.$2.selectedOption != null;
                  final isChosen = e.$1 == selectedFilterIndex.value;
                  return GestureDetector(
                    onTap: () {
                      selectedFilterIndex.value = e.$1;
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: hasValue
                            ? context.colors.primary
                            : isChosen
                            ? context.colors.secondary.withValues(alpha: 0.7)
                            : context.colors.outline,
                      ),
                      padding: EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 6,
                      ),
                      child: Row(
                        children: [
                          Text(
                            hasValue ? e.$2.selectedOption.name : e.$2.label,
                            style: context.textStyles.titleMedium?.copyWith(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: isChosen || hasValue
                                  ? context.colors.surface
                                  : context.colors.secondary,
                            ),
                          ),
                          if (hasValue) ...[
                            VerticalDivider(
                              color: context.colors.outline,
                              width: 1,
                            ),
                            GestureDetector(
                              onTap: () {
                                notifier.clearSelectedFilter(e.$1);
                                updateFilters(() {});
                              },
                              child: Icon(
                                Icons.close,
                                color: context.colors.onPrimary,
                                size: 20,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
            Space.y(8),
            Expanded(
              child: ListView.separated(
                itemCount:
                    notifier.filters[selectedFilterIndex.value].options.length,
                separatorBuilder: (context, index) {
                  return Divider(color: context.colors.outline, height: 1);
                },
                itemBuilder: (context, index) {
                  return ListTile(
                    splashColor: Colors.transparent,

                    onTap: () {
                      notifier.selectFilter(selectedFilterIndex.value, index);
                      updateFilters(() {});
                    },
                    dense: true,
                    contentPadding: EdgeInsets.only(left: 4, right: 8),

                    title: Text(
                      notifier
                          .filters[selectedFilterIndex.value]
                          .options[index]
                          .label,
                      style: context.textStyles.bodyMedium?.copyWith(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    trailing: SizedBox.square(
                      dimension: 18,
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: context.colors.outline,
                            width: 2,
                          ),
                        ),
                        child:
                            notifier
                                    .filters[selectedFilterIndex.value]
                                    .selectedOption ==
                                notifier
                                    .filters[selectedFilterIndex.value]
                                    .options[index]
                                    .data
                            ? Container(
                                margin: EdgeInsets.all(0.5),
                                decoration: BoxDecoration(
                                  color: context.colors.secondary.withValues(
                                    alpha: 0.8,
                                  ),
                                  shape: BoxShape.circle,
                                ),
                              )
                            : null,
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }
}
