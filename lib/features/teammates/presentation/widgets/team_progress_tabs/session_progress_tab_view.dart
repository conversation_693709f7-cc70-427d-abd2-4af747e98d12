import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/core/utils/helpers/snackbar.helper.dart';
import 'package:hrms_tst/features/employee/domain/progress_record.controller.dart';
import 'package:hrms_tst/features/employee/presentation/widgets/progress_record_forms/session_record_form.dart';
import 'package:hrms_tst/features/teammates/data/models/team_progress.model.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/providers/auth.provider.dart';
import 'package:hrms_tst/shared/widgets/confirmation_dialog.dart';
import 'package:hrms_tst/shared/widgets/no_data_view.dart';
import 'package:intl/intl.dart';
import '../progress_main_tile.dart';
import '../../../../../core/utils/helpers/space.dart';

class SessionProgressTabView extends ConsumerWidget {
  final AsyncValue<List<SessionProgressModel>> state;
  final bool showStatus;
  const SessionProgressTabView({
    super.key,
    required this.state,
    this.showStatus = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return state.when(
      data: (value) => value.isEmpty
          ? NoDataView()
          : ListView.builder(
              itemCount: value.length,
              itemBuilder: (context, index) => MainProgressTile(
                header: value[index].topic != null
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            value[index].topic!,
                            style: context.textStyles.bodyLarge?.copyWith(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Space.y(8),
                          const Divider(),
                          Space.y(16),
                        ],
                      )
                    : null,
                userImage: value[index].employee?.image ?? '',
                userName: value[index].employee?.displayName ?? '',
                userSubtitle: value[index].completedAt != null
                    ? DateFormat(
                        'dd-MM-yyyy',
                      ).format(value[index].completedAt!.toLocal())
                    : '',
                content: value[index].name,
                body: value[index].notes,
                contentLink: value[index].docLink,
                bottomIconPath: Assets.svgs.attachedNoteIcon,
                bottomLabel: showStatus ? 'Note' : 'View Detailed Note Here',
                status: showStatus ? value[index].status : null,
                otherOptionsForOwner: otherOptionsForOwner(
                  context,
                  ref,
                  value[index],
                ),
              ),
            ),
      error: (error, _) => Center(child: Text(error.toString())),
      loading: () => const Center(child: CircularProgressIndicator.adaptive()),
    );
  }

  Widget? otherOptionsForOwner(
    BuildContext context,
    WidgetRef ref,
    SessionProgressModel sessionProgress,
  ) {
    return ref.read(authProvider).requireValue!.details?.id ==
            sessionProgress.employeeId
        ? Row(
            children: [
              Space.x(4),
              InkWell(
                borderRadius: BorderRadius.circular(24),
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (context) =>
                        AddSessionRecordForm(sessionProgress: sessionProgress),
                  );
                },
                child: Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: Icon(Icons.edit, color: context.colors.primary),
                ),
              ),
              Space.x(4),
              InkWell(
                borderRadius: BorderRadius.circular(24),
                onTap: () {
                  showAdaptiveDialog(
                    context: context,
                    builder: (context) => ConfirmationDialog(
                      title: 'Delete Session Progress',
                      cancelText: 'No',
                      confirmText: 'Yes',
                      onConfirm: () async {
                        final result = await ref
                            .read(addProgressRecordProvider.notifier)
                            .deleteSessionProgressRecord(
                              id: sessionProgress.id,
                            );
                        result.fold(
                          onSuccess: (data) {},
                          onFailure: (message) => context.showError(message),
                        );
                      },
                    ),
                  );
                },
                child: Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: Icon(Icons.delete, color: context.colors.error),
                ),
              ),
            ],
          )
        : null;
  }
}
