import 'package:freezed_annotation/freezed_annotation.dart';

part 'team_progress.model.freezed.dart';
part 'team_progress.model.g.dart';

EmployeeData employeeDataFromJson(Map<String, dynamic> json) =>
    EmployeeData.fromJson(json['user']);

@freezed
abstract class EmployeeData with _$EmployeeData {
  const factory EmployeeData({
    required final int id,
    required final String displayName,
    required final String? image,
  }) = _EmployeeData;

  factory EmployeeData.fromJson(Map<String, dynamic> json) =>
      _$EmployeeDataFromJson(json);
}

@freezed
abstract class BookProgressModel with _$BookProgressModel {
  @JsonSerializable(explicitToJson: true)
  const factory BookProgressModel({
    required final int id,
    final String? name,
    final DateTime? completedAt,
    final String? notes,
    final String? docLink,
    final int? pages,
    final String? status,
    final DateTime? createdAt,
    final DateTime? updatedAt,
    @J<PERSON><PERSON>ey(name: 'EmployeeId') final int? employeeId,
    @J<PERSON><PERSON><PERSON>(name: 'VerifierEmpId') final int? verifierEmpId,
    @J<PERSON><PERSON><PERSON>(fromJson: employeeDataFromJson) final EmployeeData? employee,
  }) = _BookProgressModel;

  factory BookProgressModel.fromJson(Map<String, dynamic> json) =>
      _$BookProgressModelFromJson(json);
}

@freezed
abstract class CourseProgressModel with _$CourseProgressModel {
  @JsonSerializable(explicitToJson: true)
  const factory CourseProgressModel({
    required final int id,
    final String? name,
    final DateTime? completedAt,
    final String? courseLink,
    final String? notes,
    final String? docLink,
    final String? status,
    final DateTime? createdAt,
    final DateTime? updatedAt,
    @JsonKey(name: 'EmployeeId') final int? employeeId,
    @JsonKey(name: 'VerifierEmpId') final int? verifierEmpId,
    @JsonKey(fromJson: employeeDataFromJson) final EmployeeData? employee,
  }) = _CourseProgressModel;

  factory CourseProgressModel.fromJson(Map<String, dynamic> json) =>
      _$CourseProgressModelFromJson(json);
}

@freezed
abstract class GDProgressModel with _$GDProgressModel {
  @JsonSerializable(explicitToJson: true)
  const factory GDProgressModel({
    required final int id,
    final String? name,
    final String? topic,
    final DateTime? completedAt,
    final String? notes,
    final String? docLink,
    final String? status,
    final DateTime? createdAt,
    final DateTime? updatedAt,
    @JsonKey(name: 'EmployeeId') final int? employeeId,
    @JsonKey(name: 'VerifierEmpId') final int? verifierEmpId,
    @JsonKey(fromJson: employeeDataFromJson) final EmployeeData? employee,
  }) = _GDProgressModel;

  factory GDProgressModel.fromJson(Map<String, dynamic> json) =>
      _$GDProgressModelFromJson(json);
}

@freezed
abstract class SessionProgressModel with _$SessionProgressModel {
  @JsonSerializable(explicitToJson: true)
  const factory SessionProgressModel({
    required final int id,
    final String? name,
    final String? topic,
    final DateTime? completedAt,
    final String? notes,
    final String? docLink,
    final String? status,
    final DateTime? createdAt,
    final DateTime? updatedAt,
    @JsonKey(name: 'EmployeeId') final int? employeeId,
    @JsonKey(name: 'VerifierEmpId') final int? verifierEmpId,
    @JsonKey(fromJson: employeeDataFromJson) final EmployeeData? employee,
  }) = _SessionProgressModel;

  factory SessionProgressModel.fromJson(Map<String, dynamic> json) =>
      _$SessionProgressModelFromJson(json);
}

@freezed
abstract class EventProgressModel with _$EventProgressModel {
  @JsonSerializable(explicitToJson: true)
  const factory EventProgressModel({
    required final int id,
    final String? name,
    final String? topic,
    final DateTime? completedAt,
    final String? notes,
    final String? link,
    final String? status,
    final List<String>? photos,
    final DateTime? startDate,
    final DateTime? endDate,
    final DateTime? createdAt,
    final DateTime? updatedAt,
    @JsonKey(name: 'EmployeeId') final int? employeeId,
    @JsonKey(name: 'VerifierEmpId') final int? verifierEmpId,
    @JsonKey(fromJson: employeeDataFromJson) final EmployeeData? employee,
  }) = _EventProgressModel;

  factory EventProgressModel.fromJson(Map<String, dynamic> json) =>
      _$EventProgressModelFromJson(json);
}
