// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'team_progress.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_EmployeeData _$EmployeeDataFromJson(Map<String, dynamic> json) =>
    _EmployeeData(
      id: (json['id'] as num).toInt(),
      displayName: json['displayName'] as String,
      image: json['image'] as String?,
    );

Map<String, dynamic> _$EmployeeDataToJson(_EmployeeData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'displayName': instance.displayName,
      'image': instance.image,
    };

_BookProgressModel _$BookProgressModelFromJson(Map<String, dynamic> json) =>
    _BookProgressModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String?,
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      notes: json['notes'] as String?,
      docLink: json['docLink'] as String?,
      pages: (json['pages'] as num?)?.toInt(),
      status: json['status'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      employeeId: (json['EmployeeId'] as num?)?.toInt(),
      verifierEmpId: (json['VerifierEmpId'] as num?)?.toInt(),
      employee: employeeDataFromJson(json['employee'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$BookProgressModelToJson(_BookProgressModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'completedAt': instance.completedAt?.toIso8601String(),
      'notes': instance.notes,
      'docLink': instance.docLink,
      'pages': instance.pages,
      'status': instance.status,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'EmployeeId': instance.employeeId,
      'VerifierEmpId': instance.verifierEmpId,
      'employee': instance.employee?.toJson(),
    };

_CourseProgressModel _$CourseProgressModelFromJson(Map<String, dynamic> json) =>
    _CourseProgressModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String?,
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      courseLink: json['courseLink'] as String?,
      notes: json['notes'] as String?,
      docLink: json['docLink'] as String?,
      status: json['status'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      employeeId: (json['EmployeeId'] as num?)?.toInt(),
      verifierEmpId: (json['VerifierEmpId'] as num?)?.toInt(),
      employee: employeeDataFromJson(json['employee'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CourseProgressModelToJson(
  _CourseProgressModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'completedAt': instance.completedAt?.toIso8601String(),
  'courseLink': instance.courseLink,
  'notes': instance.notes,
  'docLink': instance.docLink,
  'status': instance.status,
  'createdAt': instance.createdAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
  'EmployeeId': instance.employeeId,
  'VerifierEmpId': instance.verifierEmpId,
  'employee': instance.employee?.toJson(),
};

_GDProgressModel _$GDProgressModelFromJson(Map<String, dynamic> json) =>
    _GDProgressModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String?,
      topic: json['topic'] as String?,
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      notes: json['notes'] as String?,
      docLink: json['docLink'] as String?,
      status: json['status'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      employeeId: (json['EmployeeId'] as num?)?.toInt(),
      verifierEmpId: (json['VerifierEmpId'] as num?)?.toInt(),
      employee: employeeDataFromJson(json['employee'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GDProgressModelToJson(_GDProgressModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'topic': instance.topic,
      'completedAt': instance.completedAt?.toIso8601String(),
      'notes': instance.notes,
      'docLink': instance.docLink,
      'status': instance.status,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'EmployeeId': instance.employeeId,
      'VerifierEmpId': instance.verifierEmpId,
      'employee': instance.employee?.toJson(),
    };

_SessionProgressModel _$SessionProgressModelFromJson(
  Map<String, dynamic> json,
) => _SessionProgressModel(
  id: (json['id'] as num).toInt(),
  name: json['name'] as String?,
  topic: json['topic'] as String?,
  completedAt: json['completedAt'] == null
      ? null
      : DateTime.parse(json['completedAt'] as String),
  notes: json['notes'] as String?,
  docLink: json['docLink'] as String?,
  status: json['status'] as String?,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  employeeId: (json['EmployeeId'] as num?)?.toInt(),
  verifierEmpId: (json['VerifierEmpId'] as num?)?.toInt(),
  employee: employeeDataFromJson(json['employee'] as Map<String, dynamic>),
);

Map<String, dynamic> _$SessionProgressModelToJson(
  _SessionProgressModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'topic': instance.topic,
  'completedAt': instance.completedAt?.toIso8601String(),
  'notes': instance.notes,
  'docLink': instance.docLink,
  'status': instance.status,
  'createdAt': instance.createdAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
  'EmployeeId': instance.employeeId,
  'VerifierEmpId': instance.verifierEmpId,
  'employee': instance.employee?.toJson(),
};

_EventProgressModel _$EventProgressModelFromJson(Map<String, dynamic> json) =>
    _EventProgressModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String?,
      topic: json['topic'] as String?,
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      notes: json['notes'] as String?,
      link: json['link'] as String?,
      status: json['status'] as String?,
      photos: (json['photos'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      employeeId: (json['EmployeeId'] as num?)?.toInt(),
      verifierEmpId: (json['VerifierEmpId'] as num?)?.toInt(),
      employee: employeeDataFromJson(json['employee'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$EventProgressModelToJson(_EventProgressModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'topic': instance.topic,
      'completedAt': instance.completedAt?.toIso8601String(),
      'notes': instance.notes,
      'link': instance.link,
      'status': instance.status,
      'photos': instance.photos,
      'startDate': instance.startDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'EmployeeId': instance.employeeId,
      'VerifierEmpId': instance.verifierEmpId,
      'employee': instance.employee?.toJson(),
    };
