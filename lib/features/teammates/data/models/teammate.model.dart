import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hrms_tst/shared/models/user.model.dart';

part 'teammate.model.freezed.dart';
part 'teammate.model.g.dart';

@freezed
abstract class TeammateModel with _$TeammateModel {
  @JsonSerializable(explicitToJson: true)
  const factory TeammateModel({
    required final int id,
    required final int empNo,
    final String? email,
    final DateTime? createdAt,
    required final UserModel user,
    final OfficeModel? office,
    final DesignationModel? designation,
    required final List<DepartmentModel> departments,
    required final List<JobRoleModel> jobRoles,
  }) = _TeammateModel;

  factory TeammateModel.fromJson(Map<String, dynamic> json) =>
      _$TeammateModelFromJson(json);
}

@freezed
abstract class OfficeModel with _$OfficeModel {
  const factory OfficeModel({
    required final int id,
    required final String name,
    required final String city,
    final String? state,
    final String? country,
    @JsonKey(name: 'OrganizationId') required final int? organizationId,
  }) = _OfficeModel;

  factory OfficeModel.fromJson(Map<String, dynamic> json) =>
      _$OfficeModelFromJson(json);
}

@freezed
abstract class DesignationModel with _$DesignationModel {
  const factory DesignationModel({
    required final int id,
    required final String name,
    @JsonKey(name: 'OrganizationId') final int? organizationId,
  }) = _DesignationModel;

  factory DesignationModel.fromJson(Map<String, dynamic> json) =>
      _$DesignationModelFromJson(json);
}

@freezed
abstract class DepartmentModel with _$DepartmentModel {
  const factory DepartmentModel({
    required final int id,
    required final String name,
    required final String? type,
    @JsonKey(name: 'OrganizationId') final int? organizationId,
  }) = _DepartmentModel;

  factory DepartmentModel.fromJson(Map<String, dynamic> json) =>
      _$DepartmentModelFromJson(json);
}

@freezed
abstract class JobRoleModel with _$JobRoleModel {
  const factory JobRoleModel({
    required final int id,
    required final String name,
    @JsonKey(name: 'OrganizationId') final int? organizationId,
  }) = _JobRoleModel;

  factory JobRoleModel.fromJson(Map<String, dynamic> json) =>
      _$JobRoleModelFromJson(json);
}

@freezed
abstract class WorkModeModel with _$WorkModeModel {
  const factory WorkModeModel({
    required final String id,
    required final String name,
  }) = _WorkModeModel;

  factory WorkModeModel.fromJson(Map<String, dynamic> json) =>
      _$WorkModeModelFromJson(json);
}
