// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'teammate.model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TeammateModel {

 int get id; int get empNo; String? get email; DateTime? get createdAt; UserModel get user; OfficeModel? get office; DesignationModel? get designation; List<DepartmentModel> get departments; List<JobRoleModel> get jobRoles;
/// Create a copy of TeammateModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TeammateModelCopyWith<TeammateModel> get copyWith => _$TeammateModelCopyWithImpl<TeammateModel>(this as TeammateModel, _$identity);

  /// Serializes this TeammateModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TeammateModel&&(identical(other.id, id) || other.id == id)&&(identical(other.empNo, empNo) || other.empNo == empNo)&&(identical(other.email, email) || other.email == email)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.user, user) || other.user == user)&&(identical(other.office, office) || other.office == office)&&(identical(other.designation, designation) || other.designation == designation)&&const DeepCollectionEquality().equals(other.departments, departments)&&const DeepCollectionEquality().equals(other.jobRoles, jobRoles));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,empNo,email,createdAt,user,office,designation,const DeepCollectionEquality().hash(departments),const DeepCollectionEquality().hash(jobRoles));

@override
String toString() {
  return 'TeammateModel(id: $id, empNo: $empNo, email: $email, createdAt: $createdAt, user: $user, office: $office, designation: $designation, departments: $departments, jobRoles: $jobRoles)';
}


}

/// @nodoc
abstract mixin class $TeammateModelCopyWith<$Res>  {
  factory $TeammateModelCopyWith(TeammateModel value, $Res Function(TeammateModel) _then) = _$TeammateModelCopyWithImpl;
@useResult
$Res call({
 int id, int empNo, String? email, DateTime? createdAt, UserModel user, OfficeModel? office, DesignationModel? designation, List<DepartmentModel> departments, List<JobRoleModel> jobRoles
});


$UserModelCopyWith<$Res> get user;$OfficeModelCopyWith<$Res>? get office;$DesignationModelCopyWith<$Res>? get designation;

}
/// @nodoc
class _$TeammateModelCopyWithImpl<$Res>
    implements $TeammateModelCopyWith<$Res> {
  _$TeammateModelCopyWithImpl(this._self, this._then);

  final TeammateModel _self;
  final $Res Function(TeammateModel) _then;

/// Create a copy of TeammateModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? empNo = null,Object? email = freezed,Object? createdAt = freezed,Object? user = null,Object? office = freezed,Object? designation = freezed,Object? departments = null,Object? jobRoles = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,empNo: null == empNo ? _self.empNo : empNo // ignore: cast_nullable_to_non_nullable
as int,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,user: null == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as UserModel,office: freezed == office ? _self.office : office // ignore: cast_nullable_to_non_nullable
as OfficeModel?,designation: freezed == designation ? _self.designation : designation // ignore: cast_nullable_to_non_nullable
as DesignationModel?,departments: null == departments ? _self.departments : departments // ignore: cast_nullable_to_non_nullable
as List<DepartmentModel>,jobRoles: null == jobRoles ? _self.jobRoles : jobRoles // ignore: cast_nullable_to_non_nullable
as List<JobRoleModel>,
  ));
}
/// Create a copy of TeammateModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserModelCopyWith<$Res> get user {
  
  return $UserModelCopyWith<$Res>(_self.user, (value) {
    return _then(_self.copyWith(user: value));
  });
}/// Create a copy of TeammateModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$OfficeModelCopyWith<$Res>? get office {
    if (_self.office == null) {
    return null;
  }

  return $OfficeModelCopyWith<$Res>(_self.office!, (value) {
    return _then(_self.copyWith(office: value));
  });
}/// Create a copy of TeammateModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DesignationModelCopyWith<$Res>? get designation {
    if (_self.designation == null) {
    return null;
  }

  return $DesignationModelCopyWith<$Res>(_self.designation!, (value) {
    return _then(_self.copyWith(designation: value));
  });
}
}


/// @nodoc

@JsonSerializable(explicitToJson: true)
class _TeammateModel implements TeammateModel {
  const _TeammateModel({required this.id, required this.empNo, this.email, this.createdAt, required this.user, this.office, this.designation, required final  List<DepartmentModel> departments, required final  List<JobRoleModel> jobRoles}): _departments = departments,_jobRoles = jobRoles;
  factory _TeammateModel.fromJson(Map<String, dynamic> json) => _$TeammateModelFromJson(json);

@override final  int id;
@override final  int empNo;
@override final  String? email;
@override final  DateTime? createdAt;
@override final  UserModel user;
@override final  OfficeModel? office;
@override final  DesignationModel? designation;
 final  List<DepartmentModel> _departments;
@override List<DepartmentModel> get departments {
  if (_departments is EqualUnmodifiableListView) return _departments;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_departments);
}

 final  List<JobRoleModel> _jobRoles;
@override List<JobRoleModel> get jobRoles {
  if (_jobRoles is EqualUnmodifiableListView) return _jobRoles;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_jobRoles);
}


/// Create a copy of TeammateModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TeammateModelCopyWith<_TeammateModel> get copyWith => __$TeammateModelCopyWithImpl<_TeammateModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TeammateModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TeammateModel&&(identical(other.id, id) || other.id == id)&&(identical(other.empNo, empNo) || other.empNo == empNo)&&(identical(other.email, email) || other.email == email)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.user, user) || other.user == user)&&(identical(other.office, office) || other.office == office)&&(identical(other.designation, designation) || other.designation == designation)&&const DeepCollectionEquality().equals(other._departments, _departments)&&const DeepCollectionEquality().equals(other._jobRoles, _jobRoles));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,empNo,email,createdAt,user,office,designation,const DeepCollectionEquality().hash(_departments),const DeepCollectionEquality().hash(_jobRoles));

@override
String toString() {
  return 'TeammateModel(id: $id, empNo: $empNo, email: $email, createdAt: $createdAt, user: $user, office: $office, designation: $designation, departments: $departments, jobRoles: $jobRoles)';
}


}

/// @nodoc
abstract mixin class _$TeammateModelCopyWith<$Res> implements $TeammateModelCopyWith<$Res> {
  factory _$TeammateModelCopyWith(_TeammateModel value, $Res Function(_TeammateModel) _then) = __$TeammateModelCopyWithImpl;
@override @useResult
$Res call({
 int id, int empNo, String? email, DateTime? createdAt, UserModel user, OfficeModel? office, DesignationModel? designation, List<DepartmentModel> departments, List<JobRoleModel> jobRoles
});


@override $UserModelCopyWith<$Res> get user;@override $OfficeModelCopyWith<$Res>? get office;@override $DesignationModelCopyWith<$Res>? get designation;

}
/// @nodoc
class __$TeammateModelCopyWithImpl<$Res>
    implements _$TeammateModelCopyWith<$Res> {
  __$TeammateModelCopyWithImpl(this._self, this._then);

  final _TeammateModel _self;
  final $Res Function(_TeammateModel) _then;

/// Create a copy of TeammateModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? empNo = null,Object? email = freezed,Object? createdAt = freezed,Object? user = null,Object? office = freezed,Object? designation = freezed,Object? departments = null,Object? jobRoles = null,}) {
  return _then(_TeammateModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,empNo: null == empNo ? _self.empNo : empNo // ignore: cast_nullable_to_non_nullable
as int,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,user: null == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as UserModel,office: freezed == office ? _self.office : office // ignore: cast_nullable_to_non_nullable
as OfficeModel?,designation: freezed == designation ? _self.designation : designation // ignore: cast_nullable_to_non_nullable
as DesignationModel?,departments: null == departments ? _self._departments : departments // ignore: cast_nullable_to_non_nullable
as List<DepartmentModel>,jobRoles: null == jobRoles ? _self._jobRoles : jobRoles // ignore: cast_nullable_to_non_nullable
as List<JobRoleModel>,
  ));
}

/// Create a copy of TeammateModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserModelCopyWith<$Res> get user {
  
  return $UserModelCopyWith<$Res>(_self.user, (value) {
    return _then(_self.copyWith(user: value));
  });
}/// Create a copy of TeammateModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$OfficeModelCopyWith<$Res>? get office {
    if (_self.office == null) {
    return null;
  }

  return $OfficeModelCopyWith<$Res>(_self.office!, (value) {
    return _then(_self.copyWith(office: value));
  });
}/// Create a copy of TeammateModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DesignationModelCopyWith<$Res>? get designation {
    if (_self.designation == null) {
    return null;
  }

  return $DesignationModelCopyWith<$Res>(_self.designation!, (value) {
    return _then(_self.copyWith(designation: value));
  });
}
}


/// @nodoc
mixin _$OfficeModel {

 int get id; String get name; String get city; String? get state; String? get country;@JsonKey(name: 'OrganizationId') int? get organizationId;
/// Create a copy of OfficeModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OfficeModelCopyWith<OfficeModel> get copyWith => _$OfficeModelCopyWithImpl<OfficeModel>(this as OfficeModel, _$identity);

  /// Serializes this OfficeModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OfficeModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&(identical(other.country, country) || other.country == country)&&(identical(other.organizationId, organizationId) || other.organizationId == organizationId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,city,state,country,organizationId);

@override
String toString() {
  return 'OfficeModel(id: $id, name: $name, city: $city, state: $state, country: $country, organizationId: $organizationId)';
}


}

/// @nodoc
abstract mixin class $OfficeModelCopyWith<$Res>  {
  factory $OfficeModelCopyWith(OfficeModel value, $Res Function(OfficeModel) _then) = _$OfficeModelCopyWithImpl;
@useResult
$Res call({
 int id, String name, String city, String? state, String? country,@JsonKey(name: 'OrganizationId') int? organizationId
});




}
/// @nodoc
class _$OfficeModelCopyWithImpl<$Res>
    implements $OfficeModelCopyWith<$Res> {
  _$OfficeModelCopyWithImpl(this._self, this._then);

  final OfficeModel _self;
  final $Res Function(OfficeModel) _then;

/// Create a copy of OfficeModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? city = null,Object? state = freezed,Object? country = freezed,Object? organizationId = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,city: null == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String,state: freezed == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String?,country: freezed == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String?,organizationId: freezed == organizationId ? _self.organizationId : organizationId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _OfficeModel implements OfficeModel {
  const _OfficeModel({required this.id, required this.name, required this.city, this.state, this.country, @JsonKey(name: 'OrganizationId') required this.organizationId});
  factory _OfficeModel.fromJson(Map<String, dynamic> json) => _$OfficeModelFromJson(json);

@override final  int id;
@override final  String name;
@override final  String city;
@override final  String? state;
@override final  String? country;
@override@JsonKey(name: 'OrganizationId') final  int? organizationId;

/// Create a copy of OfficeModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OfficeModelCopyWith<_OfficeModel> get copyWith => __$OfficeModelCopyWithImpl<_OfficeModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$OfficeModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OfficeModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&(identical(other.country, country) || other.country == country)&&(identical(other.organizationId, organizationId) || other.organizationId == organizationId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,city,state,country,organizationId);

@override
String toString() {
  return 'OfficeModel(id: $id, name: $name, city: $city, state: $state, country: $country, organizationId: $organizationId)';
}


}

/// @nodoc
abstract mixin class _$OfficeModelCopyWith<$Res> implements $OfficeModelCopyWith<$Res> {
  factory _$OfficeModelCopyWith(_OfficeModel value, $Res Function(_OfficeModel) _then) = __$OfficeModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String name, String city, String? state, String? country,@JsonKey(name: 'OrganizationId') int? organizationId
});




}
/// @nodoc
class __$OfficeModelCopyWithImpl<$Res>
    implements _$OfficeModelCopyWith<$Res> {
  __$OfficeModelCopyWithImpl(this._self, this._then);

  final _OfficeModel _self;
  final $Res Function(_OfficeModel) _then;

/// Create a copy of OfficeModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? city = null,Object? state = freezed,Object? country = freezed,Object? organizationId = freezed,}) {
  return _then(_OfficeModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,city: null == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String,state: freezed == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String?,country: freezed == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String?,organizationId: freezed == organizationId ? _self.organizationId : organizationId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}


/// @nodoc
mixin _$DesignationModel {

 int get id; String get name;@JsonKey(name: 'OrganizationId') int? get organizationId;
/// Create a copy of DesignationModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DesignationModelCopyWith<DesignationModel> get copyWith => _$DesignationModelCopyWithImpl<DesignationModel>(this as DesignationModel, _$identity);

  /// Serializes this DesignationModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DesignationModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.organizationId, organizationId) || other.organizationId == organizationId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,organizationId);

@override
String toString() {
  return 'DesignationModel(id: $id, name: $name, organizationId: $organizationId)';
}


}

/// @nodoc
abstract mixin class $DesignationModelCopyWith<$Res>  {
  factory $DesignationModelCopyWith(DesignationModel value, $Res Function(DesignationModel) _then) = _$DesignationModelCopyWithImpl;
@useResult
$Res call({
 int id, String name,@JsonKey(name: 'OrganizationId') int? organizationId
});




}
/// @nodoc
class _$DesignationModelCopyWithImpl<$Res>
    implements $DesignationModelCopyWith<$Res> {
  _$DesignationModelCopyWithImpl(this._self, this._then);

  final DesignationModel _self;
  final $Res Function(DesignationModel) _then;

/// Create a copy of DesignationModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? organizationId = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,organizationId: freezed == organizationId ? _self.organizationId : organizationId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _DesignationModel implements DesignationModel {
  const _DesignationModel({required this.id, required this.name, @JsonKey(name: 'OrganizationId') this.organizationId});
  factory _DesignationModel.fromJson(Map<String, dynamic> json) => _$DesignationModelFromJson(json);

@override final  int id;
@override final  String name;
@override@JsonKey(name: 'OrganizationId') final  int? organizationId;

/// Create a copy of DesignationModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DesignationModelCopyWith<_DesignationModel> get copyWith => __$DesignationModelCopyWithImpl<_DesignationModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DesignationModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DesignationModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.organizationId, organizationId) || other.organizationId == organizationId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,organizationId);

@override
String toString() {
  return 'DesignationModel(id: $id, name: $name, organizationId: $organizationId)';
}


}

/// @nodoc
abstract mixin class _$DesignationModelCopyWith<$Res> implements $DesignationModelCopyWith<$Res> {
  factory _$DesignationModelCopyWith(_DesignationModel value, $Res Function(_DesignationModel) _then) = __$DesignationModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String name,@JsonKey(name: 'OrganizationId') int? organizationId
});




}
/// @nodoc
class __$DesignationModelCopyWithImpl<$Res>
    implements _$DesignationModelCopyWith<$Res> {
  __$DesignationModelCopyWithImpl(this._self, this._then);

  final _DesignationModel _self;
  final $Res Function(_DesignationModel) _then;

/// Create a copy of DesignationModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? organizationId = freezed,}) {
  return _then(_DesignationModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,organizationId: freezed == organizationId ? _self.organizationId : organizationId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}


/// @nodoc
mixin _$DepartmentModel {

 int get id; String get name; String? get type;@JsonKey(name: 'OrganizationId') int? get organizationId;
/// Create a copy of DepartmentModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DepartmentModelCopyWith<DepartmentModel> get copyWith => _$DepartmentModelCopyWithImpl<DepartmentModel>(this as DepartmentModel, _$identity);

  /// Serializes this DepartmentModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepartmentModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.type, type) || other.type == type)&&(identical(other.organizationId, organizationId) || other.organizationId == organizationId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,type,organizationId);

@override
String toString() {
  return 'DepartmentModel(id: $id, name: $name, type: $type, organizationId: $organizationId)';
}


}

/// @nodoc
abstract mixin class $DepartmentModelCopyWith<$Res>  {
  factory $DepartmentModelCopyWith(DepartmentModel value, $Res Function(DepartmentModel) _then) = _$DepartmentModelCopyWithImpl;
@useResult
$Res call({
 int id, String name, String? type,@JsonKey(name: 'OrganizationId') int? organizationId
});




}
/// @nodoc
class _$DepartmentModelCopyWithImpl<$Res>
    implements $DepartmentModelCopyWith<$Res> {
  _$DepartmentModelCopyWithImpl(this._self, this._then);

  final DepartmentModel _self;
  final $Res Function(DepartmentModel) _then;

/// Create a copy of DepartmentModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? type = freezed,Object? organizationId = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,type: freezed == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String?,organizationId: freezed == organizationId ? _self.organizationId : organizationId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _DepartmentModel implements DepartmentModel {
  const _DepartmentModel({required this.id, required this.name, required this.type, @JsonKey(name: 'OrganizationId') this.organizationId});
  factory _DepartmentModel.fromJson(Map<String, dynamic> json) => _$DepartmentModelFromJson(json);

@override final  int id;
@override final  String name;
@override final  String? type;
@override@JsonKey(name: 'OrganizationId') final  int? organizationId;

/// Create a copy of DepartmentModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DepartmentModelCopyWith<_DepartmentModel> get copyWith => __$DepartmentModelCopyWithImpl<_DepartmentModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DepartmentModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DepartmentModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.type, type) || other.type == type)&&(identical(other.organizationId, organizationId) || other.organizationId == organizationId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,type,organizationId);

@override
String toString() {
  return 'DepartmentModel(id: $id, name: $name, type: $type, organizationId: $organizationId)';
}


}

/// @nodoc
abstract mixin class _$DepartmentModelCopyWith<$Res> implements $DepartmentModelCopyWith<$Res> {
  factory _$DepartmentModelCopyWith(_DepartmentModel value, $Res Function(_DepartmentModel) _then) = __$DepartmentModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String name, String? type,@JsonKey(name: 'OrganizationId') int? organizationId
});




}
/// @nodoc
class __$DepartmentModelCopyWithImpl<$Res>
    implements _$DepartmentModelCopyWith<$Res> {
  __$DepartmentModelCopyWithImpl(this._self, this._then);

  final _DepartmentModel _self;
  final $Res Function(_DepartmentModel) _then;

/// Create a copy of DepartmentModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? type = freezed,Object? organizationId = freezed,}) {
  return _then(_DepartmentModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,type: freezed == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String?,organizationId: freezed == organizationId ? _self.organizationId : organizationId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}


/// @nodoc
mixin _$JobRoleModel {

 int get id; String get name;@JsonKey(name: 'OrganizationId') int? get organizationId;
/// Create a copy of JobRoleModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$JobRoleModelCopyWith<JobRoleModel> get copyWith => _$JobRoleModelCopyWithImpl<JobRoleModel>(this as JobRoleModel, _$identity);

  /// Serializes this JobRoleModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is JobRoleModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.organizationId, organizationId) || other.organizationId == organizationId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,organizationId);

@override
String toString() {
  return 'JobRoleModel(id: $id, name: $name, organizationId: $organizationId)';
}


}

/// @nodoc
abstract mixin class $JobRoleModelCopyWith<$Res>  {
  factory $JobRoleModelCopyWith(JobRoleModel value, $Res Function(JobRoleModel) _then) = _$JobRoleModelCopyWithImpl;
@useResult
$Res call({
 int id, String name,@JsonKey(name: 'OrganizationId') int? organizationId
});




}
/// @nodoc
class _$JobRoleModelCopyWithImpl<$Res>
    implements $JobRoleModelCopyWith<$Res> {
  _$JobRoleModelCopyWithImpl(this._self, this._then);

  final JobRoleModel _self;
  final $Res Function(JobRoleModel) _then;

/// Create a copy of JobRoleModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? organizationId = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,organizationId: freezed == organizationId ? _self.organizationId : organizationId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _JobRoleModel implements JobRoleModel {
  const _JobRoleModel({required this.id, required this.name, @JsonKey(name: 'OrganizationId') this.organizationId});
  factory _JobRoleModel.fromJson(Map<String, dynamic> json) => _$JobRoleModelFromJson(json);

@override final  int id;
@override final  String name;
@override@JsonKey(name: 'OrganizationId') final  int? organizationId;

/// Create a copy of JobRoleModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$JobRoleModelCopyWith<_JobRoleModel> get copyWith => __$JobRoleModelCopyWithImpl<_JobRoleModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$JobRoleModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _JobRoleModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.organizationId, organizationId) || other.organizationId == organizationId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,organizationId);

@override
String toString() {
  return 'JobRoleModel(id: $id, name: $name, organizationId: $organizationId)';
}


}

/// @nodoc
abstract mixin class _$JobRoleModelCopyWith<$Res> implements $JobRoleModelCopyWith<$Res> {
  factory _$JobRoleModelCopyWith(_JobRoleModel value, $Res Function(_JobRoleModel) _then) = __$JobRoleModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String name,@JsonKey(name: 'OrganizationId') int? organizationId
});




}
/// @nodoc
class __$JobRoleModelCopyWithImpl<$Res>
    implements _$JobRoleModelCopyWith<$Res> {
  __$JobRoleModelCopyWithImpl(this._self, this._then);

  final _JobRoleModel _self;
  final $Res Function(_JobRoleModel) _then;

/// Create a copy of JobRoleModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? organizationId = freezed,}) {
  return _then(_JobRoleModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,organizationId: freezed == organizationId ? _self.organizationId : organizationId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}


/// @nodoc
mixin _$WorkModeModel {

 String get id; String get name;
/// Create a copy of WorkModeModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WorkModeModelCopyWith<WorkModeModel> get copyWith => _$WorkModeModelCopyWithImpl<WorkModeModel>(this as WorkModeModel, _$identity);

  /// Serializes this WorkModeModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WorkModeModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name);

@override
String toString() {
  return 'WorkModeModel(id: $id, name: $name)';
}


}

/// @nodoc
abstract mixin class $WorkModeModelCopyWith<$Res>  {
  factory $WorkModeModelCopyWith(WorkModeModel value, $Res Function(WorkModeModel) _then) = _$WorkModeModelCopyWithImpl;
@useResult
$Res call({
 String id, String name
});




}
/// @nodoc
class _$WorkModeModelCopyWithImpl<$Res>
    implements $WorkModeModelCopyWith<$Res> {
  _$WorkModeModelCopyWithImpl(this._self, this._then);

  final WorkModeModel _self;
  final $Res Function(WorkModeModel) _then;

/// Create a copy of WorkModeModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _WorkModeModel implements WorkModeModel {
  const _WorkModeModel({required this.id, required this.name});
  factory _WorkModeModel.fromJson(Map<String, dynamic> json) => _$WorkModeModelFromJson(json);

@override final  String id;
@override final  String name;

/// Create a copy of WorkModeModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WorkModeModelCopyWith<_WorkModeModel> get copyWith => __$WorkModeModelCopyWithImpl<_WorkModeModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WorkModeModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WorkModeModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name);

@override
String toString() {
  return 'WorkModeModel(id: $id, name: $name)';
}


}

/// @nodoc
abstract mixin class _$WorkModeModelCopyWith<$Res> implements $WorkModeModelCopyWith<$Res> {
  factory _$WorkModeModelCopyWith(_WorkModeModel value, $Res Function(_WorkModeModel) _then) = __$WorkModeModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String name
});




}
/// @nodoc
class __$WorkModeModelCopyWithImpl<$Res>
    implements _$WorkModeModelCopyWith<$Res> {
  __$WorkModeModelCopyWithImpl(this._self, this._then);

  final _WorkModeModel _self;
  final $Res Function(_WorkModeModel) _then;

/// Create a copy of WorkModeModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,}) {
  return _then(_WorkModeModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
