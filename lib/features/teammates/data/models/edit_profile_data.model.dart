import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hrms_tst/shared/models/user.model.dart';

part 'edit_profile_data.model.freezed.dart';
part 'edit_profile_data.model.g.dart';

@freezed
abstract class EditProfileDataModel with _$EditProfileDataModel {
  const factory EditProfileDataModel(
    @JsonKey(includeIfNull: false) final String? about,
    @JsonKey(includeIfNull: false) final String? interests,
    @<PERSON><PERSON><PERSON><PERSON>(includeIfNull: false) final String? hobbies,

    @Json<PERSON>ey(includeIfNull: false) final String? firstName,
    @JsonKey(includeIfNull: false) final String? middleName,
    @JsonKey(includeIfNull: false) final String? lastName,
    @Json<PERSON>ey(includeIfNull: false) final String? personalMobile,
    @JsonKey(includeIfNull: false) final String? personalEmail,
    @Json<PERSON>ey(includeIfNull: false) final Gender? gender,
    @<PERSON><PERSON><PERSON><PERSON>(includeIfNull: false) final DateTime? dob,
    @<PERSON><PERSON><PERSON><PERSON>(includeIfNull: false) final String? bloodGroup,
    @J<PERSON><PERSON><PERSON>(includeIfNull: false) final String? maritalStatus,
    @JsonKey(includeIfNull: false) final String? currentAddr,
    @JsonKey(includeIfNull: false) final String? city,
    @JsonKey(includeIfNull: false) final String? state,
    @JsonKey(includeIfNull: false) final String? country,
    @JsonKey(includeIfNull: false) final bool? isAddressSame,
    @JsonKey(includeIfNull: false) final String? permanentAddr,
    @JsonKey(includeIfNull: false) final String? permanentCity,
    @JsonKey(includeIfNull: false) final String? permanentState,
    @JsonKey(includeIfNull: false) final String? permanentCountry,
  ) = _EditProfileDataModel;

  factory EditProfileDataModel.fromJson(Map<String, dynamic> json) =>
      _$EditProfileDataModelFromJson(json);
}
