// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'edit_profile_data.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_EditProfileDataModel _$EditProfileDataModelFromJson(
  Map<String, dynamic> json,
) => _EditProfileDataModel(
  json['about'] as String?,
  json['interests'] as String?,
  json['hobbies'] as String?,
  json['firstName'] as String?,
  json['middleName'] as String?,
  json['lastName'] as String?,
  json['personalMobile'] as String?,
  json['personalEmail'] as String?,
  $enumDecodeNullable(_$GenderEnumMap, json['gender']),
  json['dob'] == null ? null : DateTime.parse(json['dob'] as String),
  json['bloodGroup'] as String?,
  json['maritalStatus'] as String?,
  json['currentAddr'] as String?,
  json['city'] as String?,
  json['state'] as String?,
  json['country'] as String?,
  json['isAddressSame'] as bool?,
  json['permanentAddr'] as String?,
  json['permanentCity'] as String?,
  json['permanentState'] as String?,
  json['permanentCountry'] as String?,
);

Map<String, dynamic> _$EditProfileDataModelToJson(
  _EditProfileDataModel instance,
) => <String, dynamic>{
  if (instance.about case final value?) 'about': value,
  if (instance.interests case final value?) 'interests': value,
  if (instance.hobbies case final value?) 'hobbies': value,
  if (instance.firstName case final value?) 'firstName': value,
  if (instance.middleName case final value?) 'middleName': value,
  if (instance.lastName case final value?) 'lastName': value,
  if (instance.personalMobile case final value?) 'personalMobile': value,
  if (instance.personalEmail case final value?) 'personalEmail': value,
  if (_$GenderEnumMap[instance.gender] case final value?) 'gender': value,
  if (instance.dob?.toIso8601String() case final value?) 'dob': value,
  if (instance.bloodGroup case final value?) 'bloodGroup': value,
  if (instance.maritalStatus case final value?) 'maritalStatus': value,
  if (instance.currentAddr case final value?) 'currentAddr': value,
  if (instance.city case final value?) 'city': value,
  if (instance.state case final value?) 'state': value,
  if (instance.country case final value?) 'country': value,
  if (instance.isAddressSame case final value?) 'isAddressSame': value,
  if (instance.permanentAddr case final value?) 'permanentAddr': value,
  if (instance.permanentCity case final value?) 'permanentCity': value,
  if (instance.permanentState case final value?) 'permanentState': value,
  if (instance.permanentCountry case final value?) 'permanentCountry': value,
};

const _$GenderEnumMap = {
  Gender.male: 'male',
  Gender.female: 'female',
  Gender.other: 'other',
};
