// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'teammate_detail.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TeammateDetailModel _$TeammateDetailModelFromJson(Map<String, dynamic> json) =>
    _TeammateDetailModel(
      currentStatus: json['currentStatus'] as String,
      user: UserModel.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TeammateDetailModelToJson(
  _TeammateDetailModel instance,
) => <String, dynamic>{
  'currentStatus': instance.currentStatus,
  'user': instance.user.toJson(),
};

_OfferLetterModel _$OfferLetterModelFromJson(Map<String, dynamic> json) =>
    _OfferLetterModel(
      id: (json['id'] as num).toInt(),
      firstName: json['firstName'] as String?,
      middleName: json['middleName'] as String?,
      lastName: json['lastName'] as String?,
      salary: (json['salary'] as num?)?.toInt(),
      workMode: json['workMode'] as String?,
      acceptanceDeadLine: json['acceptanceDeadLine'] == null
          ? null
          : DateTime.parse(json['acceptanceDeadLine'] as String),
      joiningDate: json['joiningDate'] == null
          ? null
          : DateTime.parse(json['joiningDate'] as String),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      organizationId: (json['OrganizationId'] as num?)?.toInt(),
      officeId: (json['OfficeId'] as num?)?.toInt(),
      departmentId: (json['DepartmentId'] as num?)?.toInt(),
      jobRoleId: (json['JobRoleId'] as num?)?.toInt(),
      employeeId: (json['EmployeeId'] as num?)?.toInt(),
      designationId: (json['DesignationId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$OfferLetterModelToJson(_OfferLetterModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'firstName': instance.firstName,
      'middleName': instance.middleName,
      'lastName': instance.lastName,
      'salary': instance.salary,
      'workMode': instance.workMode,
      'acceptanceDeadLine': instance.acceptanceDeadLine?.toIso8601String(),
      'joiningDate': instance.joiningDate?.toIso8601String(),
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'OrganizationId': instance.organizationId,
      'OfficeId': instance.officeId,
      'DepartmentId': instance.departmentId,
      'JobRoleId': instance.jobRoleId,
      'EmployeeId': instance.employeeId,
      'DesignationId': instance.designationId,
    };
