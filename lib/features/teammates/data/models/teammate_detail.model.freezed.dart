// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'teammate_detail.model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TeammateDetailModel {

 String get currentStatus; UserModel get user;
/// Create a copy of TeammateDetailModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TeammateDetailModelCopyWith<TeammateDetailModel> get copyWith => _$TeammateDetailModelCopyWithImpl<TeammateDetailModel>(this as TeammateDetailModel, _$identity);

  /// Serializes this TeammateDetailModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TeammateDetailModel&&(identical(other.currentStatus, currentStatus) || other.currentStatus == currentStatus)&&(identical(other.user, user) || other.user == user));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,currentStatus,user);

@override
String toString() {
  return 'TeammateDetailModel(currentStatus: $currentStatus, user: $user)';
}


}

/// @nodoc
abstract mixin class $TeammateDetailModelCopyWith<$Res>  {
  factory $TeammateDetailModelCopyWith(TeammateDetailModel value, $Res Function(TeammateDetailModel) _then) = _$TeammateDetailModelCopyWithImpl;
@useResult
$Res call({
 String currentStatus, UserModel user
});


$UserModelCopyWith<$Res> get user;

}
/// @nodoc
class _$TeammateDetailModelCopyWithImpl<$Res>
    implements $TeammateDetailModelCopyWith<$Res> {
  _$TeammateDetailModelCopyWithImpl(this._self, this._then);

  final TeammateDetailModel _self;
  final $Res Function(TeammateDetailModel) _then;

/// Create a copy of TeammateDetailModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? currentStatus = null,Object? user = null,}) {
  return _then(_self.copyWith(
currentStatus: null == currentStatus ? _self.currentStatus : currentStatus // ignore: cast_nullable_to_non_nullable
as String,user: null == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as UserModel,
  ));
}
/// Create a copy of TeammateDetailModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserModelCopyWith<$Res> get user {
  
  return $UserModelCopyWith<$Res>(_self.user, (value) {
    return _then(_self.copyWith(user: value));
  });
}
}


/// @nodoc

@JsonSerializable(explicitToJson: true)
class _TeammateDetailModel implements TeammateDetailModel {
  const _TeammateDetailModel({required this.currentStatus, required this.user});
  factory _TeammateDetailModel.fromJson(Map<String, dynamic> json) => _$TeammateDetailModelFromJson(json);

@override final  String currentStatus;
@override final  UserModel user;

/// Create a copy of TeammateDetailModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TeammateDetailModelCopyWith<_TeammateDetailModel> get copyWith => __$TeammateDetailModelCopyWithImpl<_TeammateDetailModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TeammateDetailModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TeammateDetailModel&&(identical(other.currentStatus, currentStatus) || other.currentStatus == currentStatus)&&(identical(other.user, user) || other.user == user));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,currentStatus,user);

@override
String toString() {
  return 'TeammateDetailModel(currentStatus: $currentStatus, user: $user)';
}


}

/// @nodoc
abstract mixin class _$TeammateDetailModelCopyWith<$Res> implements $TeammateDetailModelCopyWith<$Res> {
  factory _$TeammateDetailModelCopyWith(_TeammateDetailModel value, $Res Function(_TeammateDetailModel) _then) = __$TeammateDetailModelCopyWithImpl;
@override @useResult
$Res call({
 String currentStatus, UserModel user
});


@override $UserModelCopyWith<$Res> get user;

}
/// @nodoc
class __$TeammateDetailModelCopyWithImpl<$Res>
    implements _$TeammateDetailModelCopyWith<$Res> {
  __$TeammateDetailModelCopyWithImpl(this._self, this._then);

  final _TeammateDetailModel _self;
  final $Res Function(_TeammateDetailModel) _then;

/// Create a copy of TeammateDetailModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? currentStatus = null,Object? user = null,}) {
  return _then(_TeammateDetailModel(
currentStatus: null == currentStatus ? _self.currentStatus : currentStatus // ignore: cast_nullable_to_non_nullable
as String,user: null == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as UserModel,
  ));
}

/// Create a copy of TeammateDetailModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserModelCopyWith<$Res> get user {
  
  return $UserModelCopyWith<$Res>(_self.user, (value) {
    return _then(_self.copyWith(user: value));
  });
}
}


/// @nodoc
mixin _$OfferLetterModel {

 int get id; String? get firstName; String? get middleName; String? get lastName; int? get salary; String? get workMode; DateTime? get acceptanceDeadLine; DateTime? get joiningDate; DateTime? get createdAt; DateTime? get updatedAt; DateTime? get deletedAt;@JsonKey(name: 'OrganizationId') int? get organizationId;@JsonKey(name: 'OfficeId') int? get officeId;@JsonKey(name: 'DepartmentId') int? get departmentId;@JsonKey(name: 'JobRoleId') int? get jobRoleId;@JsonKey(name: 'EmployeeId') int? get employeeId;@JsonKey(name: 'DesignationId') int? get designationId;
/// Create a copy of OfferLetterModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OfferLetterModelCopyWith<OfferLetterModel> get copyWith => _$OfferLetterModelCopyWithImpl<OfferLetterModel>(this as OfferLetterModel, _$identity);

  /// Serializes this OfferLetterModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OfferLetterModel&&(identical(other.id, id) || other.id == id)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.middleName, middleName) || other.middleName == middleName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.salary, salary) || other.salary == salary)&&(identical(other.workMode, workMode) || other.workMode == workMode)&&(identical(other.acceptanceDeadLine, acceptanceDeadLine) || other.acceptanceDeadLine == acceptanceDeadLine)&&(identical(other.joiningDate, joiningDate) || other.joiningDate == joiningDate)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt)&&(identical(other.organizationId, organizationId) || other.organizationId == organizationId)&&(identical(other.officeId, officeId) || other.officeId == officeId)&&(identical(other.departmentId, departmentId) || other.departmentId == departmentId)&&(identical(other.jobRoleId, jobRoleId) || other.jobRoleId == jobRoleId)&&(identical(other.employeeId, employeeId) || other.employeeId == employeeId)&&(identical(other.designationId, designationId) || other.designationId == designationId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,firstName,middleName,lastName,salary,workMode,acceptanceDeadLine,joiningDate,createdAt,updatedAt,deletedAt,organizationId,officeId,departmentId,jobRoleId,employeeId,designationId);

@override
String toString() {
  return 'OfferLetterModel(id: $id, firstName: $firstName, middleName: $middleName, lastName: $lastName, salary: $salary, workMode: $workMode, acceptanceDeadLine: $acceptanceDeadLine, joiningDate: $joiningDate, createdAt: $createdAt, updatedAt: $updatedAt, deletedAt: $deletedAt, organizationId: $organizationId, officeId: $officeId, departmentId: $departmentId, jobRoleId: $jobRoleId, employeeId: $employeeId, designationId: $designationId)';
}


}

/// @nodoc
abstract mixin class $OfferLetterModelCopyWith<$Res>  {
  factory $OfferLetterModelCopyWith(OfferLetterModel value, $Res Function(OfferLetterModel) _then) = _$OfferLetterModelCopyWithImpl;
@useResult
$Res call({
 int id, String? firstName, String? middleName, String? lastName, int? salary, String? workMode, DateTime? acceptanceDeadLine, DateTime? joiningDate, DateTime? createdAt, DateTime? updatedAt, DateTime? deletedAt,@JsonKey(name: 'OrganizationId') int? organizationId,@JsonKey(name: 'OfficeId') int? officeId,@JsonKey(name: 'DepartmentId') int? departmentId,@JsonKey(name: 'JobRoleId') int? jobRoleId,@JsonKey(name: 'EmployeeId') int? employeeId,@JsonKey(name: 'DesignationId') int? designationId
});




}
/// @nodoc
class _$OfferLetterModelCopyWithImpl<$Res>
    implements $OfferLetterModelCopyWith<$Res> {
  _$OfferLetterModelCopyWithImpl(this._self, this._then);

  final OfferLetterModel _self;
  final $Res Function(OfferLetterModel) _then;

/// Create a copy of OfferLetterModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? firstName = freezed,Object? middleName = freezed,Object? lastName = freezed,Object? salary = freezed,Object? workMode = freezed,Object? acceptanceDeadLine = freezed,Object? joiningDate = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? deletedAt = freezed,Object? organizationId = freezed,Object? officeId = freezed,Object? departmentId = freezed,Object? jobRoleId = freezed,Object? employeeId = freezed,Object? designationId = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,firstName: freezed == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String?,middleName: freezed == middleName ? _self.middleName : middleName // ignore: cast_nullable_to_non_nullable
as String?,lastName: freezed == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String?,salary: freezed == salary ? _self.salary : salary // ignore: cast_nullable_to_non_nullable
as int?,workMode: freezed == workMode ? _self.workMode : workMode // ignore: cast_nullable_to_non_nullable
as String?,acceptanceDeadLine: freezed == acceptanceDeadLine ? _self.acceptanceDeadLine : acceptanceDeadLine // ignore: cast_nullable_to_non_nullable
as DateTime?,joiningDate: freezed == joiningDate ? _self.joiningDate : joiningDate // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,organizationId: freezed == organizationId ? _self.organizationId : organizationId // ignore: cast_nullable_to_non_nullable
as int?,officeId: freezed == officeId ? _self.officeId : officeId // ignore: cast_nullable_to_non_nullable
as int?,departmentId: freezed == departmentId ? _self.departmentId : departmentId // ignore: cast_nullable_to_non_nullable
as int?,jobRoleId: freezed == jobRoleId ? _self.jobRoleId : jobRoleId // ignore: cast_nullable_to_non_nullable
as int?,employeeId: freezed == employeeId ? _self.employeeId : employeeId // ignore: cast_nullable_to_non_nullable
as int?,designationId: freezed == designationId ? _self.designationId : designationId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _OfferLetterModel implements OfferLetterModel {
  const _OfferLetterModel({required this.id, this.firstName, this.middleName, this.lastName, this.salary, this.workMode, this.acceptanceDeadLine, this.joiningDate, this.createdAt, this.updatedAt, this.deletedAt, @JsonKey(name: 'OrganizationId') this.organizationId, @JsonKey(name: 'OfficeId') this.officeId, @JsonKey(name: 'DepartmentId') this.departmentId, @JsonKey(name: 'JobRoleId') this.jobRoleId, @JsonKey(name: 'EmployeeId') this.employeeId, @JsonKey(name: 'DesignationId') this.designationId});
  factory _OfferLetterModel.fromJson(Map<String, dynamic> json) => _$OfferLetterModelFromJson(json);

@override final  int id;
@override final  String? firstName;
@override final  String? middleName;
@override final  String? lastName;
@override final  int? salary;
@override final  String? workMode;
@override final  DateTime? acceptanceDeadLine;
@override final  DateTime? joiningDate;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;
@override final  DateTime? deletedAt;
@override@JsonKey(name: 'OrganizationId') final  int? organizationId;
@override@JsonKey(name: 'OfficeId') final  int? officeId;
@override@JsonKey(name: 'DepartmentId') final  int? departmentId;
@override@JsonKey(name: 'JobRoleId') final  int? jobRoleId;
@override@JsonKey(name: 'EmployeeId') final  int? employeeId;
@override@JsonKey(name: 'DesignationId') final  int? designationId;

/// Create a copy of OfferLetterModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OfferLetterModelCopyWith<_OfferLetterModel> get copyWith => __$OfferLetterModelCopyWithImpl<_OfferLetterModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$OfferLetterModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OfferLetterModel&&(identical(other.id, id) || other.id == id)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.middleName, middleName) || other.middleName == middleName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.salary, salary) || other.salary == salary)&&(identical(other.workMode, workMode) || other.workMode == workMode)&&(identical(other.acceptanceDeadLine, acceptanceDeadLine) || other.acceptanceDeadLine == acceptanceDeadLine)&&(identical(other.joiningDate, joiningDate) || other.joiningDate == joiningDate)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt)&&(identical(other.organizationId, organizationId) || other.organizationId == organizationId)&&(identical(other.officeId, officeId) || other.officeId == officeId)&&(identical(other.departmentId, departmentId) || other.departmentId == departmentId)&&(identical(other.jobRoleId, jobRoleId) || other.jobRoleId == jobRoleId)&&(identical(other.employeeId, employeeId) || other.employeeId == employeeId)&&(identical(other.designationId, designationId) || other.designationId == designationId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,firstName,middleName,lastName,salary,workMode,acceptanceDeadLine,joiningDate,createdAt,updatedAt,deletedAt,organizationId,officeId,departmentId,jobRoleId,employeeId,designationId);

@override
String toString() {
  return 'OfferLetterModel(id: $id, firstName: $firstName, middleName: $middleName, lastName: $lastName, salary: $salary, workMode: $workMode, acceptanceDeadLine: $acceptanceDeadLine, joiningDate: $joiningDate, createdAt: $createdAt, updatedAt: $updatedAt, deletedAt: $deletedAt, organizationId: $organizationId, officeId: $officeId, departmentId: $departmentId, jobRoleId: $jobRoleId, employeeId: $employeeId, designationId: $designationId)';
}


}

/// @nodoc
abstract mixin class _$OfferLetterModelCopyWith<$Res> implements $OfferLetterModelCopyWith<$Res> {
  factory _$OfferLetterModelCopyWith(_OfferLetterModel value, $Res Function(_OfferLetterModel) _then) = __$OfferLetterModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String? firstName, String? middleName, String? lastName, int? salary, String? workMode, DateTime? acceptanceDeadLine, DateTime? joiningDate, DateTime? createdAt, DateTime? updatedAt, DateTime? deletedAt,@JsonKey(name: 'OrganizationId') int? organizationId,@JsonKey(name: 'OfficeId') int? officeId,@JsonKey(name: 'DepartmentId') int? departmentId,@JsonKey(name: 'JobRoleId') int? jobRoleId,@JsonKey(name: 'EmployeeId') int? employeeId,@JsonKey(name: 'DesignationId') int? designationId
});




}
/// @nodoc
class __$OfferLetterModelCopyWithImpl<$Res>
    implements _$OfferLetterModelCopyWith<$Res> {
  __$OfferLetterModelCopyWithImpl(this._self, this._then);

  final _OfferLetterModel _self;
  final $Res Function(_OfferLetterModel) _then;

/// Create a copy of OfferLetterModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? firstName = freezed,Object? middleName = freezed,Object? lastName = freezed,Object? salary = freezed,Object? workMode = freezed,Object? acceptanceDeadLine = freezed,Object? joiningDate = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? deletedAt = freezed,Object? organizationId = freezed,Object? officeId = freezed,Object? departmentId = freezed,Object? jobRoleId = freezed,Object? employeeId = freezed,Object? designationId = freezed,}) {
  return _then(_OfferLetterModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,firstName: freezed == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String?,middleName: freezed == middleName ? _self.middleName : middleName // ignore: cast_nullable_to_non_nullable
as String?,lastName: freezed == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String?,salary: freezed == salary ? _self.salary : salary // ignore: cast_nullable_to_non_nullable
as int?,workMode: freezed == workMode ? _self.workMode : workMode // ignore: cast_nullable_to_non_nullable
as String?,acceptanceDeadLine: freezed == acceptanceDeadLine ? _self.acceptanceDeadLine : acceptanceDeadLine // ignore: cast_nullable_to_non_nullable
as DateTime?,joiningDate: freezed == joiningDate ? _self.joiningDate : joiningDate // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,organizationId: freezed == organizationId ? _self.organizationId : organizationId // ignore: cast_nullable_to_non_nullable
as int?,officeId: freezed == officeId ? _self.officeId : officeId // ignore: cast_nullable_to_non_nullable
as int?,departmentId: freezed == departmentId ? _self.departmentId : departmentId // ignore: cast_nullable_to_non_nullable
as int?,jobRoleId: freezed == jobRoleId ? _self.jobRoleId : jobRoleId // ignore: cast_nullable_to_non_nullable
as int?,employeeId: freezed == employeeId ? _self.employeeId : employeeId // ignore: cast_nullable_to_non_nullable
as int?,designationId: freezed == designationId ? _self.designationId : designationId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on
