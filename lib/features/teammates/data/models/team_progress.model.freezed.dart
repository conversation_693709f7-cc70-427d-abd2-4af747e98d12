// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'team_progress.model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$EmployeeData {

 int get id; String get displayName; String? get image;
/// Create a copy of EmployeeData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EmployeeDataCopyWith<EmployeeData> get copyWith => _$EmployeeDataCopyWithImpl<EmployeeData>(this as EmployeeData, _$identity);

  /// Serializes this EmployeeData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EmployeeData&&(identical(other.id, id) || other.id == id)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.image, image) || other.image == image));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,displayName,image);

@override
String toString() {
  return 'EmployeeData(id: $id, displayName: $displayName, image: $image)';
}


}

/// @nodoc
abstract mixin class $EmployeeDataCopyWith<$Res>  {
  factory $EmployeeDataCopyWith(EmployeeData value, $Res Function(EmployeeData) _then) = _$EmployeeDataCopyWithImpl;
@useResult
$Res call({
 int id, String displayName, String? image
});




}
/// @nodoc
class _$EmployeeDataCopyWithImpl<$Res>
    implements $EmployeeDataCopyWith<$Res> {
  _$EmployeeDataCopyWithImpl(this._self, this._then);

  final EmployeeData _self;
  final $Res Function(EmployeeData) _then;

/// Create a copy of EmployeeData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? displayName = null,Object? image = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,displayName: null == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String,image: freezed == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _EmployeeData implements EmployeeData {
  const _EmployeeData({required this.id, required this.displayName, required this.image});
  factory _EmployeeData.fromJson(Map<String, dynamic> json) => _$EmployeeDataFromJson(json);

@override final  int id;
@override final  String displayName;
@override final  String? image;

/// Create a copy of EmployeeData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EmployeeDataCopyWith<_EmployeeData> get copyWith => __$EmployeeDataCopyWithImpl<_EmployeeData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EmployeeDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EmployeeData&&(identical(other.id, id) || other.id == id)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.image, image) || other.image == image));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,displayName,image);

@override
String toString() {
  return 'EmployeeData(id: $id, displayName: $displayName, image: $image)';
}


}

/// @nodoc
abstract mixin class _$EmployeeDataCopyWith<$Res> implements $EmployeeDataCopyWith<$Res> {
  factory _$EmployeeDataCopyWith(_EmployeeData value, $Res Function(_EmployeeData) _then) = __$EmployeeDataCopyWithImpl;
@override @useResult
$Res call({
 int id, String displayName, String? image
});




}
/// @nodoc
class __$EmployeeDataCopyWithImpl<$Res>
    implements _$EmployeeDataCopyWith<$Res> {
  __$EmployeeDataCopyWithImpl(this._self, this._then);

  final _EmployeeData _self;
  final $Res Function(_EmployeeData) _then;

/// Create a copy of EmployeeData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? displayName = null,Object? image = freezed,}) {
  return _then(_EmployeeData(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,displayName: null == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String,image: freezed == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$BookProgressModel {

 int get id; String? get name; DateTime? get completedAt; String? get notes; String? get docLink; int? get pages; String? get status; DateTime? get createdAt; DateTime? get updatedAt;@JsonKey(name: 'EmployeeId') int? get employeeId;@JsonKey(name: 'VerifierEmpId') int? get verifierEmpId;@JsonKey(fromJson: employeeDataFromJson) EmployeeData? get employee;
/// Create a copy of BookProgressModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BookProgressModelCopyWith<BookProgressModel> get copyWith => _$BookProgressModelCopyWithImpl<BookProgressModel>(this as BookProgressModel, _$identity);

  /// Serializes this BookProgressModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BookProgressModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.docLink, docLink) || other.docLink == docLink)&&(identical(other.pages, pages) || other.pages == pages)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.employeeId, employeeId) || other.employeeId == employeeId)&&(identical(other.verifierEmpId, verifierEmpId) || other.verifierEmpId == verifierEmpId)&&(identical(other.employee, employee) || other.employee == employee));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,completedAt,notes,docLink,pages,status,createdAt,updatedAt,employeeId,verifierEmpId,employee);

@override
String toString() {
  return 'BookProgressModel(id: $id, name: $name, completedAt: $completedAt, notes: $notes, docLink: $docLink, pages: $pages, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, employeeId: $employeeId, verifierEmpId: $verifierEmpId, employee: $employee)';
}


}

/// @nodoc
abstract mixin class $BookProgressModelCopyWith<$Res>  {
  factory $BookProgressModelCopyWith(BookProgressModel value, $Res Function(BookProgressModel) _then) = _$BookProgressModelCopyWithImpl;
@useResult
$Res call({
 int id, String? name, DateTime? completedAt, String? notes, String? docLink, int? pages, String? status, DateTime? createdAt, DateTime? updatedAt,@JsonKey(name: 'EmployeeId') int? employeeId,@JsonKey(name: 'VerifierEmpId') int? verifierEmpId,@JsonKey(fromJson: employeeDataFromJson) EmployeeData? employee
});


$EmployeeDataCopyWith<$Res>? get employee;

}
/// @nodoc
class _$BookProgressModelCopyWithImpl<$Res>
    implements $BookProgressModelCopyWith<$Res> {
  _$BookProgressModelCopyWithImpl(this._self, this._then);

  final BookProgressModel _self;
  final $Res Function(BookProgressModel) _then;

/// Create a copy of BookProgressModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = freezed,Object? completedAt = freezed,Object? notes = freezed,Object? docLink = freezed,Object? pages = freezed,Object? status = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? employeeId = freezed,Object? verifierEmpId = freezed,Object? employee = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,docLink: freezed == docLink ? _self.docLink : docLink // ignore: cast_nullable_to_non_nullable
as String?,pages: freezed == pages ? _self.pages : pages // ignore: cast_nullable_to_non_nullable
as int?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,employeeId: freezed == employeeId ? _self.employeeId : employeeId // ignore: cast_nullable_to_non_nullable
as int?,verifierEmpId: freezed == verifierEmpId ? _self.verifierEmpId : verifierEmpId // ignore: cast_nullable_to_non_nullable
as int?,employee: freezed == employee ? _self.employee : employee // ignore: cast_nullable_to_non_nullable
as EmployeeData?,
  ));
}
/// Create a copy of BookProgressModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EmployeeDataCopyWith<$Res>? get employee {
    if (_self.employee == null) {
    return null;
  }

  return $EmployeeDataCopyWith<$Res>(_self.employee!, (value) {
    return _then(_self.copyWith(employee: value));
  });
}
}


/// @nodoc

@JsonSerializable(explicitToJson: true)
class _BookProgressModel implements BookProgressModel {
  const _BookProgressModel({required this.id, this.name, this.completedAt, this.notes, this.docLink, this.pages, this.status, this.createdAt, this.updatedAt, @JsonKey(name: 'EmployeeId') this.employeeId, @JsonKey(name: 'VerifierEmpId') this.verifierEmpId, @JsonKey(fromJson: employeeDataFromJson) this.employee});
  factory _BookProgressModel.fromJson(Map<String, dynamic> json) => _$BookProgressModelFromJson(json);

@override final  int id;
@override final  String? name;
@override final  DateTime? completedAt;
@override final  String? notes;
@override final  String? docLink;
@override final  int? pages;
@override final  String? status;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;
@override@JsonKey(name: 'EmployeeId') final  int? employeeId;
@override@JsonKey(name: 'VerifierEmpId') final  int? verifierEmpId;
@override@JsonKey(fromJson: employeeDataFromJson) final  EmployeeData? employee;

/// Create a copy of BookProgressModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BookProgressModelCopyWith<_BookProgressModel> get copyWith => __$BookProgressModelCopyWithImpl<_BookProgressModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BookProgressModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BookProgressModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.docLink, docLink) || other.docLink == docLink)&&(identical(other.pages, pages) || other.pages == pages)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.employeeId, employeeId) || other.employeeId == employeeId)&&(identical(other.verifierEmpId, verifierEmpId) || other.verifierEmpId == verifierEmpId)&&(identical(other.employee, employee) || other.employee == employee));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,completedAt,notes,docLink,pages,status,createdAt,updatedAt,employeeId,verifierEmpId,employee);

@override
String toString() {
  return 'BookProgressModel(id: $id, name: $name, completedAt: $completedAt, notes: $notes, docLink: $docLink, pages: $pages, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, employeeId: $employeeId, verifierEmpId: $verifierEmpId, employee: $employee)';
}


}

/// @nodoc
abstract mixin class _$BookProgressModelCopyWith<$Res> implements $BookProgressModelCopyWith<$Res> {
  factory _$BookProgressModelCopyWith(_BookProgressModel value, $Res Function(_BookProgressModel) _then) = __$BookProgressModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String? name, DateTime? completedAt, String? notes, String? docLink, int? pages, String? status, DateTime? createdAt, DateTime? updatedAt,@JsonKey(name: 'EmployeeId') int? employeeId,@JsonKey(name: 'VerifierEmpId') int? verifierEmpId,@JsonKey(fromJson: employeeDataFromJson) EmployeeData? employee
});


@override $EmployeeDataCopyWith<$Res>? get employee;

}
/// @nodoc
class __$BookProgressModelCopyWithImpl<$Res>
    implements _$BookProgressModelCopyWith<$Res> {
  __$BookProgressModelCopyWithImpl(this._self, this._then);

  final _BookProgressModel _self;
  final $Res Function(_BookProgressModel) _then;

/// Create a copy of BookProgressModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = freezed,Object? completedAt = freezed,Object? notes = freezed,Object? docLink = freezed,Object? pages = freezed,Object? status = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? employeeId = freezed,Object? verifierEmpId = freezed,Object? employee = freezed,}) {
  return _then(_BookProgressModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,docLink: freezed == docLink ? _self.docLink : docLink // ignore: cast_nullable_to_non_nullable
as String?,pages: freezed == pages ? _self.pages : pages // ignore: cast_nullable_to_non_nullable
as int?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,employeeId: freezed == employeeId ? _self.employeeId : employeeId // ignore: cast_nullable_to_non_nullable
as int?,verifierEmpId: freezed == verifierEmpId ? _self.verifierEmpId : verifierEmpId // ignore: cast_nullable_to_non_nullable
as int?,employee: freezed == employee ? _self.employee : employee // ignore: cast_nullable_to_non_nullable
as EmployeeData?,
  ));
}

/// Create a copy of BookProgressModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EmployeeDataCopyWith<$Res>? get employee {
    if (_self.employee == null) {
    return null;
  }

  return $EmployeeDataCopyWith<$Res>(_self.employee!, (value) {
    return _then(_self.copyWith(employee: value));
  });
}
}


/// @nodoc
mixin _$CourseProgressModel {

 int get id; String? get name; DateTime? get completedAt; String? get courseLink; String? get notes; String? get docLink; String? get status; DateTime? get createdAt; DateTime? get updatedAt;@JsonKey(name: 'EmployeeId') int? get employeeId;@JsonKey(name: 'VerifierEmpId') int? get verifierEmpId;@JsonKey(fromJson: employeeDataFromJson) EmployeeData? get employee;
/// Create a copy of CourseProgressModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CourseProgressModelCopyWith<CourseProgressModel> get copyWith => _$CourseProgressModelCopyWithImpl<CourseProgressModel>(this as CourseProgressModel, _$identity);

  /// Serializes this CourseProgressModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CourseProgressModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.courseLink, courseLink) || other.courseLink == courseLink)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.docLink, docLink) || other.docLink == docLink)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.employeeId, employeeId) || other.employeeId == employeeId)&&(identical(other.verifierEmpId, verifierEmpId) || other.verifierEmpId == verifierEmpId)&&(identical(other.employee, employee) || other.employee == employee));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,completedAt,courseLink,notes,docLink,status,createdAt,updatedAt,employeeId,verifierEmpId,employee);

@override
String toString() {
  return 'CourseProgressModel(id: $id, name: $name, completedAt: $completedAt, courseLink: $courseLink, notes: $notes, docLink: $docLink, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, employeeId: $employeeId, verifierEmpId: $verifierEmpId, employee: $employee)';
}


}

/// @nodoc
abstract mixin class $CourseProgressModelCopyWith<$Res>  {
  factory $CourseProgressModelCopyWith(CourseProgressModel value, $Res Function(CourseProgressModel) _then) = _$CourseProgressModelCopyWithImpl;
@useResult
$Res call({
 int id, String? name, DateTime? completedAt, String? courseLink, String? notes, String? docLink, String? status, DateTime? createdAt, DateTime? updatedAt,@JsonKey(name: 'EmployeeId') int? employeeId,@JsonKey(name: 'VerifierEmpId') int? verifierEmpId,@JsonKey(fromJson: employeeDataFromJson) EmployeeData? employee
});


$EmployeeDataCopyWith<$Res>? get employee;

}
/// @nodoc
class _$CourseProgressModelCopyWithImpl<$Res>
    implements $CourseProgressModelCopyWith<$Res> {
  _$CourseProgressModelCopyWithImpl(this._self, this._then);

  final CourseProgressModel _self;
  final $Res Function(CourseProgressModel) _then;

/// Create a copy of CourseProgressModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = freezed,Object? completedAt = freezed,Object? courseLink = freezed,Object? notes = freezed,Object? docLink = freezed,Object? status = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? employeeId = freezed,Object? verifierEmpId = freezed,Object? employee = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,courseLink: freezed == courseLink ? _self.courseLink : courseLink // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,docLink: freezed == docLink ? _self.docLink : docLink // ignore: cast_nullable_to_non_nullable
as String?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,employeeId: freezed == employeeId ? _self.employeeId : employeeId // ignore: cast_nullable_to_non_nullable
as int?,verifierEmpId: freezed == verifierEmpId ? _self.verifierEmpId : verifierEmpId // ignore: cast_nullable_to_non_nullable
as int?,employee: freezed == employee ? _self.employee : employee // ignore: cast_nullable_to_non_nullable
as EmployeeData?,
  ));
}
/// Create a copy of CourseProgressModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EmployeeDataCopyWith<$Res>? get employee {
    if (_self.employee == null) {
    return null;
  }

  return $EmployeeDataCopyWith<$Res>(_self.employee!, (value) {
    return _then(_self.copyWith(employee: value));
  });
}
}


/// @nodoc

@JsonSerializable(explicitToJson: true)
class _CourseProgressModel implements CourseProgressModel {
  const _CourseProgressModel({required this.id, this.name, this.completedAt, this.courseLink, this.notes, this.docLink, this.status, this.createdAt, this.updatedAt, @JsonKey(name: 'EmployeeId') this.employeeId, @JsonKey(name: 'VerifierEmpId') this.verifierEmpId, @JsonKey(fromJson: employeeDataFromJson) this.employee});
  factory _CourseProgressModel.fromJson(Map<String, dynamic> json) => _$CourseProgressModelFromJson(json);

@override final  int id;
@override final  String? name;
@override final  DateTime? completedAt;
@override final  String? courseLink;
@override final  String? notes;
@override final  String? docLink;
@override final  String? status;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;
@override@JsonKey(name: 'EmployeeId') final  int? employeeId;
@override@JsonKey(name: 'VerifierEmpId') final  int? verifierEmpId;
@override@JsonKey(fromJson: employeeDataFromJson) final  EmployeeData? employee;

/// Create a copy of CourseProgressModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CourseProgressModelCopyWith<_CourseProgressModel> get copyWith => __$CourseProgressModelCopyWithImpl<_CourseProgressModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CourseProgressModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CourseProgressModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.courseLink, courseLink) || other.courseLink == courseLink)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.docLink, docLink) || other.docLink == docLink)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.employeeId, employeeId) || other.employeeId == employeeId)&&(identical(other.verifierEmpId, verifierEmpId) || other.verifierEmpId == verifierEmpId)&&(identical(other.employee, employee) || other.employee == employee));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,completedAt,courseLink,notes,docLink,status,createdAt,updatedAt,employeeId,verifierEmpId,employee);

@override
String toString() {
  return 'CourseProgressModel(id: $id, name: $name, completedAt: $completedAt, courseLink: $courseLink, notes: $notes, docLink: $docLink, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, employeeId: $employeeId, verifierEmpId: $verifierEmpId, employee: $employee)';
}


}

/// @nodoc
abstract mixin class _$CourseProgressModelCopyWith<$Res> implements $CourseProgressModelCopyWith<$Res> {
  factory _$CourseProgressModelCopyWith(_CourseProgressModel value, $Res Function(_CourseProgressModel) _then) = __$CourseProgressModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String? name, DateTime? completedAt, String? courseLink, String? notes, String? docLink, String? status, DateTime? createdAt, DateTime? updatedAt,@JsonKey(name: 'EmployeeId') int? employeeId,@JsonKey(name: 'VerifierEmpId') int? verifierEmpId,@JsonKey(fromJson: employeeDataFromJson) EmployeeData? employee
});


@override $EmployeeDataCopyWith<$Res>? get employee;

}
/// @nodoc
class __$CourseProgressModelCopyWithImpl<$Res>
    implements _$CourseProgressModelCopyWith<$Res> {
  __$CourseProgressModelCopyWithImpl(this._self, this._then);

  final _CourseProgressModel _self;
  final $Res Function(_CourseProgressModel) _then;

/// Create a copy of CourseProgressModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = freezed,Object? completedAt = freezed,Object? courseLink = freezed,Object? notes = freezed,Object? docLink = freezed,Object? status = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? employeeId = freezed,Object? verifierEmpId = freezed,Object? employee = freezed,}) {
  return _then(_CourseProgressModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,courseLink: freezed == courseLink ? _self.courseLink : courseLink // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,docLink: freezed == docLink ? _self.docLink : docLink // ignore: cast_nullable_to_non_nullable
as String?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,employeeId: freezed == employeeId ? _self.employeeId : employeeId // ignore: cast_nullable_to_non_nullable
as int?,verifierEmpId: freezed == verifierEmpId ? _self.verifierEmpId : verifierEmpId // ignore: cast_nullable_to_non_nullable
as int?,employee: freezed == employee ? _self.employee : employee // ignore: cast_nullable_to_non_nullable
as EmployeeData?,
  ));
}

/// Create a copy of CourseProgressModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EmployeeDataCopyWith<$Res>? get employee {
    if (_self.employee == null) {
    return null;
  }

  return $EmployeeDataCopyWith<$Res>(_self.employee!, (value) {
    return _then(_self.copyWith(employee: value));
  });
}
}


/// @nodoc
mixin _$GDProgressModel {

 int get id; String? get name; String? get topic; DateTime? get completedAt; String? get notes; String? get docLink; String? get status; DateTime? get createdAt; DateTime? get updatedAt;@JsonKey(name: 'EmployeeId') int? get employeeId;@JsonKey(name: 'VerifierEmpId') int? get verifierEmpId;@JsonKey(fromJson: employeeDataFromJson) EmployeeData? get employee;
/// Create a copy of GDProgressModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GDProgressModelCopyWith<GDProgressModel> get copyWith => _$GDProgressModelCopyWithImpl<GDProgressModel>(this as GDProgressModel, _$identity);

  /// Serializes this GDProgressModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GDProgressModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.topic, topic) || other.topic == topic)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.docLink, docLink) || other.docLink == docLink)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.employeeId, employeeId) || other.employeeId == employeeId)&&(identical(other.verifierEmpId, verifierEmpId) || other.verifierEmpId == verifierEmpId)&&(identical(other.employee, employee) || other.employee == employee));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,topic,completedAt,notes,docLink,status,createdAt,updatedAt,employeeId,verifierEmpId,employee);

@override
String toString() {
  return 'GDProgressModel(id: $id, name: $name, topic: $topic, completedAt: $completedAt, notes: $notes, docLink: $docLink, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, employeeId: $employeeId, verifierEmpId: $verifierEmpId, employee: $employee)';
}


}

/// @nodoc
abstract mixin class $GDProgressModelCopyWith<$Res>  {
  factory $GDProgressModelCopyWith(GDProgressModel value, $Res Function(GDProgressModel) _then) = _$GDProgressModelCopyWithImpl;
@useResult
$Res call({
 int id, String? name, String? topic, DateTime? completedAt, String? notes, String? docLink, String? status, DateTime? createdAt, DateTime? updatedAt,@JsonKey(name: 'EmployeeId') int? employeeId,@JsonKey(name: 'VerifierEmpId') int? verifierEmpId,@JsonKey(fromJson: employeeDataFromJson) EmployeeData? employee
});


$EmployeeDataCopyWith<$Res>? get employee;

}
/// @nodoc
class _$GDProgressModelCopyWithImpl<$Res>
    implements $GDProgressModelCopyWith<$Res> {
  _$GDProgressModelCopyWithImpl(this._self, this._then);

  final GDProgressModel _self;
  final $Res Function(GDProgressModel) _then;

/// Create a copy of GDProgressModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = freezed,Object? topic = freezed,Object? completedAt = freezed,Object? notes = freezed,Object? docLink = freezed,Object? status = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? employeeId = freezed,Object? verifierEmpId = freezed,Object? employee = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,topic: freezed == topic ? _self.topic : topic // ignore: cast_nullable_to_non_nullable
as String?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,docLink: freezed == docLink ? _self.docLink : docLink // ignore: cast_nullable_to_non_nullable
as String?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,employeeId: freezed == employeeId ? _self.employeeId : employeeId // ignore: cast_nullable_to_non_nullable
as int?,verifierEmpId: freezed == verifierEmpId ? _self.verifierEmpId : verifierEmpId // ignore: cast_nullable_to_non_nullable
as int?,employee: freezed == employee ? _self.employee : employee // ignore: cast_nullable_to_non_nullable
as EmployeeData?,
  ));
}
/// Create a copy of GDProgressModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EmployeeDataCopyWith<$Res>? get employee {
    if (_self.employee == null) {
    return null;
  }

  return $EmployeeDataCopyWith<$Res>(_self.employee!, (value) {
    return _then(_self.copyWith(employee: value));
  });
}
}


/// @nodoc

@JsonSerializable(explicitToJson: true)
class _GDProgressModel implements GDProgressModel {
  const _GDProgressModel({required this.id, this.name, this.topic, this.completedAt, this.notes, this.docLink, this.status, this.createdAt, this.updatedAt, @JsonKey(name: 'EmployeeId') this.employeeId, @JsonKey(name: 'VerifierEmpId') this.verifierEmpId, @JsonKey(fromJson: employeeDataFromJson) this.employee});
  factory _GDProgressModel.fromJson(Map<String, dynamic> json) => _$GDProgressModelFromJson(json);

@override final  int id;
@override final  String? name;
@override final  String? topic;
@override final  DateTime? completedAt;
@override final  String? notes;
@override final  String? docLink;
@override final  String? status;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;
@override@JsonKey(name: 'EmployeeId') final  int? employeeId;
@override@JsonKey(name: 'VerifierEmpId') final  int? verifierEmpId;
@override@JsonKey(fromJson: employeeDataFromJson) final  EmployeeData? employee;

/// Create a copy of GDProgressModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GDProgressModelCopyWith<_GDProgressModel> get copyWith => __$GDProgressModelCopyWithImpl<_GDProgressModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GDProgressModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GDProgressModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.topic, topic) || other.topic == topic)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.docLink, docLink) || other.docLink == docLink)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.employeeId, employeeId) || other.employeeId == employeeId)&&(identical(other.verifierEmpId, verifierEmpId) || other.verifierEmpId == verifierEmpId)&&(identical(other.employee, employee) || other.employee == employee));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,topic,completedAt,notes,docLink,status,createdAt,updatedAt,employeeId,verifierEmpId,employee);

@override
String toString() {
  return 'GDProgressModel(id: $id, name: $name, topic: $topic, completedAt: $completedAt, notes: $notes, docLink: $docLink, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, employeeId: $employeeId, verifierEmpId: $verifierEmpId, employee: $employee)';
}


}

/// @nodoc
abstract mixin class _$GDProgressModelCopyWith<$Res> implements $GDProgressModelCopyWith<$Res> {
  factory _$GDProgressModelCopyWith(_GDProgressModel value, $Res Function(_GDProgressModel) _then) = __$GDProgressModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String? name, String? topic, DateTime? completedAt, String? notes, String? docLink, String? status, DateTime? createdAt, DateTime? updatedAt,@JsonKey(name: 'EmployeeId') int? employeeId,@JsonKey(name: 'VerifierEmpId') int? verifierEmpId,@JsonKey(fromJson: employeeDataFromJson) EmployeeData? employee
});


@override $EmployeeDataCopyWith<$Res>? get employee;

}
/// @nodoc
class __$GDProgressModelCopyWithImpl<$Res>
    implements _$GDProgressModelCopyWith<$Res> {
  __$GDProgressModelCopyWithImpl(this._self, this._then);

  final _GDProgressModel _self;
  final $Res Function(_GDProgressModel) _then;

/// Create a copy of GDProgressModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = freezed,Object? topic = freezed,Object? completedAt = freezed,Object? notes = freezed,Object? docLink = freezed,Object? status = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? employeeId = freezed,Object? verifierEmpId = freezed,Object? employee = freezed,}) {
  return _then(_GDProgressModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,topic: freezed == topic ? _self.topic : topic // ignore: cast_nullable_to_non_nullable
as String?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,docLink: freezed == docLink ? _self.docLink : docLink // ignore: cast_nullable_to_non_nullable
as String?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,employeeId: freezed == employeeId ? _self.employeeId : employeeId // ignore: cast_nullable_to_non_nullable
as int?,verifierEmpId: freezed == verifierEmpId ? _self.verifierEmpId : verifierEmpId // ignore: cast_nullable_to_non_nullable
as int?,employee: freezed == employee ? _self.employee : employee // ignore: cast_nullable_to_non_nullable
as EmployeeData?,
  ));
}

/// Create a copy of GDProgressModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EmployeeDataCopyWith<$Res>? get employee {
    if (_self.employee == null) {
    return null;
  }

  return $EmployeeDataCopyWith<$Res>(_self.employee!, (value) {
    return _then(_self.copyWith(employee: value));
  });
}
}


/// @nodoc
mixin _$SessionProgressModel {

 int get id; String? get name; String? get topic; DateTime? get completedAt; String? get notes; String? get docLink; String? get status; DateTime? get createdAt; DateTime? get updatedAt;@JsonKey(name: 'EmployeeId') int? get employeeId;@JsonKey(name: 'VerifierEmpId') int? get verifierEmpId;@JsonKey(fromJson: employeeDataFromJson) EmployeeData? get employee;
/// Create a copy of SessionProgressModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SessionProgressModelCopyWith<SessionProgressModel> get copyWith => _$SessionProgressModelCopyWithImpl<SessionProgressModel>(this as SessionProgressModel, _$identity);

  /// Serializes this SessionProgressModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SessionProgressModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.topic, topic) || other.topic == topic)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.docLink, docLink) || other.docLink == docLink)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.employeeId, employeeId) || other.employeeId == employeeId)&&(identical(other.verifierEmpId, verifierEmpId) || other.verifierEmpId == verifierEmpId)&&(identical(other.employee, employee) || other.employee == employee));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,topic,completedAt,notes,docLink,status,createdAt,updatedAt,employeeId,verifierEmpId,employee);

@override
String toString() {
  return 'SessionProgressModel(id: $id, name: $name, topic: $topic, completedAt: $completedAt, notes: $notes, docLink: $docLink, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, employeeId: $employeeId, verifierEmpId: $verifierEmpId, employee: $employee)';
}


}

/// @nodoc
abstract mixin class $SessionProgressModelCopyWith<$Res>  {
  factory $SessionProgressModelCopyWith(SessionProgressModel value, $Res Function(SessionProgressModel) _then) = _$SessionProgressModelCopyWithImpl;
@useResult
$Res call({
 int id, String? name, String? topic, DateTime? completedAt, String? notes, String? docLink, String? status, DateTime? createdAt, DateTime? updatedAt,@JsonKey(name: 'EmployeeId') int? employeeId,@JsonKey(name: 'VerifierEmpId') int? verifierEmpId,@JsonKey(fromJson: employeeDataFromJson) EmployeeData? employee
});


$EmployeeDataCopyWith<$Res>? get employee;

}
/// @nodoc
class _$SessionProgressModelCopyWithImpl<$Res>
    implements $SessionProgressModelCopyWith<$Res> {
  _$SessionProgressModelCopyWithImpl(this._self, this._then);

  final SessionProgressModel _self;
  final $Res Function(SessionProgressModel) _then;

/// Create a copy of SessionProgressModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = freezed,Object? topic = freezed,Object? completedAt = freezed,Object? notes = freezed,Object? docLink = freezed,Object? status = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? employeeId = freezed,Object? verifierEmpId = freezed,Object? employee = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,topic: freezed == topic ? _self.topic : topic // ignore: cast_nullable_to_non_nullable
as String?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,docLink: freezed == docLink ? _self.docLink : docLink // ignore: cast_nullable_to_non_nullable
as String?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,employeeId: freezed == employeeId ? _self.employeeId : employeeId // ignore: cast_nullable_to_non_nullable
as int?,verifierEmpId: freezed == verifierEmpId ? _self.verifierEmpId : verifierEmpId // ignore: cast_nullable_to_non_nullable
as int?,employee: freezed == employee ? _self.employee : employee // ignore: cast_nullable_to_non_nullable
as EmployeeData?,
  ));
}
/// Create a copy of SessionProgressModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EmployeeDataCopyWith<$Res>? get employee {
    if (_self.employee == null) {
    return null;
  }

  return $EmployeeDataCopyWith<$Res>(_self.employee!, (value) {
    return _then(_self.copyWith(employee: value));
  });
}
}


/// @nodoc

@JsonSerializable(explicitToJson: true)
class _SessionProgressModel implements SessionProgressModel {
  const _SessionProgressModel({required this.id, this.name, this.topic, this.completedAt, this.notes, this.docLink, this.status, this.createdAt, this.updatedAt, @JsonKey(name: 'EmployeeId') this.employeeId, @JsonKey(name: 'VerifierEmpId') this.verifierEmpId, @JsonKey(fromJson: employeeDataFromJson) this.employee});
  factory _SessionProgressModel.fromJson(Map<String, dynamic> json) => _$SessionProgressModelFromJson(json);

@override final  int id;
@override final  String? name;
@override final  String? topic;
@override final  DateTime? completedAt;
@override final  String? notes;
@override final  String? docLink;
@override final  String? status;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;
@override@JsonKey(name: 'EmployeeId') final  int? employeeId;
@override@JsonKey(name: 'VerifierEmpId') final  int? verifierEmpId;
@override@JsonKey(fromJson: employeeDataFromJson) final  EmployeeData? employee;

/// Create a copy of SessionProgressModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SessionProgressModelCopyWith<_SessionProgressModel> get copyWith => __$SessionProgressModelCopyWithImpl<_SessionProgressModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SessionProgressModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SessionProgressModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.topic, topic) || other.topic == topic)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.docLink, docLink) || other.docLink == docLink)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.employeeId, employeeId) || other.employeeId == employeeId)&&(identical(other.verifierEmpId, verifierEmpId) || other.verifierEmpId == verifierEmpId)&&(identical(other.employee, employee) || other.employee == employee));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,topic,completedAt,notes,docLink,status,createdAt,updatedAt,employeeId,verifierEmpId,employee);

@override
String toString() {
  return 'SessionProgressModel(id: $id, name: $name, topic: $topic, completedAt: $completedAt, notes: $notes, docLink: $docLink, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, employeeId: $employeeId, verifierEmpId: $verifierEmpId, employee: $employee)';
}


}

/// @nodoc
abstract mixin class _$SessionProgressModelCopyWith<$Res> implements $SessionProgressModelCopyWith<$Res> {
  factory _$SessionProgressModelCopyWith(_SessionProgressModel value, $Res Function(_SessionProgressModel) _then) = __$SessionProgressModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String? name, String? topic, DateTime? completedAt, String? notes, String? docLink, String? status, DateTime? createdAt, DateTime? updatedAt,@JsonKey(name: 'EmployeeId') int? employeeId,@JsonKey(name: 'VerifierEmpId') int? verifierEmpId,@JsonKey(fromJson: employeeDataFromJson) EmployeeData? employee
});


@override $EmployeeDataCopyWith<$Res>? get employee;

}
/// @nodoc
class __$SessionProgressModelCopyWithImpl<$Res>
    implements _$SessionProgressModelCopyWith<$Res> {
  __$SessionProgressModelCopyWithImpl(this._self, this._then);

  final _SessionProgressModel _self;
  final $Res Function(_SessionProgressModel) _then;

/// Create a copy of SessionProgressModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = freezed,Object? topic = freezed,Object? completedAt = freezed,Object? notes = freezed,Object? docLink = freezed,Object? status = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? employeeId = freezed,Object? verifierEmpId = freezed,Object? employee = freezed,}) {
  return _then(_SessionProgressModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,topic: freezed == topic ? _self.topic : topic // ignore: cast_nullable_to_non_nullable
as String?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,docLink: freezed == docLink ? _self.docLink : docLink // ignore: cast_nullable_to_non_nullable
as String?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,employeeId: freezed == employeeId ? _self.employeeId : employeeId // ignore: cast_nullable_to_non_nullable
as int?,verifierEmpId: freezed == verifierEmpId ? _self.verifierEmpId : verifierEmpId // ignore: cast_nullable_to_non_nullable
as int?,employee: freezed == employee ? _self.employee : employee // ignore: cast_nullable_to_non_nullable
as EmployeeData?,
  ));
}

/// Create a copy of SessionProgressModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EmployeeDataCopyWith<$Res>? get employee {
    if (_self.employee == null) {
    return null;
  }

  return $EmployeeDataCopyWith<$Res>(_self.employee!, (value) {
    return _then(_self.copyWith(employee: value));
  });
}
}


/// @nodoc
mixin _$EventProgressModel {

 int get id; String? get name; String? get topic; DateTime? get completedAt; String? get notes; String? get link; String? get status; List<String>? get photos; DateTime? get startDate; DateTime? get endDate; DateTime? get createdAt; DateTime? get updatedAt;@JsonKey(name: 'EmployeeId') int? get employeeId;@JsonKey(name: 'VerifierEmpId') int? get verifierEmpId;@JsonKey(fromJson: employeeDataFromJson) EmployeeData? get employee;
/// Create a copy of EventProgressModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EventProgressModelCopyWith<EventProgressModel> get copyWith => _$EventProgressModelCopyWithImpl<EventProgressModel>(this as EventProgressModel, _$identity);

  /// Serializes this EventProgressModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EventProgressModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.topic, topic) || other.topic == topic)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.link, link) || other.link == link)&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other.photos, photos)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.employeeId, employeeId) || other.employeeId == employeeId)&&(identical(other.verifierEmpId, verifierEmpId) || other.verifierEmpId == verifierEmpId)&&(identical(other.employee, employee) || other.employee == employee));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,topic,completedAt,notes,link,status,const DeepCollectionEquality().hash(photos),startDate,endDate,createdAt,updatedAt,employeeId,verifierEmpId,employee);

@override
String toString() {
  return 'EventProgressModel(id: $id, name: $name, topic: $topic, completedAt: $completedAt, notes: $notes, link: $link, status: $status, photos: $photos, startDate: $startDate, endDate: $endDate, createdAt: $createdAt, updatedAt: $updatedAt, employeeId: $employeeId, verifierEmpId: $verifierEmpId, employee: $employee)';
}


}

/// @nodoc
abstract mixin class $EventProgressModelCopyWith<$Res>  {
  factory $EventProgressModelCopyWith(EventProgressModel value, $Res Function(EventProgressModel) _then) = _$EventProgressModelCopyWithImpl;
@useResult
$Res call({
 int id, String? name, String? topic, DateTime? completedAt, String? notes, String? link, String? status, List<String>? photos, DateTime? startDate, DateTime? endDate, DateTime? createdAt, DateTime? updatedAt,@JsonKey(name: 'EmployeeId') int? employeeId,@JsonKey(name: 'VerifierEmpId') int? verifierEmpId,@JsonKey(fromJson: employeeDataFromJson) EmployeeData? employee
});


$EmployeeDataCopyWith<$Res>? get employee;

}
/// @nodoc
class _$EventProgressModelCopyWithImpl<$Res>
    implements $EventProgressModelCopyWith<$Res> {
  _$EventProgressModelCopyWithImpl(this._self, this._then);

  final EventProgressModel _self;
  final $Res Function(EventProgressModel) _then;

/// Create a copy of EventProgressModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = freezed,Object? topic = freezed,Object? completedAt = freezed,Object? notes = freezed,Object? link = freezed,Object? status = freezed,Object? photos = freezed,Object? startDate = freezed,Object? endDate = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? employeeId = freezed,Object? verifierEmpId = freezed,Object? employee = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,topic: freezed == topic ? _self.topic : topic // ignore: cast_nullable_to_non_nullable
as String?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,link: freezed == link ? _self.link : link // ignore: cast_nullable_to_non_nullable
as String?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,photos: freezed == photos ? _self.photos : photos // ignore: cast_nullable_to_non_nullable
as List<String>?,startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,employeeId: freezed == employeeId ? _self.employeeId : employeeId // ignore: cast_nullable_to_non_nullable
as int?,verifierEmpId: freezed == verifierEmpId ? _self.verifierEmpId : verifierEmpId // ignore: cast_nullable_to_non_nullable
as int?,employee: freezed == employee ? _self.employee : employee // ignore: cast_nullable_to_non_nullable
as EmployeeData?,
  ));
}
/// Create a copy of EventProgressModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EmployeeDataCopyWith<$Res>? get employee {
    if (_self.employee == null) {
    return null;
  }

  return $EmployeeDataCopyWith<$Res>(_self.employee!, (value) {
    return _then(_self.copyWith(employee: value));
  });
}
}


/// @nodoc

@JsonSerializable(explicitToJson: true)
class _EventProgressModel implements EventProgressModel {
  const _EventProgressModel({required this.id, this.name, this.topic, this.completedAt, this.notes, this.link, this.status, final  List<String>? photos, this.startDate, this.endDate, this.createdAt, this.updatedAt, @JsonKey(name: 'EmployeeId') this.employeeId, @JsonKey(name: 'VerifierEmpId') this.verifierEmpId, @JsonKey(fromJson: employeeDataFromJson) this.employee}): _photos = photos;
  factory _EventProgressModel.fromJson(Map<String, dynamic> json) => _$EventProgressModelFromJson(json);

@override final  int id;
@override final  String? name;
@override final  String? topic;
@override final  DateTime? completedAt;
@override final  String? notes;
@override final  String? link;
@override final  String? status;
 final  List<String>? _photos;
@override List<String>? get photos {
  final value = _photos;
  if (value == null) return null;
  if (_photos is EqualUnmodifiableListView) return _photos;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  DateTime? startDate;
@override final  DateTime? endDate;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;
@override@JsonKey(name: 'EmployeeId') final  int? employeeId;
@override@JsonKey(name: 'VerifierEmpId') final  int? verifierEmpId;
@override@JsonKey(fromJson: employeeDataFromJson) final  EmployeeData? employee;

/// Create a copy of EventProgressModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EventProgressModelCopyWith<_EventProgressModel> get copyWith => __$EventProgressModelCopyWithImpl<_EventProgressModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EventProgressModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EventProgressModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.topic, topic) || other.topic == topic)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.link, link) || other.link == link)&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other._photos, _photos)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.employeeId, employeeId) || other.employeeId == employeeId)&&(identical(other.verifierEmpId, verifierEmpId) || other.verifierEmpId == verifierEmpId)&&(identical(other.employee, employee) || other.employee == employee));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,topic,completedAt,notes,link,status,const DeepCollectionEquality().hash(_photos),startDate,endDate,createdAt,updatedAt,employeeId,verifierEmpId,employee);

@override
String toString() {
  return 'EventProgressModel(id: $id, name: $name, topic: $topic, completedAt: $completedAt, notes: $notes, link: $link, status: $status, photos: $photos, startDate: $startDate, endDate: $endDate, createdAt: $createdAt, updatedAt: $updatedAt, employeeId: $employeeId, verifierEmpId: $verifierEmpId, employee: $employee)';
}


}

/// @nodoc
abstract mixin class _$EventProgressModelCopyWith<$Res> implements $EventProgressModelCopyWith<$Res> {
  factory _$EventProgressModelCopyWith(_EventProgressModel value, $Res Function(_EventProgressModel) _then) = __$EventProgressModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String? name, String? topic, DateTime? completedAt, String? notes, String? link, String? status, List<String>? photos, DateTime? startDate, DateTime? endDate, DateTime? createdAt, DateTime? updatedAt,@JsonKey(name: 'EmployeeId') int? employeeId,@JsonKey(name: 'VerifierEmpId') int? verifierEmpId,@JsonKey(fromJson: employeeDataFromJson) EmployeeData? employee
});


@override $EmployeeDataCopyWith<$Res>? get employee;

}
/// @nodoc
class __$EventProgressModelCopyWithImpl<$Res>
    implements _$EventProgressModelCopyWith<$Res> {
  __$EventProgressModelCopyWithImpl(this._self, this._then);

  final _EventProgressModel _self;
  final $Res Function(_EventProgressModel) _then;

/// Create a copy of EventProgressModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = freezed,Object? topic = freezed,Object? completedAt = freezed,Object? notes = freezed,Object? link = freezed,Object? status = freezed,Object? photos = freezed,Object? startDate = freezed,Object? endDate = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? employeeId = freezed,Object? verifierEmpId = freezed,Object? employee = freezed,}) {
  return _then(_EventProgressModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,topic: freezed == topic ? _self.topic : topic // ignore: cast_nullable_to_non_nullable
as String?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,link: freezed == link ? _self.link : link // ignore: cast_nullable_to_non_nullable
as String?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,photos: freezed == photos ? _self._photos : photos // ignore: cast_nullable_to_non_nullable
as List<String>?,startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,employeeId: freezed == employeeId ? _self.employeeId : employeeId // ignore: cast_nullable_to_non_nullable
as int?,verifierEmpId: freezed == verifierEmpId ? _self.verifierEmpId : verifierEmpId // ignore: cast_nullable_to_non_nullable
as int?,employee: freezed == employee ? _self.employee : employee // ignore: cast_nullable_to_non_nullable
as EmployeeData?,
  ));
}

/// Create a copy of EventProgressModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EmployeeDataCopyWith<$Res>? get employee {
    if (_self.employee == null) {
    return null;
  }

  return $EmployeeDataCopyWith<$Res>(_self.employee!, (value) {
    return _then(_self.copyWith(employee: value));
  });
}
}

// dart format on
