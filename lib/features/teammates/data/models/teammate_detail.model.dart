import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hrms_tst/shared/models/user.model.dart';

part 'teammate_detail.model.freezed.dart';
part 'teammate_detail.model.g.dart';

@freezed
abstract class TeammateDetailModel with _$TeammateDetailModel {
  @JsonSerializable(explicitToJson: true)
  const factory TeammateDetailModel({
    required final String currentStatus,
    required final UserModel user,
  }) = _TeammateDetailModel;

  factory TeammateDetailModel.fromJson(Map<String, dynamic> json) =>
      _$TeammateDetailModelFromJson(json);
}

@freezed
abstract class OfferLetterModel with _$OfferLetterModel {
  const factory OfferLetterModel({
    required final int id,
    final String? firstName,
    final String? middleName,
    final String? lastName,
    final int? salary,
    final String? workMode,
    final DateTime? acceptanceDeadLine,
    final DateTime? joiningDate,
    final DateTime? createdAt,
    final DateTime? updatedAt,
    final DateTime? deletedAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'OrganizationId') final int? organizationId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'OfficeId') final int? officeId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'DepartmentId') final int? departmentId,
    @JsonKey(name: 'JobRoleId') final int? jobRoleId,
    @JsonKey(name: 'EmployeeId') final int? employeeId,
    @JsonKey(name: 'DesignationId') final int? designationId,
  }) = _OfferLetterModel;

  factory OfferLetterModel.fromJson(Map<String, dynamic> json) =>
      _$OfferLetterModelFromJson(json);
}
