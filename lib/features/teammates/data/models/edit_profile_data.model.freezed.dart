// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'edit_profile_data.model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$EditProfileDataModel {

@JsonKey(includeIfNull: false) String? get about;@JsonKey(includeIfNull: false) String? get interests;@JsonKey(includeIfNull: false) String? get hobbies;@JsonKey(includeIfNull: false) String? get firstName;@JsonKey(includeIfNull: false) String? get middleName;@JsonKey(includeIfNull: false) String? get lastName;@JsonKey(includeIfNull: false) String? get personalMobile;@JsonKey(includeIfNull: false) String? get personalEmail;@JsonKey(includeIfNull: false) Gender? get gender;@JsonKey(includeIfNull: false) DateTime? get dob;@JsonKey(includeIfNull: false) String? get bloodGroup;@JsonKey(includeIfNull: false) String? get maritalStatus;@JsonKey(includeIfNull: false) String? get currentAddr;@JsonKey(includeIfNull: false) String? get city;@JsonKey(includeIfNull: false) String? get state;@JsonKey(includeIfNull: false) String? get country;@JsonKey(includeIfNull: false) bool? get isAddressSame;@JsonKey(includeIfNull: false) String? get permanentAddr;@JsonKey(includeIfNull: false) String? get permanentCity;@JsonKey(includeIfNull: false) String? get permanentState;@JsonKey(includeIfNull: false) String? get permanentCountry;
/// Create a copy of EditProfileDataModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EditProfileDataModelCopyWith<EditProfileDataModel> get copyWith => _$EditProfileDataModelCopyWithImpl<EditProfileDataModel>(this as EditProfileDataModel, _$identity);

  /// Serializes this EditProfileDataModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EditProfileDataModel&&(identical(other.about, about) || other.about == about)&&(identical(other.interests, interests) || other.interests == interests)&&(identical(other.hobbies, hobbies) || other.hobbies == hobbies)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.middleName, middleName) || other.middleName == middleName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.personalMobile, personalMobile) || other.personalMobile == personalMobile)&&(identical(other.personalEmail, personalEmail) || other.personalEmail == personalEmail)&&(identical(other.gender, gender) || other.gender == gender)&&(identical(other.dob, dob) || other.dob == dob)&&(identical(other.bloodGroup, bloodGroup) || other.bloodGroup == bloodGroup)&&(identical(other.maritalStatus, maritalStatus) || other.maritalStatus == maritalStatus)&&(identical(other.currentAddr, currentAddr) || other.currentAddr == currentAddr)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&(identical(other.country, country) || other.country == country)&&(identical(other.isAddressSame, isAddressSame) || other.isAddressSame == isAddressSame)&&(identical(other.permanentAddr, permanentAddr) || other.permanentAddr == permanentAddr)&&(identical(other.permanentCity, permanentCity) || other.permanentCity == permanentCity)&&(identical(other.permanentState, permanentState) || other.permanentState == permanentState)&&(identical(other.permanentCountry, permanentCountry) || other.permanentCountry == permanentCountry));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,about,interests,hobbies,firstName,middleName,lastName,personalMobile,personalEmail,gender,dob,bloodGroup,maritalStatus,currentAddr,city,state,country,isAddressSame,permanentAddr,permanentCity,permanentState,permanentCountry]);

@override
String toString() {
  return 'EditProfileDataModel(about: $about, interests: $interests, hobbies: $hobbies, firstName: $firstName, middleName: $middleName, lastName: $lastName, personalMobile: $personalMobile, personalEmail: $personalEmail, gender: $gender, dob: $dob, bloodGroup: $bloodGroup, maritalStatus: $maritalStatus, currentAddr: $currentAddr, city: $city, state: $state, country: $country, isAddressSame: $isAddressSame, permanentAddr: $permanentAddr, permanentCity: $permanentCity, permanentState: $permanentState, permanentCountry: $permanentCountry)';
}


}

/// @nodoc
abstract mixin class $EditProfileDataModelCopyWith<$Res>  {
  factory $EditProfileDataModelCopyWith(EditProfileDataModel value, $Res Function(EditProfileDataModel) _then) = _$EditProfileDataModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(includeIfNull: false) String? about,@JsonKey(includeIfNull: false) String? interests,@JsonKey(includeIfNull: false) String? hobbies,@JsonKey(includeIfNull: false) String? firstName,@JsonKey(includeIfNull: false) String? middleName,@JsonKey(includeIfNull: false) String? lastName,@JsonKey(includeIfNull: false) String? personalMobile,@JsonKey(includeIfNull: false) String? personalEmail,@JsonKey(includeIfNull: false) Gender? gender,@JsonKey(includeIfNull: false) DateTime? dob,@JsonKey(includeIfNull: false) String? bloodGroup,@JsonKey(includeIfNull: false) String? maritalStatus,@JsonKey(includeIfNull: false) String? currentAddr,@JsonKey(includeIfNull: false) String? city,@JsonKey(includeIfNull: false) String? state,@JsonKey(includeIfNull: false) String? country,@JsonKey(includeIfNull: false) bool? isAddressSame,@JsonKey(includeIfNull: false) String? permanentAddr,@JsonKey(includeIfNull: false) String? permanentCity,@JsonKey(includeIfNull: false) String? permanentState,@JsonKey(includeIfNull: false) String? permanentCountry
});




}
/// @nodoc
class _$EditProfileDataModelCopyWithImpl<$Res>
    implements $EditProfileDataModelCopyWith<$Res> {
  _$EditProfileDataModelCopyWithImpl(this._self, this._then);

  final EditProfileDataModel _self;
  final $Res Function(EditProfileDataModel) _then;

/// Create a copy of EditProfileDataModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? about = freezed,Object? interests = freezed,Object? hobbies = freezed,Object? firstName = freezed,Object? middleName = freezed,Object? lastName = freezed,Object? personalMobile = freezed,Object? personalEmail = freezed,Object? gender = freezed,Object? dob = freezed,Object? bloodGroup = freezed,Object? maritalStatus = freezed,Object? currentAddr = freezed,Object? city = freezed,Object? state = freezed,Object? country = freezed,Object? isAddressSame = freezed,Object? permanentAddr = freezed,Object? permanentCity = freezed,Object? permanentState = freezed,Object? permanentCountry = freezed,}) {
  return _then(_self.copyWith(
about: freezed == about ? _self.about : about // ignore: cast_nullable_to_non_nullable
as String?,interests: freezed == interests ? _self.interests : interests // ignore: cast_nullable_to_non_nullable
as String?,hobbies: freezed == hobbies ? _self.hobbies : hobbies // ignore: cast_nullable_to_non_nullable
as String?,firstName: freezed == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String?,middleName: freezed == middleName ? _self.middleName : middleName // ignore: cast_nullable_to_non_nullable
as String?,lastName: freezed == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String?,personalMobile: freezed == personalMobile ? _self.personalMobile : personalMobile // ignore: cast_nullable_to_non_nullable
as String?,personalEmail: freezed == personalEmail ? _self.personalEmail : personalEmail // ignore: cast_nullable_to_non_nullable
as String?,gender: freezed == gender ? _self.gender : gender // ignore: cast_nullable_to_non_nullable
as Gender?,dob: freezed == dob ? _self.dob : dob // ignore: cast_nullable_to_non_nullable
as DateTime?,bloodGroup: freezed == bloodGroup ? _self.bloodGroup : bloodGroup // ignore: cast_nullable_to_non_nullable
as String?,maritalStatus: freezed == maritalStatus ? _self.maritalStatus : maritalStatus // ignore: cast_nullable_to_non_nullable
as String?,currentAddr: freezed == currentAddr ? _self.currentAddr : currentAddr // ignore: cast_nullable_to_non_nullable
as String?,city: freezed == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String?,state: freezed == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String?,country: freezed == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String?,isAddressSame: freezed == isAddressSame ? _self.isAddressSame : isAddressSame // ignore: cast_nullable_to_non_nullable
as bool?,permanentAddr: freezed == permanentAddr ? _self.permanentAddr : permanentAddr // ignore: cast_nullable_to_non_nullable
as String?,permanentCity: freezed == permanentCity ? _self.permanentCity : permanentCity // ignore: cast_nullable_to_non_nullable
as String?,permanentState: freezed == permanentState ? _self.permanentState : permanentState // ignore: cast_nullable_to_non_nullable
as String?,permanentCountry: freezed == permanentCountry ? _self.permanentCountry : permanentCountry // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _EditProfileDataModel implements EditProfileDataModel {
  const _EditProfileDataModel(@JsonKey(includeIfNull: false) this.about, @JsonKey(includeIfNull: false) this.interests, @JsonKey(includeIfNull: false) this.hobbies, @JsonKey(includeIfNull: false) this.firstName, @JsonKey(includeIfNull: false) this.middleName, @JsonKey(includeIfNull: false) this.lastName, @JsonKey(includeIfNull: false) this.personalMobile, @JsonKey(includeIfNull: false) this.personalEmail, @JsonKey(includeIfNull: false) this.gender, @JsonKey(includeIfNull: false) this.dob, @JsonKey(includeIfNull: false) this.bloodGroup, @JsonKey(includeIfNull: false) this.maritalStatus, @JsonKey(includeIfNull: false) this.currentAddr, @JsonKey(includeIfNull: false) this.city, @JsonKey(includeIfNull: false) this.state, @JsonKey(includeIfNull: false) this.country, @JsonKey(includeIfNull: false) this.isAddressSame, @JsonKey(includeIfNull: false) this.permanentAddr, @JsonKey(includeIfNull: false) this.permanentCity, @JsonKey(includeIfNull: false) this.permanentState, @JsonKey(includeIfNull: false) this.permanentCountry);
  factory _EditProfileDataModel.fromJson(Map<String, dynamic> json) => _$EditProfileDataModelFromJson(json);

@override@JsonKey(includeIfNull: false) final  String? about;
@override@JsonKey(includeIfNull: false) final  String? interests;
@override@JsonKey(includeIfNull: false) final  String? hobbies;
@override@JsonKey(includeIfNull: false) final  String? firstName;
@override@JsonKey(includeIfNull: false) final  String? middleName;
@override@JsonKey(includeIfNull: false) final  String? lastName;
@override@JsonKey(includeIfNull: false) final  String? personalMobile;
@override@JsonKey(includeIfNull: false) final  String? personalEmail;
@override@JsonKey(includeIfNull: false) final  Gender? gender;
@override@JsonKey(includeIfNull: false) final  DateTime? dob;
@override@JsonKey(includeIfNull: false) final  String? bloodGroup;
@override@JsonKey(includeIfNull: false) final  String? maritalStatus;
@override@JsonKey(includeIfNull: false) final  String? currentAddr;
@override@JsonKey(includeIfNull: false) final  String? city;
@override@JsonKey(includeIfNull: false) final  String? state;
@override@JsonKey(includeIfNull: false) final  String? country;
@override@JsonKey(includeIfNull: false) final  bool? isAddressSame;
@override@JsonKey(includeIfNull: false) final  String? permanentAddr;
@override@JsonKey(includeIfNull: false) final  String? permanentCity;
@override@JsonKey(includeIfNull: false) final  String? permanentState;
@override@JsonKey(includeIfNull: false) final  String? permanentCountry;

/// Create a copy of EditProfileDataModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EditProfileDataModelCopyWith<_EditProfileDataModel> get copyWith => __$EditProfileDataModelCopyWithImpl<_EditProfileDataModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EditProfileDataModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EditProfileDataModel&&(identical(other.about, about) || other.about == about)&&(identical(other.interests, interests) || other.interests == interests)&&(identical(other.hobbies, hobbies) || other.hobbies == hobbies)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.middleName, middleName) || other.middleName == middleName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.personalMobile, personalMobile) || other.personalMobile == personalMobile)&&(identical(other.personalEmail, personalEmail) || other.personalEmail == personalEmail)&&(identical(other.gender, gender) || other.gender == gender)&&(identical(other.dob, dob) || other.dob == dob)&&(identical(other.bloodGroup, bloodGroup) || other.bloodGroup == bloodGroup)&&(identical(other.maritalStatus, maritalStatus) || other.maritalStatus == maritalStatus)&&(identical(other.currentAddr, currentAddr) || other.currentAddr == currentAddr)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&(identical(other.country, country) || other.country == country)&&(identical(other.isAddressSame, isAddressSame) || other.isAddressSame == isAddressSame)&&(identical(other.permanentAddr, permanentAddr) || other.permanentAddr == permanentAddr)&&(identical(other.permanentCity, permanentCity) || other.permanentCity == permanentCity)&&(identical(other.permanentState, permanentState) || other.permanentState == permanentState)&&(identical(other.permanentCountry, permanentCountry) || other.permanentCountry == permanentCountry));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,about,interests,hobbies,firstName,middleName,lastName,personalMobile,personalEmail,gender,dob,bloodGroup,maritalStatus,currentAddr,city,state,country,isAddressSame,permanentAddr,permanentCity,permanentState,permanentCountry]);

@override
String toString() {
  return 'EditProfileDataModel(about: $about, interests: $interests, hobbies: $hobbies, firstName: $firstName, middleName: $middleName, lastName: $lastName, personalMobile: $personalMobile, personalEmail: $personalEmail, gender: $gender, dob: $dob, bloodGroup: $bloodGroup, maritalStatus: $maritalStatus, currentAddr: $currentAddr, city: $city, state: $state, country: $country, isAddressSame: $isAddressSame, permanentAddr: $permanentAddr, permanentCity: $permanentCity, permanentState: $permanentState, permanentCountry: $permanentCountry)';
}


}

/// @nodoc
abstract mixin class _$EditProfileDataModelCopyWith<$Res> implements $EditProfileDataModelCopyWith<$Res> {
  factory _$EditProfileDataModelCopyWith(_EditProfileDataModel value, $Res Function(_EditProfileDataModel) _then) = __$EditProfileDataModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(includeIfNull: false) String? about,@JsonKey(includeIfNull: false) String? interests,@JsonKey(includeIfNull: false) String? hobbies,@JsonKey(includeIfNull: false) String? firstName,@JsonKey(includeIfNull: false) String? middleName,@JsonKey(includeIfNull: false) String? lastName,@JsonKey(includeIfNull: false) String? personalMobile,@JsonKey(includeIfNull: false) String? personalEmail,@JsonKey(includeIfNull: false) Gender? gender,@JsonKey(includeIfNull: false) DateTime? dob,@JsonKey(includeIfNull: false) String? bloodGroup,@JsonKey(includeIfNull: false) String? maritalStatus,@JsonKey(includeIfNull: false) String? currentAddr,@JsonKey(includeIfNull: false) String? city,@JsonKey(includeIfNull: false) String? state,@JsonKey(includeIfNull: false) String? country,@JsonKey(includeIfNull: false) bool? isAddressSame,@JsonKey(includeIfNull: false) String? permanentAddr,@JsonKey(includeIfNull: false) String? permanentCity,@JsonKey(includeIfNull: false) String? permanentState,@JsonKey(includeIfNull: false) String? permanentCountry
});




}
/// @nodoc
class __$EditProfileDataModelCopyWithImpl<$Res>
    implements _$EditProfileDataModelCopyWith<$Res> {
  __$EditProfileDataModelCopyWithImpl(this._self, this._then);

  final _EditProfileDataModel _self;
  final $Res Function(_EditProfileDataModel) _then;

/// Create a copy of EditProfileDataModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? about = freezed,Object? interests = freezed,Object? hobbies = freezed,Object? firstName = freezed,Object? middleName = freezed,Object? lastName = freezed,Object? personalMobile = freezed,Object? personalEmail = freezed,Object? gender = freezed,Object? dob = freezed,Object? bloodGroup = freezed,Object? maritalStatus = freezed,Object? currentAddr = freezed,Object? city = freezed,Object? state = freezed,Object? country = freezed,Object? isAddressSame = freezed,Object? permanentAddr = freezed,Object? permanentCity = freezed,Object? permanentState = freezed,Object? permanentCountry = freezed,}) {
  return _then(_EditProfileDataModel(
freezed == about ? _self.about : about // ignore: cast_nullable_to_non_nullable
as String?,freezed == interests ? _self.interests : interests // ignore: cast_nullable_to_non_nullable
as String?,freezed == hobbies ? _self.hobbies : hobbies // ignore: cast_nullable_to_non_nullable
as String?,freezed == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String?,freezed == middleName ? _self.middleName : middleName // ignore: cast_nullable_to_non_nullable
as String?,freezed == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String?,freezed == personalMobile ? _self.personalMobile : personalMobile // ignore: cast_nullable_to_non_nullable
as String?,freezed == personalEmail ? _self.personalEmail : personalEmail // ignore: cast_nullable_to_non_nullable
as String?,freezed == gender ? _self.gender : gender // ignore: cast_nullable_to_non_nullable
as Gender?,freezed == dob ? _self.dob : dob // ignore: cast_nullable_to_non_nullable
as DateTime?,freezed == bloodGroup ? _self.bloodGroup : bloodGroup // ignore: cast_nullable_to_non_nullable
as String?,freezed == maritalStatus ? _self.maritalStatus : maritalStatus // ignore: cast_nullable_to_non_nullable
as String?,freezed == currentAddr ? _self.currentAddr : currentAddr // ignore: cast_nullable_to_non_nullable
as String?,freezed == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String?,freezed == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String?,freezed == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String?,freezed == isAddressSame ? _self.isAddressSame : isAddressSame // ignore: cast_nullable_to_non_nullable
as bool?,freezed == permanentAddr ? _self.permanentAddr : permanentAddr // ignore: cast_nullable_to_non_nullable
as String?,freezed == permanentCity ? _self.permanentCity : permanentCity // ignore: cast_nullable_to_non_nullable
as String?,freezed == permanentState ? _self.permanentState : permanentState // ignore: cast_nullable_to_non_nullable
as String?,freezed == permanentCountry ? _self.permanentCountry : permanentCountry // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
