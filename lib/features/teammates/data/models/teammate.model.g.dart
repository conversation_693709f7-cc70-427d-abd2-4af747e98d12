// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'teammate.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TeammateModel _$TeammateModelFromJson(Map<String, dynamic> json) =>
    _TeammateModel(
      id: (json['id'] as num).toInt(),
      empNo: (json['empNo'] as num).toInt(),
      email: json['email'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      user: UserModel.fromJson(json['user'] as Map<String, dynamic>),
      office: json['office'] == null
          ? null
          : OfficeModel.fromJson(json['office'] as Map<String, dynamic>),
      designation: json['designation'] == null
          ? null
          : DesignationModel.fromJson(
              json['designation'] as Map<String, dynamic>,
            ),
      departments: (json['departments'] as List<dynamic>)
          .map((e) => DepartmentModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      jobRoles: (json['jobRoles'] as List<dynamic>)
          .map((e) => JobRoleModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$TeammateModelToJson(_TeammateModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'empNo': instance.empNo,
      'email': instance.email,
      'createdAt': instance.createdAt?.toIso8601String(),
      'user': instance.user.toJson(),
      'office': instance.office?.toJson(),
      'designation': instance.designation?.toJson(),
      'departments': instance.departments.map((e) => e.toJson()).toList(),
      'jobRoles': instance.jobRoles.map((e) => e.toJson()).toList(),
    };

_OfficeModel _$OfficeModelFromJson(Map<String, dynamic> json) => _OfficeModel(
  id: (json['id'] as num).toInt(),
  name: json['name'] as String,
  city: json['city'] as String,
  state: json['state'] as String?,
  country: json['country'] as String?,
  organizationId: (json['OrganizationId'] as num?)?.toInt(),
);

Map<String, dynamic> _$OfficeModelToJson(_OfficeModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'city': instance.city,
      'state': instance.state,
      'country': instance.country,
      'OrganizationId': instance.organizationId,
    };

_DesignationModel _$DesignationModelFromJson(Map<String, dynamic> json) =>
    _DesignationModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      organizationId: (json['OrganizationId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$DesignationModelToJson(_DesignationModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'OrganizationId': instance.organizationId,
    };

_DepartmentModel _$DepartmentModelFromJson(Map<String, dynamic> json) =>
    _DepartmentModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      type: json['type'] as String?,
      organizationId: (json['OrganizationId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$DepartmentModelToJson(_DepartmentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'OrganizationId': instance.organizationId,
    };

_JobRoleModel _$JobRoleModelFromJson(Map<String, dynamic> json) =>
    _JobRoleModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      organizationId: (json['OrganizationId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$JobRoleModelToJson(_JobRoleModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'OrganizationId': instance.organizationId,
    };

_WorkModeModel _$WorkModeModelFromJson(Map<String, dynamic> json) =>
    _WorkModeModel(id: json['id'] as String, name: json['name'] as String);

Map<String, dynamic> _$WorkModeModelToJson(_WorkModeModel instance) =>
    <String, dynamic>{'id': instance.id, 'name': instance.name};
