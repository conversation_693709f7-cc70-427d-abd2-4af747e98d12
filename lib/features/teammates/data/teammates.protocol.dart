import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/features/teammates/data/models/edit_profile_data.model.dart';
import 'package:hrms_tst/features/teammates/data/models/teammate_detail.model.dart';
import 'package:hrms_tst/shared/models/teammate_filter.dart';
import 'package:hrms_tst/shared/models/user.model.dart';

import 'models/teammate.model.dart';

abstract interface class TeammatesProtocol {
  Future<Result<List<OfficeModel>>> getOffices();
  Future<Result<List<DepartmentModel>>> getDepartments();
  Future<Result<List<JobRoleModel>>> getJobRoles();
  Future<Result<List<DesignationModel>>> getDesignations();

  Future<Result<List<TeammateModel>>> getTeammates({
    required List<TeammateFilter> filters,
  });
  Future<Result<List<TeammateModel>>> getTeammatesByQuery({
    required String query,
  });
  Future<Result<UserModel>> getTeammateById({required int id});
  Future<Result<TeammateDetailModel>> getTeammateDetail(int id);

  Future<Result<void>> updateProfile(EditProfileDataModel data);
}
