import 'package:hrms_tst/core/services/network/network.service.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:hrms_tst/features/teammates/data/models/edit_profile_data.model.dart';

import 'package:hrms_tst/features/teammates/data/models/teammate.model.dart';
import 'package:hrms_tst/features/teammates/data/models/teammate_detail.model.dart';
import 'package:hrms_tst/shared/models/teammate_filter.dart';
import 'package:hrms_tst/shared/models/user.model.dart';

import 'teammates.protocol.dart';

class TeammatesRepository implements TeammatesProtocol {
  final networkService = getIt.get<NetworkService>();

  @override
  Future<Result<List<DepartmentModel>>> getDepartments() async {
    return await networkService.get(
      '/departments',
      mapper: (response) {
        return (response.data['data']['departmentRecords'] as List<dynamic>)
            .map((e) => DepartmentModel.fromJson(e))
            .toList();
      },
    );
  }

  @override
  Future<Result<List<DesignationModel>>> getDesignations() async {
    return await networkService.get(
      '/designations',
      mapper: (response) {
        return (response.data['data']['designationRecords'] as List<dynamic>)
            .map((e) => DesignationModel.fromJson(e))
            .toList();
      },
    );
  }

  @override
  Future<Result<List<JobRoleModel>>> getJobRoles() async {
    return await networkService.get(
      '/job-roles',
      mapper: (response) {
        return (response.data['data']['jobRoleRecords'] as List<dynamic>)
            .map((e) => JobRoleModel.fromJson(e))
            .toList();
      },
    );
  }

  @override
  Future<Result<List<OfficeModel>>> getOffices() async {
    return await networkService.get(
      '/offices',
      mapper: (response) {
        return (response.data['data']['officeRecords'] as List<dynamic>)
            .map((e) => OfficeModel.fromJson(e))
            .toList();
      },
    );
  }

  @override
  Future<Result<List<TeammateModel>>> getTeammates({
    required List<TeammateFilter> filters,
  }) async {
    return await networkService.get(
      '/employees',
      queryParameters: {
        for (final filter in filters)
          if (filter.selectedOption != null)
            filter.key: filter.selectedOption.id,
      },
      mapper: (response) {
        return (response.data['data']['employeeRecords'] as List<dynamic>)
            .map((e) => TeammateModel.fromJson(e))
            .toList();
      },
    );
  }

  @override
  Future<Result<UserModel>> getTeammateById({required int id}) async {
    return await networkService.get(
      '/employees/$id',
      mapper: (response) {
        return UserModel.fromJson(response.data['data']['user']);
      },
    );
  }

  @override
  Future<Result<TeammateDetailModel>> getTeammateDetail(int id) async {
    return await networkService.get(
      '/employees/${id.toString()}',
      mapper: (response) {
        return TeammateDetailModel.fromJson(response.data['data']);
      },
    );
  }

  @override
  Future<Result<void>> updateProfile(EditProfileDataModel data) async {
    return await networkService.patch(
      '/employees/update-me',
      data: data.toJson(),
      mapper: (response) {
        return;
      },
    );
  }

  @override
  Future<Result<List<TeammateModel>>> getTeammatesByQuery({
    required String query,
  }) async {
    return await networkService.get(
      '/employees',
      queryParameters: {'searchKey': query},
      mapper: (response) {
        return (response.data['data']['employeeRecords'] as List<dynamic>)
            .map((e) => TeammateModel.fromJson(e))
            .toList();
      },
    );
  }
}
