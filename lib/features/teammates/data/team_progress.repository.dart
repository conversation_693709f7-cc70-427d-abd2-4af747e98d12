import 'package:hrms_tst/core/services/network/network.service.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:hrms_tst/features/teammates/data/models/team_progress.model.dart';

import 'team_progress.protocol.dart';

class TeamProgressRepository implements TeamProgressProtocol {
  final networkService = getIt.get<NetworkService>();

  @override
  Future<Result<List<BookProgressModel>>> getBookProgress({
    DateTime? fromDate,
    DateTime? toDate,
    int? employeeId,
  }) async {
    return await networkService.get(
      '/book-summaries',
      queryParameters: {
        if (fromDate != null) 'fromDate': fromDate.toIso8601String(),
        if (toDate != null) 'toDate': toDate.toIso8601String(),
        if (employeeId != null) 'EmployeeId': employeeId,
        if (employeeId == null) 'status': 'verified',
      },
      mapper: (response) {
        return (response.data['data']['data']['rows'] as List<dynamic>)
            .map((e) => BookProgressModel.fromJson(e))
            .toList();
      },
    );
  }

  @override
  Future<Result<List<CourseProgressModel>>> getCourseProgress({
    DateTime? fromDate,
    DateTime? toDate,
    int? employeeId,
  }) async {
    return await networkService.get(
      '/course-summaries',
      queryParameters: {
        if (fromDate != null) 'fromDate': fromDate.toIso8601String(),
        if (toDate != null) 'toDate': toDate.toIso8601String(),
        if (employeeId != null) 'EmployeeId': employeeId,
        if (employeeId == null) 'status': 'verified',
      },
      mapper: (response) {
        return (response.data['data']['data']['rows'] as List<dynamic>)
            .map((e) => CourseProgressModel.fromJson(e))
            .toList();
      },
    );
  }

  @override
  Future<Result<List<EventProgressModel>>> getEventProgress({
    DateTime? fromDate,
    DateTime? toDate,
    int? employeeId,
  }) async {
    return await networkService.get(
      '/event-summaries',
      queryParameters: {
        if (fromDate != null) 'fromDate': fromDate.toIso8601String(),
        if (toDate != null) 'toDate': toDate.toIso8601String(),
        if (employeeId != null) 'EmployeeId': employeeId,
        if (employeeId == null) 'status': 'verified',
      },
      mapper: (response) {
        return (response.data['data']['data']['rows'] as List<dynamic>)
            .map((e) => EventProgressModel.fromJson(e))
            .toList();
      },
    );
  }

  @override
  Future<Result<List<GDProgressModel>>> getGDProgress({
    DateTime? fromDate,
    DateTime? toDate,
    int? employeeId,
  }) async {
    return await networkService.get(
      '/gd-summaries',
      queryParameters: {
        if (fromDate != null) 'fromDate': fromDate.toIso8601String(),
        if (toDate != null) 'toDate': toDate.toIso8601String(),
        if (employeeId != null) 'EmployeeId': employeeId,
        if (employeeId == null) 'status': 'verified',
      },
      mapper: (response) {
        return (response.data['data']['data']['rows'] as List<dynamic>)
            .map((e) => GDProgressModel.fromJson(e))
            .toList();
      },
    );
  }

  @override
  Future<Result<List<SessionProgressModel>>> getSessionProgress({
    DateTime? fromDate,
    DateTime? toDate,
    int? employeeId,
  }) async {
    return await networkService.get(
      '/session-summaries',
      queryParameters: {
        if (fromDate != null) 'fromDate': fromDate.toIso8601String(),
        if (toDate != null) 'toDate': toDate.toIso8601String(),
        if (employeeId != null) 'EmployeeId': employeeId,
        if (employeeId == null) 'status': 'verified',
      },
      mapper: (response) {
        return (response.data['data']['data']['rows'] as List<dynamic>)
            .map((e) => SessionProgressModel.fromJson(e))
            .toList();
      },
    );
  }

  @override
  Future<Result<List<String>>> getPhotosForEvent({required int eventId}) async {
    return await networkService.get(
      '/event-images',
      queryParameters: {'EventSummaryId': eventId},
      mapper: (response) {
        return (response.data['data']['eventImages']['rows'] as List<dynamic>)
            .map((e) => e['url'] as String)
            .toList();
      },
    );
  }
}
