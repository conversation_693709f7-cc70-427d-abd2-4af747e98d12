import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/features/teammates/data/models/team_progress.model.dart';

abstract interface class TeamProgressProtocol {
  Future<Result<List<BookProgressModel>>> getBookProgress({
    DateTime? fromDate,
    DateTime? toDate,
    int? employeeId,
  });
  Future<Result<List<CourseProgressModel>>> getCourseProgress({
    DateTime? fromDate,
    DateTime? toDate,
    int? employeeId,
  });
  Future<Result<List<GDProgressModel>>> getGDProgress({
    DateTime? fromDate,
    DateTime? toDate,
    int? employeeId,
  });
  Future<Result<List<SessionProgressModel>>> getSessionProgress({
    DateTime? fromDate,
    DateTime? toDate,
    int? employeeId,
  });
  Future<Result<List<EventProgressModel>>> getEventProgress({
    DateTime? fromDate,
    DateTime? toDate,
    int? employeeId,
  });

  Future<Result<List<String>>> getPhotosForEvent({required final int eventId});
}
