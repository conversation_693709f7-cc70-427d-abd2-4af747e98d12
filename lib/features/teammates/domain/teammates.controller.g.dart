// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'teammates.controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$teammateByIdHash() => r'201771eee80ab300ad7b9c0dd8e31ccdd51fba38';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [teammateById].
@ProviderFor(teammateById)
const teammateByIdProvider = TeammateByIdFamily();

/// See also [teammateById].
class TeammateByIdFamily extends Family<AsyncValue<UserModel?>> {
  /// See also [teammateById].
  const TeammateByIdFamily();

  /// See also [teammateById].
  TeammateByIdProvider call(int? id) {
    return TeammateByIdProvider(id);
  }

  @override
  TeammateByIdProvider getProviderOverride(
    covariant TeammateByIdProvider provider,
  ) {
    return call(provider.id);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'teammateByIdProvider';
}

/// See also [teammateById].
class TeammateByIdProvider extends AutoDisposeFutureProvider<UserModel?> {
  /// See also [teammateById].
  TeammateByIdProvider(int? id)
    : this._internal(
        (ref) => teammateById(ref as TeammateByIdRef, id),
        from: teammateByIdProvider,
        name: r'teammateByIdProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$teammateByIdHash,
        dependencies: TeammateByIdFamily._dependencies,
        allTransitiveDependencies:
            TeammateByIdFamily._allTransitiveDependencies,
        id: id,
      );

  TeammateByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
  }) : super.internal();

  final int? id;

  @override
  Override overrideWith(
    FutureOr<UserModel?> Function(TeammateByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TeammateByIdProvider._internal(
        (ref) => create(ref as TeammateByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<UserModel?> createElement() {
    return _TeammateByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TeammateByIdProvider && other.id == id;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TeammateByIdRef on AutoDisposeFutureProviderRef<UserModel?> {
  /// The parameter `id` of this provider.
  int? get id;
}

class _TeammateByIdProviderElement
    extends AutoDisposeFutureProviderElement<UserModel?>
    with TeammateByIdRef {
  _TeammateByIdProviderElement(super.provider);

  @override
  int? get id => (origin as TeammateByIdProvider).id;
}

String _$teammateDetailHash() => r'f680a5b8964a5286584e7e6aa4f9dc10cb4afdab';

/// See also [teammateDetail].
@ProviderFor(teammateDetail)
const teammateDetailProvider = TeammateDetailFamily();

/// See also [teammateDetail].
class TeammateDetailFamily extends Family<AsyncValue<TeammateDetailModel>> {
  /// See also [teammateDetail].
  const TeammateDetailFamily();

  /// See also [teammateDetail].
  TeammateDetailProvider call(int id) {
    return TeammateDetailProvider(id);
  }

  @override
  TeammateDetailProvider getProviderOverride(
    covariant TeammateDetailProvider provider,
  ) {
    return call(provider.id);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'teammateDetailProvider';
}

/// See also [teammateDetail].
class TeammateDetailProvider
    extends AutoDisposeFutureProvider<TeammateDetailModel> {
  /// See also [teammateDetail].
  TeammateDetailProvider(int id)
    : this._internal(
        (ref) => teammateDetail(ref as TeammateDetailRef, id),
        from: teammateDetailProvider,
        name: r'teammateDetailProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$teammateDetailHash,
        dependencies: TeammateDetailFamily._dependencies,
        allTransitiveDependencies:
            TeammateDetailFamily._allTransitiveDependencies,
        id: id,
      );

  TeammateDetailProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
  }) : super.internal();

  final int id;

  @override
  Override overrideWith(
    FutureOr<TeammateDetailModel> Function(TeammateDetailRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TeammateDetailProvider._internal(
        (ref) => create(ref as TeammateDetailRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<TeammateDetailModel> createElement() {
    return _TeammateDetailProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TeammateDetailProvider && other.id == id;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TeammateDetailRef on AutoDisposeFutureProviderRef<TeammateDetailModel> {
  /// The parameter `id` of this provider.
  int get id;
}

class _TeammateDetailProviderElement
    extends AutoDisposeFutureProviderElement<TeammateDetailModel>
    with TeammateDetailRef {
  _TeammateDetailProviderElement(super.provider);

  @override
  int get id => (origin as TeammateDetailProvider).id;
}

String _$teammatesByQueryHash() => r'e2be2df9ea058d863ea6a704199e277340f64bd0';

/// See also [teammatesByQuery].
@ProviderFor(teammatesByQuery)
const teammatesByQueryProvider = TeammatesByQueryFamily();

/// See also [teammatesByQuery].
class TeammatesByQueryFamily extends Family<AsyncValue<List<TeammateModel>>> {
  /// See also [teammatesByQuery].
  const TeammatesByQueryFamily();

  /// See also [teammatesByQuery].
  TeammatesByQueryProvider call(String query) {
    return TeammatesByQueryProvider(query);
  }

  @override
  TeammatesByQueryProvider getProviderOverride(
    covariant TeammatesByQueryProvider provider,
  ) {
    return call(provider.query);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'teammatesByQueryProvider';
}

/// See also [teammatesByQuery].
class TeammatesByQueryProvider
    extends AutoDisposeFutureProvider<List<TeammateModel>> {
  /// See also [teammatesByQuery].
  TeammatesByQueryProvider(String query)
    : this._internal(
        (ref) => teammatesByQuery(ref as TeammatesByQueryRef, query),
        from: teammatesByQueryProvider,
        name: r'teammatesByQueryProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$teammatesByQueryHash,
        dependencies: TeammatesByQueryFamily._dependencies,
        allTransitiveDependencies:
            TeammatesByQueryFamily._allTransitiveDependencies,
        query: query,
      );

  TeammatesByQueryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final String query;

  @override
  Override overrideWith(
    FutureOr<List<TeammateModel>> Function(TeammatesByQueryRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TeammatesByQueryProvider._internal(
        (ref) => create(ref as TeammatesByQueryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<TeammateModel>> createElement() {
    return _TeammatesByQueryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TeammatesByQueryProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TeammatesByQueryRef on AutoDisposeFutureProviderRef<List<TeammateModel>> {
  /// The parameter `query` of this provider.
  String get query;
}

class _TeammatesByQueryProviderElement
    extends AutoDisposeFutureProviderElement<List<TeammateModel>>
    with TeammatesByQueryRef {
  _TeammatesByQueryProviderElement(super.provider);

  @override
  String get query => (origin as TeammatesByQueryProvider).query;
}

String _$teammatesHash() => r'09e72d72808fa2a4351e7dc127f4a3df5d2d3e25';

/// See also [Teammates].
@ProviderFor(Teammates)
final teammatesProvider =
    AutoDisposeAsyncNotifierProvider<Teammates, List<TeammateModel>>.internal(
      Teammates.new,
      name: r'teammatesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$teammatesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$Teammates = AutoDisposeAsyncNotifier<List<TeammateModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
