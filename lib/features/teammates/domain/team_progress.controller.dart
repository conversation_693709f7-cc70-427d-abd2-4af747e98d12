import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../data/models/team_progress.model.dart';
import '../data/team_progress.protocol.dart';

part 'team_progress.controller.g.dart';

@riverpod
FutureOr<List<BookProgressModel>> bookProgress(
  Ref ref,
  DateTime? fromDate,
  DateTime? toDate,
  int? employeeId,
) async {
  return (await getIt.get<TeamProgressProtocol>().getBookProgress(
    fromDate: fromDate,
    toDate: toDate,
    employeeId: employeeId,
  )).ignoreFailure();
}

@riverpod
FutureOr<List<CourseProgressModel>> courseProgress(
  Ref ref,
  DateTime? fromDate,
  DateTime? toDate,
  int? employeeId,
) async {
  return (await getIt.get<TeamProgressProtocol>().getCourseProgress(
    fromDate: fromDate,
    toDate: toDate,
    employeeId: employeeId,
  )).ignoreFailure();
}

@riverpod
FutureOr<List<GDProgressModel>> gdProgress(
  Ref ref,
  DateTime? fromDate,
  DateTime? toDate,
  int? employeeId,
) async {
  return (await getIt.get<TeamProgressProtocol>().getGDProgress(
    fromDate: fromDate,
    toDate: toDate,
    employeeId: employeeId,
  )).ignoreFailure();
}

@riverpod
FutureOr<List<SessionProgressModel>> sessionProgress(
  Ref ref,
  DateTime? fromDate,
  DateTime? toDate,
  int? employeeId,
) async {
  return (await getIt.get<TeamProgressProtocol>().getSessionProgress(
    fromDate: fromDate,
    toDate: toDate,
    employeeId: employeeId,
  )).ignoreFailure();
}

@riverpod
FutureOr<List<EventProgressModel>> eventProgress(
  Ref ref,
  DateTime? fromDate,
  DateTime? toDate,
  int? employeeId,
) async {
  return (await getIt.get<TeamProgressProtocol>().getEventProgress(
    fromDate: fromDate,
    toDate: toDate,
    employeeId: employeeId,
  )).ignoreFailure();
}

@riverpod
FutureOr<List<String>> eventPhotos(Ref ref, int eventId) async {
  return (await getIt.get<TeamProgressProtocol>().getPhotosForEvent(
    eventId: eventId,
  )).ignoreFailure();
}
