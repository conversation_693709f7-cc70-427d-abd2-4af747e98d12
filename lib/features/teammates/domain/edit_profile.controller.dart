import 'dart:convert';
import 'dart:developer';

import 'package:hrms_tst/core/services/auth/protocol/auth.protocol.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:hrms_tst/features/teammates/data/models/edit_profile_data.model.dart';
import 'package:hrms_tst/features/teammates/data/teammates.protocol.dart';
import 'package:hrms_tst/shared/models/user.model.dart';
import 'package:hrms_tst/shared/providers/auth.provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'edit_profile.controller.g.dart';

@riverpod
class ProfileData extends _$ProfileData {
  @override
  UserModel build() {
    return ref.read(authProvider).requireValue!;
  }

  Future<Result<void>> refresh() async {
    final userResult = await getIt.get<AuthProtocol>().getMe();
    return userResult.fold(
      onSuccess: (data) {
        state = data!.$1;
        return Success(null);
      },
      onFailure: (message) => Failure(message),
    );
  }
}

@riverpod
class EditProfile extends _$EditProfile {
  @override
  EditProfileDataModel build() {
    final userDetails = ref.read(profileDataProvider);
    return EditProfileDataModel(
      userDetails.details?.about,
      userDetails.details?.interests,
      userDetails.details?.hobbies,
      userDetails.firstName,
      userDetails.middleName,
      userDetails.lastName,
      userDetails.personalMobile,
      userDetails.personalEmail,
      userDetails.gender,
      userDetails.dob,
      userDetails.details?.bloodGroup,
      userDetails.details?.maritalStatus,
      userDetails.details?.currentAddr,
      userDetails.details?.city,
      userDetails.details?.state,
      userDetails.details?.country,
      userDetails.details?.isAddressSame,
      userDetails.details?.permanentAddr,
      userDetails.details?.permanentCity,
      userDetails.details?.permanentState,
      userDetails.details?.permanentCountry,
    );
  }

  set about(String? value) {
    state = state.copyWith(about: value);
  }

  set interests(String? value) {
    state = state.copyWith(interests: value);
  }

  set hobbies(String? value) {
    state = state.copyWith(hobbies: value);
  }

  set firstName(String? value) {
    state = state.copyWith(firstName: value);
  }

  set middleName(String? value) {
    state = state.copyWith(middleName: value);
  }

  set lastName(String? value) {
    state = state.copyWith(lastName: value);
  }

  set personalMobile(String? value) {
    state = state.copyWith(personalMobile: value);
  }

  set personalEmail(String? value) {
    state = state.copyWith(personalEmail: value);
  }

  set gender(Gender? value) {
    state = state.copyWith(gender: value);
  }

  set dob(DateTime? value) {
    state = state.copyWith(dob: value);
  }

  set bloodGroup(String? value) {
    state = state.copyWith(bloodGroup: value);
  }

  set maritalStatus(String? value) {
    state = state.copyWith(maritalStatus: value);
  }

  set currentAddr(String? value) {
    state = state.copyWith(currentAddr: value);
  }

  set city(String? value) {
    state = state.copyWith(city: value);
  }

  set addressState(String? value) {
    state = state.copyWith(state: value);
  }

  set country(String? value) {
    state = state.copyWith(country: value);
  }

  set isAddressSame(bool? value) {
    state = state.copyWith(isAddressSame: value);
  }

  set permanentAddr(String? value) {
    state = state.copyWith(permanentAddr: value);
  }

  set permanentCity(String? value) {
    state = state.copyWith(permanentCity: value);
  }

  set permanentState(String? value) {
    state = state.copyWith(permanentState: value);
  }

  set permanentCountry(String? value) {
    state = state.copyWith(permanentCountry: value);
  }

  Future<Result<void>> save() async {
    log(JsonEncoder.withIndent('    ').convert(state.toJson()));
    final result = await getIt.get<TeammatesProtocol>().updateProfile(state);
    if (result.isSuccess) {
      final refreshResult = await ref
          .read(profileDataProvider.notifier)
          .refresh();
      if (refreshResult.isFailure) {
        return Failure(refreshResult.failure.message);
      }
      ref.invalidateSelf();
    }

    return result;
  }
}
