// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'team_progress.controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$bookProgressHash() => r'afc4402e3619f5040806d143893d9e7732dcacad';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [bookProgress].
@ProviderFor(bookProgress)
const bookProgressProvider = BookProgressFamily();

/// See also [bookProgress].
class BookProgressFamily extends Family<AsyncValue<List<BookProgressModel>>> {
  /// See also [bookProgress].
  const BookProgressFamily();

  /// See also [bookProgress].
  BookProgressProvider call(
    DateTime? fromDate,
    DateTime? toDate,
    int? employeeId,
  ) {
    return BookProgressProvider(fromDate, toDate, employeeId);
  }

  @override
  BookProgressProvider getProviderOverride(
    covariant BookProgressProvider provider,
  ) {
    return call(provider.fromDate, provider.toDate, provider.employeeId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'bookProgressProvider';
}

/// See also [bookProgress].
class BookProgressProvider
    extends AutoDisposeFutureProvider<List<BookProgressModel>> {
  /// See also [bookProgress].
  BookProgressProvider(DateTime? fromDate, DateTime? toDate, int? employeeId)
    : this._internal(
        (ref) =>
            bookProgress(ref as BookProgressRef, fromDate, toDate, employeeId),
        from: bookProgressProvider,
        name: r'bookProgressProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$bookProgressHash,
        dependencies: BookProgressFamily._dependencies,
        allTransitiveDependencies:
            BookProgressFamily._allTransitiveDependencies,
        fromDate: fromDate,
        toDate: toDate,
        employeeId: employeeId,
      );

  BookProgressProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.fromDate,
    required this.toDate,
    required this.employeeId,
  }) : super.internal();

  final DateTime? fromDate;
  final DateTime? toDate;
  final int? employeeId;

  @override
  Override overrideWith(
    FutureOr<List<BookProgressModel>> Function(BookProgressRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: BookProgressProvider._internal(
        (ref) => create(ref as BookProgressRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        fromDate: fromDate,
        toDate: toDate,
        employeeId: employeeId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<BookProgressModel>> createElement() {
    return _BookProgressProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is BookProgressProvider &&
        other.fromDate == fromDate &&
        other.toDate == toDate &&
        other.employeeId == employeeId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, fromDate.hashCode);
    hash = _SystemHash.combine(hash, toDate.hashCode);
    hash = _SystemHash.combine(hash, employeeId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin BookProgressRef on AutoDisposeFutureProviderRef<List<BookProgressModel>> {
  /// The parameter `fromDate` of this provider.
  DateTime? get fromDate;

  /// The parameter `toDate` of this provider.
  DateTime? get toDate;

  /// The parameter `employeeId` of this provider.
  int? get employeeId;
}

class _BookProgressProviderElement
    extends AutoDisposeFutureProviderElement<List<BookProgressModel>>
    with BookProgressRef {
  _BookProgressProviderElement(super.provider);

  @override
  DateTime? get fromDate => (origin as BookProgressProvider).fromDate;
  @override
  DateTime? get toDate => (origin as BookProgressProvider).toDate;
  @override
  int? get employeeId => (origin as BookProgressProvider).employeeId;
}

String _$courseProgressHash() => r'bc8e49251166b6b7241b489e5318445fbeb687d6';

/// See also [courseProgress].
@ProviderFor(courseProgress)
const courseProgressProvider = CourseProgressFamily();

/// See also [courseProgress].
class CourseProgressFamily
    extends Family<AsyncValue<List<CourseProgressModel>>> {
  /// See also [courseProgress].
  const CourseProgressFamily();

  /// See also [courseProgress].
  CourseProgressProvider call(
    DateTime? fromDate,
    DateTime? toDate,
    int? employeeId,
  ) {
    return CourseProgressProvider(fromDate, toDate, employeeId);
  }

  @override
  CourseProgressProvider getProviderOverride(
    covariant CourseProgressProvider provider,
  ) {
    return call(provider.fromDate, provider.toDate, provider.employeeId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'courseProgressProvider';
}

/// See also [courseProgress].
class CourseProgressProvider
    extends AutoDisposeFutureProvider<List<CourseProgressModel>> {
  /// See also [courseProgress].
  CourseProgressProvider(DateTime? fromDate, DateTime? toDate, int? employeeId)
    : this._internal(
        (ref) => courseProgress(
          ref as CourseProgressRef,
          fromDate,
          toDate,
          employeeId,
        ),
        from: courseProgressProvider,
        name: r'courseProgressProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$courseProgressHash,
        dependencies: CourseProgressFamily._dependencies,
        allTransitiveDependencies:
            CourseProgressFamily._allTransitiveDependencies,
        fromDate: fromDate,
        toDate: toDate,
        employeeId: employeeId,
      );

  CourseProgressProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.fromDate,
    required this.toDate,
    required this.employeeId,
  }) : super.internal();

  final DateTime? fromDate;
  final DateTime? toDate;
  final int? employeeId;

  @override
  Override overrideWith(
    FutureOr<List<CourseProgressModel>> Function(CourseProgressRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CourseProgressProvider._internal(
        (ref) => create(ref as CourseProgressRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        fromDate: fromDate,
        toDate: toDate,
        employeeId: employeeId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<CourseProgressModel>> createElement() {
    return _CourseProgressProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CourseProgressProvider &&
        other.fromDate == fromDate &&
        other.toDate == toDate &&
        other.employeeId == employeeId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, fromDate.hashCode);
    hash = _SystemHash.combine(hash, toDate.hashCode);
    hash = _SystemHash.combine(hash, employeeId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CourseProgressRef
    on AutoDisposeFutureProviderRef<List<CourseProgressModel>> {
  /// The parameter `fromDate` of this provider.
  DateTime? get fromDate;

  /// The parameter `toDate` of this provider.
  DateTime? get toDate;

  /// The parameter `employeeId` of this provider.
  int? get employeeId;
}

class _CourseProgressProviderElement
    extends AutoDisposeFutureProviderElement<List<CourseProgressModel>>
    with CourseProgressRef {
  _CourseProgressProviderElement(super.provider);

  @override
  DateTime? get fromDate => (origin as CourseProgressProvider).fromDate;
  @override
  DateTime? get toDate => (origin as CourseProgressProvider).toDate;
  @override
  int? get employeeId => (origin as CourseProgressProvider).employeeId;
}

String _$gdProgressHash() => r'6ed84d9c0fc404dbe3ad4699b58c694b87069a0f';

/// See also [gdProgress].
@ProviderFor(gdProgress)
const gdProgressProvider = GdProgressFamily();

/// See also [gdProgress].
class GdProgressFamily extends Family<AsyncValue<List<GDProgressModel>>> {
  /// See also [gdProgress].
  const GdProgressFamily();

  /// See also [gdProgress].
  GdProgressProvider call(
    DateTime? fromDate,
    DateTime? toDate,
    int? employeeId,
  ) {
    return GdProgressProvider(fromDate, toDate, employeeId);
  }

  @override
  GdProgressProvider getProviderOverride(
    covariant GdProgressProvider provider,
  ) {
    return call(provider.fromDate, provider.toDate, provider.employeeId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'gdProgressProvider';
}

/// See also [gdProgress].
class GdProgressProvider
    extends AutoDisposeFutureProvider<List<GDProgressModel>> {
  /// See also [gdProgress].
  GdProgressProvider(DateTime? fromDate, DateTime? toDate, int? employeeId)
    : this._internal(
        (ref) => gdProgress(ref as GdProgressRef, fromDate, toDate, employeeId),
        from: gdProgressProvider,
        name: r'gdProgressProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$gdProgressHash,
        dependencies: GdProgressFamily._dependencies,
        allTransitiveDependencies: GdProgressFamily._allTransitiveDependencies,
        fromDate: fromDate,
        toDate: toDate,
        employeeId: employeeId,
      );

  GdProgressProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.fromDate,
    required this.toDate,
    required this.employeeId,
  }) : super.internal();

  final DateTime? fromDate;
  final DateTime? toDate;
  final int? employeeId;

  @override
  Override overrideWith(
    FutureOr<List<GDProgressModel>> Function(GdProgressRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: GdProgressProvider._internal(
        (ref) => create(ref as GdProgressRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        fromDate: fromDate,
        toDate: toDate,
        employeeId: employeeId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<GDProgressModel>> createElement() {
    return _GdProgressProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is GdProgressProvider &&
        other.fromDate == fromDate &&
        other.toDate == toDate &&
        other.employeeId == employeeId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, fromDate.hashCode);
    hash = _SystemHash.combine(hash, toDate.hashCode);
    hash = _SystemHash.combine(hash, employeeId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin GdProgressRef on AutoDisposeFutureProviderRef<List<GDProgressModel>> {
  /// The parameter `fromDate` of this provider.
  DateTime? get fromDate;

  /// The parameter `toDate` of this provider.
  DateTime? get toDate;

  /// The parameter `employeeId` of this provider.
  int? get employeeId;
}

class _GdProgressProviderElement
    extends AutoDisposeFutureProviderElement<List<GDProgressModel>>
    with GdProgressRef {
  _GdProgressProviderElement(super.provider);

  @override
  DateTime? get fromDate => (origin as GdProgressProvider).fromDate;
  @override
  DateTime? get toDate => (origin as GdProgressProvider).toDate;
  @override
  int? get employeeId => (origin as GdProgressProvider).employeeId;
}

String _$sessionProgressHash() => r'c947db1f0e21cfa5329db90ddcb43e7065babcd2';

/// See also [sessionProgress].
@ProviderFor(sessionProgress)
const sessionProgressProvider = SessionProgressFamily();

/// See also [sessionProgress].
class SessionProgressFamily
    extends Family<AsyncValue<List<SessionProgressModel>>> {
  /// See also [sessionProgress].
  const SessionProgressFamily();

  /// See also [sessionProgress].
  SessionProgressProvider call(
    DateTime? fromDate,
    DateTime? toDate,
    int? employeeId,
  ) {
    return SessionProgressProvider(fromDate, toDate, employeeId);
  }

  @override
  SessionProgressProvider getProviderOverride(
    covariant SessionProgressProvider provider,
  ) {
    return call(provider.fromDate, provider.toDate, provider.employeeId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'sessionProgressProvider';
}

/// See also [sessionProgress].
class SessionProgressProvider
    extends AutoDisposeFutureProvider<List<SessionProgressModel>> {
  /// See also [sessionProgress].
  SessionProgressProvider(DateTime? fromDate, DateTime? toDate, int? employeeId)
    : this._internal(
        (ref) => sessionProgress(
          ref as SessionProgressRef,
          fromDate,
          toDate,
          employeeId,
        ),
        from: sessionProgressProvider,
        name: r'sessionProgressProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$sessionProgressHash,
        dependencies: SessionProgressFamily._dependencies,
        allTransitiveDependencies:
            SessionProgressFamily._allTransitiveDependencies,
        fromDate: fromDate,
        toDate: toDate,
        employeeId: employeeId,
      );

  SessionProgressProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.fromDate,
    required this.toDate,
    required this.employeeId,
  }) : super.internal();

  final DateTime? fromDate;
  final DateTime? toDate;
  final int? employeeId;

  @override
  Override overrideWith(
    FutureOr<List<SessionProgressModel>> Function(SessionProgressRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SessionProgressProvider._internal(
        (ref) => create(ref as SessionProgressRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        fromDate: fromDate,
        toDate: toDate,
        employeeId: employeeId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<SessionProgressModel>> createElement() {
    return _SessionProgressProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SessionProgressProvider &&
        other.fromDate == fromDate &&
        other.toDate == toDate &&
        other.employeeId == employeeId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, fromDate.hashCode);
    hash = _SystemHash.combine(hash, toDate.hashCode);
    hash = _SystemHash.combine(hash, employeeId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SessionProgressRef
    on AutoDisposeFutureProviderRef<List<SessionProgressModel>> {
  /// The parameter `fromDate` of this provider.
  DateTime? get fromDate;

  /// The parameter `toDate` of this provider.
  DateTime? get toDate;

  /// The parameter `employeeId` of this provider.
  int? get employeeId;
}

class _SessionProgressProviderElement
    extends AutoDisposeFutureProviderElement<List<SessionProgressModel>>
    with SessionProgressRef {
  _SessionProgressProviderElement(super.provider);

  @override
  DateTime? get fromDate => (origin as SessionProgressProvider).fromDate;
  @override
  DateTime? get toDate => (origin as SessionProgressProvider).toDate;
  @override
  int? get employeeId => (origin as SessionProgressProvider).employeeId;
}

String _$eventProgressHash() => r'8d0dfba0228a146c1ecd3bc114e5e19ea34f48ca';

/// See also [eventProgress].
@ProviderFor(eventProgress)
const eventProgressProvider = EventProgressFamily();

/// See also [eventProgress].
class EventProgressFamily extends Family<AsyncValue<List<EventProgressModel>>> {
  /// See also [eventProgress].
  const EventProgressFamily();

  /// See also [eventProgress].
  EventProgressProvider call(
    DateTime? fromDate,
    DateTime? toDate,
    int? employeeId,
  ) {
    return EventProgressProvider(fromDate, toDate, employeeId);
  }

  @override
  EventProgressProvider getProviderOverride(
    covariant EventProgressProvider provider,
  ) {
    return call(provider.fromDate, provider.toDate, provider.employeeId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'eventProgressProvider';
}

/// See also [eventProgress].
class EventProgressProvider
    extends AutoDisposeFutureProvider<List<EventProgressModel>> {
  /// See also [eventProgress].
  EventProgressProvider(DateTime? fromDate, DateTime? toDate, int? employeeId)
    : this._internal(
        (ref) => eventProgress(
          ref as EventProgressRef,
          fromDate,
          toDate,
          employeeId,
        ),
        from: eventProgressProvider,
        name: r'eventProgressProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$eventProgressHash,
        dependencies: EventProgressFamily._dependencies,
        allTransitiveDependencies:
            EventProgressFamily._allTransitiveDependencies,
        fromDate: fromDate,
        toDate: toDate,
        employeeId: employeeId,
      );

  EventProgressProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.fromDate,
    required this.toDate,
    required this.employeeId,
  }) : super.internal();

  final DateTime? fromDate;
  final DateTime? toDate;
  final int? employeeId;

  @override
  Override overrideWith(
    FutureOr<List<EventProgressModel>> Function(EventProgressRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: EventProgressProvider._internal(
        (ref) => create(ref as EventProgressRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        fromDate: fromDate,
        toDate: toDate,
        employeeId: employeeId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<EventProgressModel>> createElement() {
    return _EventProgressProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is EventProgressProvider &&
        other.fromDate == fromDate &&
        other.toDate == toDate &&
        other.employeeId == employeeId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, fromDate.hashCode);
    hash = _SystemHash.combine(hash, toDate.hashCode);
    hash = _SystemHash.combine(hash, employeeId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin EventProgressRef
    on AutoDisposeFutureProviderRef<List<EventProgressModel>> {
  /// The parameter `fromDate` of this provider.
  DateTime? get fromDate;

  /// The parameter `toDate` of this provider.
  DateTime? get toDate;

  /// The parameter `employeeId` of this provider.
  int? get employeeId;
}

class _EventProgressProviderElement
    extends AutoDisposeFutureProviderElement<List<EventProgressModel>>
    with EventProgressRef {
  _EventProgressProviderElement(super.provider);

  @override
  DateTime? get fromDate => (origin as EventProgressProvider).fromDate;
  @override
  DateTime? get toDate => (origin as EventProgressProvider).toDate;
  @override
  int? get employeeId => (origin as EventProgressProvider).employeeId;
}

String _$eventPhotosHash() => r'b83933836487b81edcbad6afa9431e64db563045';

/// See also [eventPhotos].
@ProviderFor(eventPhotos)
const eventPhotosProvider = EventPhotosFamily();

/// See also [eventPhotos].
class EventPhotosFamily extends Family<AsyncValue<List<String>>> {
  /// See also [eventPhotos].
  const EventPhotosFamily();

  /// See also [eventPhotos].
  EventPhotosProvider call(int eventId) {
    return EventPhotosProvider(eventId);
  }

  @override
  EventPhotosProvider getProviderOverride(
    covariant EventPhotosProvider provider,
  ) {
    return call(provider.eventId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'eventPhotosProvider';
}

/// See also [eventPhotos].
class EventPhotosProvider extends AutoDisposeFutureProvider<List<String>> {
  /// See also [eventPhotos].
  EventPhotosProvider(int eventId)
    : this._internal(
        (ref) => eventPhotos(ref as EventPhotosRef, eventId),
        from: eventPhotosProvider,
        name: r'eventPhotosProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$eventPhotosHash,
        dependencies: EventPhotosFamily._dependencies,
        allTransitiveDependencies: EventPhotosFamily._allTransitiveDependencies,
        eventId: eventId,
      );

  EventPhotosProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.eventId,
  }) : super.internal();

  final int eventId;

  @override
  Override overrideWith(
    FutureOr<List<String>> Function(EventPhotosRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: EventPhotosProvider._internal(
        (ref) => create(ref as EventPhotosRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        eventId: eventId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<String>> createElement() {
    return _EventPhotosProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is EventPhotosProvider && other.eventId == eventId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, eventId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin EventPhotosRef on AutoDisposeFutureProviderRef<List<String>> {
  /// The parameter `eventId` of this provider.
  int get eventId;
}

class _EventPhotosProviderElement
    extends AutoDisposeFutureProviderElement<List<String>>
    with EventPhotosRef {
  _EventPhotosProviderElement(super.provider);

  @override
  int get eventId => (origin as EventPhotosProvider).eventId;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
