import 'dart:developer';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/list.extension.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:hrms_tst/features/teammates/data/teammates.protocol.dart';
import 'package:hrms_tst/shared/models/teammate_filter.dart';
import 'package:hrms_tst/shared/models/user.model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../data/models/teammate.model.dart';
import '../data/models/teammate_detail.model.dart';

part 'teammates.controller.g.dart';

@riverpod
FutureOr<UserModel?> teammateById(Ref ref, int? id) async {
  if (id == null) return null;

  final teammates = await ref.read(teammatesProvider.future);

  return teammates.firstWhereOrNull((e) => e.id == id)?.user;
}

@riverpod
FutureOr<TeammateDetailModel> teammateDetail(Ref ref, int id) async {
  return (await getIt.get<TeammatesProtocol>().getTeammateDetail(
    id,
  )).ignoreFailure();
}

@riverpod
class Teammates extends _$Teammates {
  List<TeammateFilter> filters = [];

  bool get isFilterApplied => filters.any((e) => e.selectedOption != null);

  @override
  FutureOr<List<TeammateModel>> build() async {
    final lists = await Future.wait([
      getIt.get<TeammatesProtocol>().getOffices(),
      getIt.get<TeammatesProtocol>().getDepartments(),
      getIt.get<TeammatesProtocol>().getJobRoles(),
      getIt.get<TeammatesProtocol>().getDesignations(),
    ]);

    filters = [
      TeammateFilter<OfficeModel>(
        key: 'OfficeId',
        label: 'Office',
        options: ((lists[0]).ignoreFailure() as List<OfficeModel>)
            .map((e) => TeammateFilterOption(data: e, label: e.name))
            .toList(),
      ),
      TeammateFilter<DepartmentModel>(
        key: 'DepartmentId',
        label: 'Department',
        options: ((lists[1]).ignoreFailure() as List<DepartmentModel>)
            .map((e) => TeammateFilterOption(data: e, label: e.name))
            .toList(),
      ),
      TeammateFilter<JobRoleModel>(
        key: 'JobRoleId',
        label: 'Job Role',
        options: ((lists[2]).ignoreFailure() as List<JobRoleModel>)
            .map((e) => TeammateFilterOption(data: e, label: e.name))
            .toList(),
      ),
      TeammateFilter<DesignationModel>(
        key: 'DesignationId',
        label: 'Designation',
        options: ((lists[3]).ignoreFailure() as List<DesignationModel>)
            .map((e) => TeammateFilterOption(data: e, label: e.name))
            .toList(),
      ),
      TeammateFilter<WorkModeModel>(
        key: 'workMode',
        label: 'Work Mode',
        options: [
          WorkModeModel(id: 'On Site', name: 'On Site'),
          WorkModeModel(id: 'Remote', name: 'Remote'),
          WorkModeModel(id: 'Hybrid', name: 'Hybrid'),
        ].map((e) => TeammateFilterOption(data: e, label: e.name)).toList(),
      ),
    ];

    return (await getIt.get<TeammatesProtocol>().getTeammates(
      filters: filters,
    )).ignoreFailure();
  }

  void selectFilter(int filterIndex, int optionIndex) {
    filters = [
      ...filters
        ..[filterIndex] = filters[filterIndex].copyWith(
          selectedOption: filters[filterIndex].options[optionIndex].data,
        ),
    ];

    log(filters.map((e) => e.selectedOption.toString()).toString());
  }

  void clearSelectedFilter(int filterIndex) {
    filters = [
      ...filters
        ..[filterIndex] = filters[filterIndex].copyWith(selectedOption: null),
    ];
  }

  Future<void> applyFilters() async {
    state = AsyncLoading();
    final teammatesResult = (await getIt.get<TeammatesProtocol>().getTeammates(
      filters: filters,
    ));

    teammatesResult.fold(
      onSuccess: (data) {
        state = AsyncData(data);
      },
      onFailure: (message) {
        state = AsyncError(message, StackTrace.current);
      },
    );
  }

  void clearFilters() {
    filters = filters.map((e) => e.copyWith(selectedOption: null)).toList();
  }
}

@riverpod
FutureOr<List<TeammateModel>> teammatesByQuery(Ref ref, String query) async {
  return (await getIt.get<TeammatesProtocol>().getTeammatesByQuery(
    query: query,
  )).ignoreFailure();
}
