import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/features/app_update/data/app_config.protocol.dart';
import 'package:hrms_tst/features/app_update/data/models/app_config.model.dart';

class AppConfigRepository implements AppConfigProtocol {
  @override
  Future<Result<AppConfigModel>> getAppConfig() async {
    return Success(AppConfigModel(isUpdateRequired: false));
  }
}
