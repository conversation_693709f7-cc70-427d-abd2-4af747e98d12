import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:hrms_tst/features/app_update/data/app_config.protocol.dart';
import 'package:hrms_tst/features/app_update/data/models/app_config.model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'app_config.provider.g.dart';

@riverpod
class AppConfig extends _$AppConfig {
  @override
  FutureOr<AppConfigModel> build() async {
    return (await getIt.get<AppConfigProtocol>().getAppConfig())
        .ignoreFailure();
  }
}
