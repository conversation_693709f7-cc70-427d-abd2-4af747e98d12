import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:hrms_tst/features/employee/data/employee.protocol.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../data/models/attendance_tab.model.dart';
import '../data/models/document.model.dart';
import '../data/models/leave_tab.model.dart';

part 'employee.controller.g.dart';

@riverpod
class AttendanceData extends _$AttendanceData {
  @override
  FutureOr<AttendanceTabModel> build(DateTime? dateFilter) async {
    return (await getIt.get<EmployeeProtocol>().getAttendenceData(
      dateFilter: dateFilter,
    )).ignoreFailure();
  }

  Future<Result<void>> requestRegularization({
    required int id,
    required DateTime clockIn,
    required DateTime clockOut,
    required String reason,
  }) async {
    final result = await getIt.get<EmployeeProtocol>().requestRegularization(
      id: id,
      clockIn: clockIn,
      clockOut: clockOut,
      reason: reason,
    );

    if (result.isSuccess) {
      ref.invalidateSelf();
    }

    return result;
  }
}

@Riverpod(keepAlive: true)
FutureOr<WorkPolicyModel> workPolicy(
  Ref ref, {
  required int workPolicyId,
  required int timetablePolicyId,
}) async {
  return (await getIt.get<EmployeeProtocol>().getWorkPolicy(
    workPolicyId: workPolicyId,
    timetablePolicyId: timetablePolicyId,
  )).ignoreFailure();
}

@Riverpod(keepAlive: true)
FutureOr<List<LeaveRequestRecordModel>> leaveRequestRecords(
  Ref ref,
  int? leaveTypeId,
  String? status,
  DateTime? startDate,
  DateTime? endDate,
) async {
  return (await getIt.get<EmployeeProtocol>().getLeaveRequestRecord(
    leaveTypeId: leaveTypeId,
    status: status,
    startDate: startDate,
    endDate: endDate,
  )).ignoreFailure();
}

@Riverpod(keepAlive: true)
FutureOr<LeavePolicyModel> leavePolicy(
  Ref ref, {
  required int leavePolicyId,
}) async {
  return (await getIt.get<EmployeeProtocol>().getLeavePolicy(
    leavePolicyId: leavePolicyId,
  )).ignoreFailure();
}

@Riverpod(keepAlive: true)
FutureOr<List<DocumentCategoryModel>> documentCategories(Ref ref) async {
  return (await getIt.get<EmployeeProtocol>().getDocumentCategories())
      .ignoreFailure();
}

@Riverpod(keepAlive: true)
FutureOr<List<SingleDocumentModel>> documents(Ref ref, int categoryId) async {
  return (await getIt.get<EmployeeProtocol>().getDocuments(
    categoryId,
  )).ignoreFailure();
}
