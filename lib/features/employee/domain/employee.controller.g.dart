// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'employee.controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$workPolicyHash() => r'67b4828d626a39668cada831eeee2e238a52960d';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [workPolicy].
@ProviderFor(workPolicy)
const workPolicyProvider = WorkPolicyFamily();

/// See also [workPolicy].
class WorkPolicyFamily extends Family<AsyncValue<WorkPolicyModel>> {
  /// See also [workPolicy].
  const WorkPolicyFamily();

  /// See also [workPolicy].
  WorkPolicyProvider call({
    required int workPolicyId,
    required int timetablePolicyId,
  }) {
    return WorkPolicyProvider(
      workPolicyId: workPolicyId,
      timetablePolicyId: timetablePolicyId,
    );
  }

  @override
  WorkPolicyProvider getProviderOverride(
    covariant WorkPolicyProvider provider,
  ) {
    return call(
      workPolicyId: provider.workPolicyId,
      timetablePolicyId: provider.timetablePolicyId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'workPolicyProvider';
}

/// See also [workPolicy].
class WorkPolicyProvider extends FutureProvider<WorkPolicyModel> {
  /// See also [workPolicy].
  WorkPolicyProvider({
    required int workPolicyId,
    required int timetablePolicyId,
  }) : this._internal(
         (ref) => workPolicy(
           ref as WorkPolicyRef,
           workPolicyId: workPolicyId,
           timetablePolicyId: timetablePolicyId,
         ),
         from: workPolicyProvider,
         name: r'workPolicyProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$workPolicyHash,
         dependencies: WorkPolicyFamily._dependencies,
         allTransitiveDependencies: WorkPolicyFamily._allTransitiveDependencies,
         workPolicyId: workPolicyId,
         timetablePolicyId: timetablePolicyId,
       );

  WorkPolicyProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.workPolicyId,
    required this.timetablePolicyId,
  }) : super.internal();

  final int workPolicyId;
  final int timetablePolicyId;

  @override
  Override overrideWith(
    FutureOr<WorkPolicyModel> Function(WorkPolicyRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: WorkPolicyProvider._internal(
        (ref) => create(ref as WorkPolicyRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        workPolicyId: workPolicyId,
        timetablePolicyId: timetablePolicyId,
      ),
    );
  }

  @override
  FutureProviderElement<WorkPolicyModel> createElement() {
    return _WorkPolicyProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is WorkPolicyProvider &&
        other.workPolicyId == workPolicyId &&
        other.timetablePolicyId == timetablePolicyId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, workPolicyId.hashCode);
    hash = _SystemHash.combine(hash, timetablePolicyId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin WorkPolicyRef on FutureProviderRef<WorkPolicyModel> {
  /// The parameter `workPolicyId` of this provider.
  int get workPolicyId;

  /// The parameter `timetablePolicyId` of this provider.
  int get timetablePolicyId;
}

class _WorkPolicyProviderElement extends FutureProviderElement<WorkPolicyModel>
    with WorkPolicyRef {
  _WorkPolicyProviderElement(super.provider);

  @override
  int get workPolicyId => (origin as WorkPolicyProvider).workPolicyId;
  @override
  int get timetablePolicyId => (origin as WorkPolicyProvider).timetablePolicyId;
}

String _$leaveRequestRecordsHash() =>
    r'bfe7d5b1643de4c9bd2788e45d6b2d22e9b94dac';

/// See also [leaveRequestRecords].
@ProviderFor(leaveRequestRecords)
const leaveRequestRecordsProvider = LeaveRequestRecordsFamily();

/// See also [leaveRequestRecords].
class LeaveRequestRecordsFamily
    extends Family<AsyncValue<List<LeaveRequestRecordModel>>> {
  /// See also [leaveRequestRecords].
  const LeaveRequestRecordsFamily();

  /// See also [leaveRequestRecords].
  LeaveRequestRecordsProvider call(
    int? leaveTypeId,
    String? status,
    DateTime? startDate,
    DateTime? endDate,
  ) {
    return LeaveRequestRecordsProvider(leaveTypeId, status, startDate, endDate);
  }

  @override
  LeaveRequestRecordsProvider getProviderOverride(
    covariant LeaveRequestRecordsProvider provider,
  ) {
    return call(
      provider.leaveTypeId,
      provider.status,
      provider.startDate,
      provider.endDate,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'leaveRequestRecordsProvider';
}

/// See also [leaveRequestRecords].
class LeaveRequestRecordsProvider
    extends FutureProvider<List<LeaveRequestRecordModel>> {
  /// See also [leaveRequestRecords].
  LeaveRequestRecordsProvider(
    int? leaveTypeId,
    String? status,
    DateTime? startDate,
    DateTime? endDate,
  ) : this._internal(
        (ref) => leaveRequestRecords(
          ref as LeaveRequestRecordsRef,
          leaveTypeId,
          status,
          startDate,
          endDate,
        ),
        from: leaveRequestRecordsProvider,
        name: r'leaveRequestRecordsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$leaveRequestRecordsHash,
        dependencies: LeaveRequestRecordsFamily._dependencies,
        allTransitiveDependencies:
            LeaveRequestRecordsFamily._allTransitiveDependencies,
        leaveTypeId: leaveTypeId,
        status: status,
        startDate: startDate,
        endDate: endDate,
      );

  LeaveRequestRecordsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.leaveTypeId,
    required this.status,
    required this.startDate,
    required this.endDate,
  }) : super.internal();

  final int? leaveTypeId;
  final String? status;
  final DateTime? startDate;
  final DateTime? endDate;

  @override
  Override overrideWith(
    FutureOr<List<LeaveRequestRecordModel>> Function(
      LeaveRequestRecordsRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: LeaveRequestRecordsProvider._internal(
        (ref) => create(ref as LeaveRequestRecordsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        leaveTypeId: leaveTypeId,
        status: status,
        startDate: startDate,
        endDate: endDate,
      ),
    );
  }

  @override
  FutureProviderElement<List<LeaveRequestRecordModel>> createElement() {
    return _LeaveRequestRecordsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is LeaveRequestRecordsProvider &&
        other.leaveTypeId == leaveTypeId &&
        other.status == status &&
        other.startDate == startDate &&
        other.endDate == endDate;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, leaveTypeId.hashCode);
    hash = _SystemHash.combine(hash, status.hashCode);
    hash = _SystemHash.combine(hash, startDate.hashCode);
    hash = _SystemHash.combine(hash, endDate.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin LeaveRequestRecordsRef
    on FutureProviderRef<List<LeaveRequestRecordModel>> {
  /// The parameter `leaveTypeId` of this provider.
  int? get leaveTypeId;

  /// The parameter `status` of this provider.
  String? get status;

  /// The parameter `startDate` of this provider.
  DateTime? get startDate;

  /// The parameter `endDate` of this provider.
  DateTime? get endDate;
}

class _LeaveRequestRecordsProviderElement
    extends FutureProviderElement<List<LeaveRequestRecordModel>>
    with LeaveRequestRecordsRef {
  _LeaveRequestRecordsProviderElement(super.provider);

  @override
  int? get leaveTypeId => (origin as LeaveRequestRecordsProvider).leaveTypeId;
  @override
  String? get status => (origin as LeaveRequestRecordsProvider).status;
  @override
  DateTime? get startDate => (origin as LeaveRequestRecordsProvider).startDate;
  @override
  DateTime? get endDate => (origin as LeaveRequestRecordsProvider).endDate;
}

String _$leavePolicyHash() => r'7c92f2a4053e4f0436bebc2bdf8a5243ce19dfa7';

/// See also [leavePolicy].
@ProviderFor(leavePolicy)
const leavePolicyProvider = LeavePolicyFamily();

/// See also [leavePolicy].
class LeavePolicyFamily extends Family<AsyncValue<LeavePolicyModel>> {
  /// See also [leavePolicy].
  const LeavePolicyFamily();

  /// See also [leavePolicy].
  LeavePolicyProvider call({required int leavePolicyId}) {
    return LeavePolicyProvider(leavePolicyId: leavePolicyId);
  }

  @override
  LeavePolicyProvider getProviderOverride(
    covariant LeavePolicyProvider provider,
  ) {
    return call(leavePolicyId: provider.leavePolicyId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'leavePolicyProvider';
}

/// See also [leavePolicy].
class LeavePolicyProvider extends FutureProvider<LeavePolicyModel> {
  /// See also [leavePolicy].
  LeavePolicyProvider({required int leavePolicyId})
    : this._internal(
        (ref) =>
            leavePolicy(ref as LeavePolicyRef, leavePolicyId: leavePolicyId),
        from: leavePolicyProvider,
        name: r'leavePolicyProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$leavePolicyHash,
        dependencies: LeavePolicyFamily._dependencies,
        allTransitiveDependencies: LeavePolicyFamily._allTransitiveDependencies,
        leavePolicyId: leavePolicyId,
      );

  LeavePolicyProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.leavePolicyId,
  }) : super.internal();

  final int leavePolicyId;

  @override
  Override overrideWith(
    FutureOr<LeavePolicyModel> Function(LeavePolicyRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: LeavePolicyProvider._internal(
        (ref) => create(ref as LeavePolicyRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        leavePolicyId: leavePolicyId,
      ),
    );
  }

  @override
  FutureProviderElement<LeavePolicyModel> createElement() {
    return _LeavePolicyProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is LeavePolicyProvider && other.leavePolicyId == leavePolicyId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, leavePolicyId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin LeavePolicyRef on FutureProviderRef<LeavePolicyModel> {
  /// The parameter `leavePolicyId` of this provider.
  int get leavePolicyId;
}

class _LeavePolicyProviderElement
    extends FutureProviderElement<LeavePolicyModel>
    with LeavePolicyRef {
  _LeavePolicyProviderElement(super.provider);

  @override
  int get leavePolicyId => (origin as LeavePolicyProvider).leavePolicyId;
}

String _$documentCategoriesHash() =>
    r'044a18815c9acd41c31c5138dfa91cc3ee9eaa9d';

/// See also [documentCategories].
@ProviderFor(documentCategories)
final documentCategoriesProvider =
    FutureProvider<List<DocumentCategoryModel>>.internal(
      documentCategories,
      name: r'documentCategoriesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$documentCategoriesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DocumentCategoriesRef = FutureProviderRef<List<DocumentCategoryModel>>;
String _$documentsHash() => r'367979c134eba11b863b9cb9d8f8d50cc967205a';

/// See also [documents].
@ProviderFor(documents)
const documentsProvider = DocumentsFamily();

/// See also [documents].
class DocumentsFamily extends Family<AsyncValue<List<SingleDocumentModel>>> {
  /// See also [documents].
  const DocumentsFamily();

  /// See also [documents].
  DocumentsProvider call(int categoryId) {
    return DocumentsProvider(categoryId);
  }

  @override
  DocumentsProvider getProviderOverride(covariant DocumentsProvider provider) {
    return call(provider.categoryId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'documentsProvider';
}

/// See also [documents].
class DocumentsProvider extends FutureProvider<List<SingleDocumentModel>> {
  /// See also [documents].
  DocumentsProvider(int categoryId)
    : this._internal(
        (ref) => documents(ref as DocumentsRef, categoryId),
        from: documentsProvider,
        name: r'documentsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$documentsHash,
        dependencies: DocumentsFamily._dependencies,
        allTransitiveDependencies: DocumentsFamily._allTransitiveDependencies,
        categoryId: categoryId,
      );

  DocumentsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryId,
  }) : super.internal();

  final int categoryId;

  @override
  Override overrideWith(
    FutureOr<List<SingleDocumentModel>> Function(DocumentsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: DocumentsProvider._internal(
        (ref) => create(ref as DocumentsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryId: categoryId,
      ),
    );
  }

  @override
  FutureProviderElement<List<SingleDocumentModel>> createElement() {
    return _DocumentsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is DocumentsProvider && other.categoryId == categoryId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin DocumentsRef on FutureProviderRef<List<SingleDocumentModel>> {
  /// The parameter `categoryId` of this provider.
  int get categoryId;
}

class _DocumentsProviderElement
    extends FutureProviderElement<List<SingleDocumentModel>>
    with DocumentsRef {
  _DocumentsProviderElement(super.provider);

  @override
  int get categoryId => (origin as DocumentsProvider).categoryId;
}

String _$attendanceDataHash() => r'a8594a9126a3f1fcdbb84915f2c00d979746be2b';

abstract class _$AttendanceData
    extends BuildlessAutoDisposeAsyncNotifier<AttendanceTabModel> {
  late final DateTime? dateFilter;

  FutureOr<AttendanceTabModel> build(DateTime? dateFilter);
}

/// See also [AttendanceData].
@ProviderFor(AttendanceData)
const attendanceDataProvider = AttendanceDataFamily();

/// See also [AttendanceData].
class AttendanceDataFamily extends Family<AsyncValue<AttendanceTabModel>> {
  /// See also [AttendanceData].
  const AttendanceDataFamily();

  /// See also [AttendanceData].
  AttendanceDataProvider call(DateTime? dateFilter) {
    return AttendanceDataProvider(dateFilter);
  }

  @override
  AttendanceDataProvider getProviderOverride(
    covariant AttendanceDataProvider provider,
  ) {
    return call(provider.dateFilter);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'attendanceDataProvider';
}

/// See also [AttendanceData].
class AttendanceDataProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          AttendanceData,
          AttendanceTabModel
        > {
  /// See also [AttendanceData].
  AttendanceDataProvider(DateTime? dateFilter)
    : this._internal(
        () => AttendanceData()..dateFilter = dateFilter,
        from: attendanceDataProvider,
        name: r'attendanceDataProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$attendanceDataHash,
        dependencies: AttendanceDataFamily._dependencies,
        allTransitiveDependencies:
            AttendanceDataFamily._allTransitiveDependencies,
        dateFilter: dateFilter,
      );

  AttendanceDataProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.dateFilter,
  }) : super.internal();

  final DateTime? dateFilter;

  @override
  FutureOr<AttendanceTabModel> runNotifierBuild(
    covariant AttendanceData notifier,
  ) {
    return notifier.build(dateFilter);
  }

  @override
  Override overrideWith(AttendanceData Function() create) {
    return ProviderOverride(
      origin: this,
      override: AttendanceDataProvider._internal(
        () => create()..dateFilter = dateFilter,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        dateFilter: dateFilter,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<AttendanceData, AttendanceTabModel>
  createElement() {
    return _AttendanceDataProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AttendanceDataProvider && other.dateFilter == dateFilter;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, dateFilter.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AttendanceDataRef
    on AutoDisposeAsyncNotifierProviderRef<AttendanceTabModel> {
  /// The parameter `dateFilter` of this provider.
  DateTime? get dateFilter;
}

class _AttendanceDataProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          AttendanceData,
          AttendanceTabModel
        >
    with AttendanceDataRef {
  _AttendanceDataProviderElement(super.provider);

  @override
  DateTime? get dateFilter => (origin as AttendanceDataProvider).dateFilter;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
