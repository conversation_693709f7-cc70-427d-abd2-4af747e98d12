import 'dart:io';

import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:hrms_tst/features/employee/data/employee.protocol.dart';
import 'package:hrms_tst/features/teammates/domain/team_progress.controller.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'progress_record.controller.g.dart';

@riverpod
class AddProgressRecord extends _$AddProgressRecord {
  final _repository = getIt.get<EmployeeProtocol>();

  @override
  void build() {
    return;
  }

  Future<Result<void>> addBookProgressRecord({
    required String name,
    required DateTime completedAt,
    required String notes,
    required String docLink,
    required int pages,
  }) async {
    return await _repository.addBookProgressRecord(
      name: name,
      completedAt: completedAt,
      notes: notes,
      docLink: docLink,
      pages: pages,
    );
  }

  Future<Result<void>> addCourseProgressRecord({
    required String name,
    required DateTime completedAt,
    required String notes,
    required String courseLink,
    required String docLink,
  }) async {
    return await _repository.addCourseProgressRecord(
      name: name,
      completedAt: completedAt,
      notes: notes,
      courseLink: courseLink,
      docLink: docLink,
    );
  }

  Future<Result<void>> addGDProgressRecord({
    required String name,
    required String topic,
    required DateTime completedAt,
    required String docLink,
    required String notes,
  }) async {
    return await _repository.addGDProgressRecord(
      name: name,
      topic: topic,
      completedAt: completedAt,
      notes: notes,
      docLink: docLink,
    );
  }

  Future<Result<void>> addSessionProgressRecord({
    required String name,
    required String topic,
    required DateTime completedAt,
    required String docLink,
    required String notes,
  }) async {
    return await _repository.addSessionProgressRecord(
      name: name,
      topic: topic,
      completedAt: completedAt,
      notes: notes,
      docLink: docLink,
    );
  }

  Future<Result<void>> addEventProgressRecord({
    required String name,
    required DateTime startDate,
    required DateTime endDate,
    required String docLink,
    required List<File> images,
    required String notes,
  }) async {
    return await _repository.addEventProgressRecord(
      name: name,
      startDate: startDate,
      endDate: endDate,
      docLink: docLink,
      images: images,
      notes: notes,
    );
  }

  Future<Result<void>> editBookProgressRecord({
    required int id,
    required String name,
    required DateTime completedAt,
    required String notes,
    required String docLink,
    required int pages,
  }) async {
    return await _repository.editBookProgressRecord(
      id: id,
      name: name,
      completedAt: completedAt,
      notes: notes,
      docLink: docLink,
      pages: pages,
    );
  }

  Future<Result<void>> editCourseProgressRecord({
    required int id,
    required String name,
    required DateTime completedAt,
    required String notes,
    required String courseLink,
    required String docLink,
  }) async {
    return await _repository.editCourseProgressRecord(
      id: id,
      name: name,
      completedAt: completedAt,
      notes: notes,
      courseLink: courseLink,
      docLink: docLink,
    );
  }

  Future<Result<void>> editGDProgressRecord({
    required int id,
    required String name,
    required String topic,
    required DateTime completedAt,
    required String docLink,
    required String notes,
  }) async {
    return await _repository.editGDProgressRecord(
      id: id,
      name: name,
      topic: topic,
      completedAt: completedAt,
      notes: notes,
      docLink: docLink,
    );
  }

  Future<Result<void>> editSessionProgressRecord({
    required int id,
    required String name,
    required String topic,
    required DateTime completedAt,
    required String docLink,
    required String notes,
  }) async {
    return await _repository.editSessionProgressRecord(
      id: id,
      name: name,
      topic: topic,
      completedAt: completedAt,
      notes: notes,
      docLink: docLink,
    );
  }

  Future<Result<void>> editEventProgressRecord({
    required int id,
    required String name,
    required DateTime startDate,
    required DateTime endDate,
    required String docLink,
    required String notes,
  }) async {
    return await _repository.editEventProgressRecord(
      id: id,
      name: name,
      startDate: startDate,
      endDate: endDate,
      docLink: docLink,
      notes: notes,
    );
  }

  Future<Result<void>> deleteBookProgressRecord({required int id}) async {
    final result = await _repository.deleteBookProgressRecord(id: id);
    if (result.isSuccess) {
      ref.invalidate(bookProgressProvider);
    }
    return result;
  }

  Future<Result<void>> deleteCourseProgressRecord({required int id}) async {
    final result = await _repository.deleteCourseProgressRecord(id: id);
    if (result.isSuccess) {
      ref.invalidate(courseProgressProvider);
    }
    return result;
  }

  Future<Result<void>> deleteEventProgressRecord({required int id}) async {
    final result = await _repository.deleteEventProgressRecord(id: id);
    if (result.isSuccess) {
      ref.invalidate(eventProgressProvider);
    }
    return result;
  }

  Future<Result<void>> deleteGDProgressRecord({required int id}) async {
    final result = await _repository.deleteGDProgressRecord(id: id);
    if (result.isSuccess) {
      ref.invalidate(gdProgressProvider);
    }
    return result;
  }

  Future<Result<void>> deleteSessionProgressRecord({required int id}) async {
    final result = await _repository.deleteSessionProgressRecord(id: id);
    if (result.isSuccess) {
      ref.invalidate(sessionProgressProvider);
    }
    return result;
  }
}
