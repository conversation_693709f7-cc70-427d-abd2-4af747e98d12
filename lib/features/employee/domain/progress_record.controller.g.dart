// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'progress_record.controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$addProgressRecordHash() => r'bedb4f657825b11a64aff5843b64b0e2b739984f';

/// See also [AddProgressRecord].
@ProviderFor(AddProgressRecord)
final addProgressRecordProvider =
    AutoDisposeNotifierProvider<AddProgressRecord, void>.internal(
      AddProgressRecord.new,
      name: r'addProgressRecordProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$addProgressRecordHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AddProgressRecord = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
