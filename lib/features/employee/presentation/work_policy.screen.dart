import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/employee/domain/employee.controller.dart';
import 'package:hrms_tst/features/employee/presentation/widgets/policy_widgets/timetable_display_view.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';
import 'package:hrms_tst/shared/widgets/screen_back_button.dart';

import 'widgets/policy_widgets/work_time_policy_view.dart';

class WorkPolicyScreen extends HookConsumerWidget {
  const WorkPolicyScreen({
    super.key,
    required this.workPolicyId,
    required this.timetablePolicyId,
  });

  static const String name = 'Work Policy';
  static const String path = 'work-policy';

  static GoRoute route({List<RouteBase> routes = const []}) => GoRoute(
    path: path,
    name: name,
    routes: routes,
    pageBuilder: (context, state) => NoTransitionPage(
      child: WorkPolicyScreen(
        workPolicyId: int.parse(state.uri.queryParameters['workPolicyId']!),
        timetablePolicyId: int.parse(state.uri.queryParameters['timetableId']!),
      ),
    ),
  );

  final int workPolicyId;
  final int timetablePolicyId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final policy = ref.watch(
      workPolicyProvider(
        workPolicyId: workPolicyId,
        timetablePolicyId: timetablePolicyId,
      ),
    );

    final selectedTab = useState(WorkPolicyOption.timePolicy);
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            children: [
              ScreenBackButton(),
              Space.y(16),

              Expanded(
                child: CardContainer(
                  elevation: 0.5,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: ListView(
                      children: [
                        Text(
                          'Office Hours & Schedule',
                          style: context.textStyles.bodyLarge?.copyWith(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Space.y(20),
                        Row(
                          spacing: 10,
                          children: WorkPolicyOption.values.map((e) {
                            final isSelected = e == selectedTab.value;
                            return GestureDetector(
                              onTap: () {
                                selectedTab.value = e;
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: isSelected
                                      ? context.colors.tertiary
                                      : null,
                                  border: Border.all(
                                    color: isSelected
                                        ? Colors.transparent
                                        : context.colors.outline,
                                    width: 1,
                                  ),
                                ),
                                padding: EdgeInsets.symmetric(
                                  horizontal: 10,
                                  vertical: 6,
                                ),
                                child: Text(
                                  e.label,
                                  style: context.textStyles.titleMedium
                                      ?.copyWith(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                        color: isSelected
                                            ? context.colors.surface
                                            : context.colors.secondary,
                                      ),
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                        switch (policy) {
                          AsyncData(:final value) => switch (selectedTab
                              .value) {
                            WorkPolicyOption.timePolicy => WorkTimePolicyView(
                              workPolicy: value.workShiftPolicy,
                            ),
                            WorkPolicyOption.timetable => TimeTableDisplayView(
                              timetablePolicy: value.timetablePolicy,
                            ),
                          },
                          AsyncError(:final error) => Center(
                            child: Text(error.toString()),
                          ),
                          _ => Padding(
                            padding: const EdgeInsets.all(24.0),
                            child: Center(
                              child: CircularProgressIndicator.adaptive(),
                            ),
                          ),
                        },
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

enum WorkPolicyOption {
  timePolicy('Work Time Policy'),
  timetable('General Timetable');

  final String label;
  const WorkPolicyOption(this.label);
}
