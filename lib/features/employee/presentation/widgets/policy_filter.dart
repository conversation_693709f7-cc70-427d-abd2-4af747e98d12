import 'package:flutter/material.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';

enum PolicyFilter {
  policy('Policy'),
  requestHistory('Request History');

  final String label;
  const PolicyFilter(this.label);
}

class PolicyFilterOptions extends StatelessWidget {
  const PolicyFilterOptions({
    super.key,
    required this.selectedTab,
    required this.onTabSelected,
  });

  final PolicyFilter selectedTab;
  final Function(PolicyFilter tab) onTabSelected;

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 8,
      children: PolicyFilter.values.map((e) {
        final isSelected = e == selectedTab;
        return Expanded(
          child: GestureDetector(
            onTap: () {
              onTabSelected(e);
            },
            child: Container(
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: isSelected
                    ? context.colors.tertiary
                    : context.colors.tertiary.withValues(alpha: 0.1),
              ),
              padding: EdgeInsets.symmetric(horizontal: 10, vertical: 13),
              child: Text(
                e.label,
                style: context.textStyles.titleMedium?.copyWith(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: isSelected
                      ? context.colors.surface
                      : context.colors.secondary,
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
