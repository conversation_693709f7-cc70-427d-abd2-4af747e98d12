import 'dart:developer';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/features/employee/data/models/document.model.dart';
import 'package:hrms_tst/features/employee/domain/employee.controller.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';
import 'package:hrms_tst/shared/widgets/loading_placeholder.dart';
import 'package:hrms_tst/shared/widgets/no_data_view.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';

class DocumentCategoryTile extends ConsumerWidget {
  const DocumentCategoryTile({super.key, required this.category});

  final DocumentCategoryModel category;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: CardContainer(
        elevation: 1,
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            spacing: 12,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${category.name}',
                    style: context.textStyles.titleMedium,
                  ),
                  Text(
                    '${category.count} documents',
                    style: context.textStyles.titleSmall,
                  ),
                ],
              ),

              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: context.colors.outline,
                ),
                child: Column(
                  children: switch (ref.watch(documentsProvider(category.id))) {
                    AsyncData(:final value) =>
                      value.isEmpty
                          ? [
                              Center(
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: NoDataView(
                                    message: 'No Documents',
                                    iconColor: context.colors.surface,
                                    messageTextColor: context.colors.onSurface,
                                  ),
                                ),
                              ),
                            ]
                          : value
                                .map((doc) => SingleDocumentTile(doc: doc))
                                .toList(),
                    AsyncError(:final error) => [Text(error.toString())],
                    _ => [
                      ListTile(
                        minVerticalPadding: 12,
                        leading: LoadingPlaceholder(
                          height: 24,
                          width: 24,
                          color: context.colors.surface,
                        ),
                        title: Padding(
                          padding: const EdgeInsets.only(bottom: 6.0),
                          child: LoadingPlaceholder(
                            height: 20,
                            color: context.colors.surface,
                          ),
                        ),
                        subtitle: LoadingPlaceholder(
                          height: 16,
                          color: context.colors.surface,
                        ),
                        trailing: LoadingPlaceholder(
                          height: 24,
                          width: 24,
                          color: context.colors.surface,
                        ),
                      ),
                    ],
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class SingleDocumentTile extends HookConsumerWidget {
  const SingleDocumentTile({super.key, required this.doc});

  final SingleDocumentModel doc;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ListTile(
      leading: SVGImage(Assets.svgs.documentVerified),
      title: Text(doc.name ?? ''),
      subtitle: Text(doc.data?.docNumber?.toString() ?? ''),
      trailing: IconButton(
        onPressed: () async {
          showDialog(
            context: context,
            builder: (context) => Dialog.fullscreen(
              backgroundColor: context.colors.outline,
              child: Column(
                children: [
                  AppBar(
                    title: Text('${doc.name}'),
                    actions: [
                      IconButton(
                        onPressed: () {
                          context.pop();
                        },
                        icon: Icon(
                          Icons.close,
                          color: context.colors.onSurface,
                        ),
                      ),
                    ],
                  ),
                  Expanded(child: ImageDownloadViewer(url: doc.data?.docLink)),
                ],
              ),
            ),
          );
        },
        icon: Icon(Icons.visibility),
      ),
    );
  }
}

class ImageDownloadViewer extends HookConsumerWidget {
  const ImageDownloadViewer({super.key, required this.url});

  final String? url;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLoading = useState<bool>(true);
    final imageData = useState<Uint8List?>(null);

    Future<void> loadImage() async {
      isLoading.value = true;
      try {
        final data = await Dio().get(
          url ?? '',
          options: Options(responseType: ResponseType.bytes),
        );
        imageData.value = Uint8List.fromList(data.data);
        isLoading.value = false;
      } catch (e) {
        log(e.toString());
        isLoading.value = false;
      }
    }

    useEffect(() {
      loadImage();
      return null;
    }, []);

    return isLoading.value
        ? Center(child: CircularProgressIndicator())
        : imageData.value != null
        ? Image.memory(imageData.value!, fit: BoxFit.fitWidth)
        : Text('No Image');
  }
}
