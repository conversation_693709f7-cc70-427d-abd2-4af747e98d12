import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/extensions/string.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/employee/data/models/attendance_tab.model.dart';
import 'package:hrms_tst/features/employee/presentation/widgets/regularization_dialog.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';
import 'package:intl/intl.dart';

class AttendanceHistoryTile extends ConsumerWidget {
  const AttendanceHistoryTile({super.key, required this.attendance});

  final AttendanceHistoryModel attendance;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: context.colors.outlineVariant),
        ),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      DateFormat(
                        'dd MMM',
                      ).format(attendance.createdAt!.toLocal()),
                      style: context.textStyles.bodyMedium?.copyWith(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      DateFormat(
                        'EEEE',
                      ).format(attendance.createdAt!.toLocal()),
                      style: context.textStyles.bodyMedium?.copyWith(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
                Spacer(),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      attendance.status.capitalize(),
                      style: context.textStyles.bodyMedium?.copyWith(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: switch (attendance.status) {
                          'present' => context.colors.primary,
                          'absent' || 'leave' => context.colors.error,
                          'wfh' => Colors.blue,
                          'holiday' => Color(0xFFFFA500),
                          _ => context.colors.secondary,
                        },
                      ),
                    ),
                    switch (attendance.status) {
                      'present' || 'wfh' => Text(
                        (attendance.hoursWorked ?? 0.0).toStringAsFixed(2),
                        style: context.textStyles.bodyMedium?.copyWith(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      'holiday' => Text(
                        attendance.holiday!['name'],
                        style: context.textStyles.bodyMedium?.copyWith(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      'absent' => showRegularization(context),
                      _ => Space.y(14),
                    },
                  ],
                ),
              ],
            ),
            if (['present', 'wfh'].contains(attendance.status) ||
                isRegularizationAppliedOrApproved)
              buildDateTimeShow(context, ref),
            if (['present', 'wfh'].contains(attendance.status))
              Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: Row(
                  children: [
                    SVGImage(Assets.svgs.locationIcon, height: 16, width: 16),
                    Space.x(8),
                    Text(
                      '${attendance.lat?.toStringAsFixed(2) ?? 'N/A'} | ${attendance.long?.toStringAsFixed(2) ?? 'N/A'}',
                      style: context.textStyles.titleSmall?.copyWith(
                        fontSize: 15,
                        fontWeight: FontWeight.w400,
                        color: context.colors.onSurface,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget buildDateTimeShow(BuildContext context, WidgetRef ref) {
    DateTime? clockIn;
    DateTime? clockOut;

    if (attendance.regularization == 'rejected') {
      return SizedBox.shrink();
    }

    if (attendance.regularizationTimes != null) {
      clockIn = attendance.regularizationTimes!.clockIn;
      clockOut = attendance.regularizationTimes!.clockOut;
    } else {
      clockIn = attendance.clockIn;
      clockOut = attendance.clockOut;
    }

    return Container(
      margin: EdgeInsets.only(top: 16),
      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: context.colors.outline,
      ),
      child: Row(
        children: [
          SVGImage(Assets.svgs.clockIcon, height: 16, width: 16),
          Space.x(8),
          Text(
            '${DateFormat('hh:mm a').format(clockIn!.toLocal())} - ${clockOut == null ? 'Now' : DateFormat('hh:mm a').format(clockOut.toLocal())}',
            style: context.textStyles.titleSmall?.copyWith(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: context.colors.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  Widget showRegularization(BuildContext context) {
    if (isRegularizationAppliedOrApproved) {
      return Text(
        'Regularization: ${attendance.regularization!.capitalize()}',
        style: context.textStyles.bodyMedium?.copyWith(
          fontSize: 12,
          fontWeight: FontWeight.w400,
        ),
      );
    }
    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: OutlinedButton(
        onPressed: () {
          showDialog(
            context: context,
            builder: (context) => RegularizationDialog(attendance: attendance),
          );
        },
        style: OutlinedButton.styleFrom(
          shape: StadiumBorder(),
          padding: EdgeInsets.symmetric(vertical: 6, horizontal: 16),
        ),
        child: Text('Regularize'),
      ),
    );
  }

  bool get isRegularizationAppliedOrApproved {
    return attendance.regularization != null &&
        attendance.regularizationTimes != null;
  }
}
