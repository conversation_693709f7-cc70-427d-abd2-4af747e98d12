import 'package:flutter/material.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';

enum EmployeeTabs {
  attendance('Attendance'),
  leave('Leave'),
  documents('Documents'),
  progress('Progress');

  final String label;
  const EmployeeTabs(this.label);
}

class EmployeeTabsOptions extends StatelessWidget {
  const EmployeeTabsOptions({
    super.key,
    required this.selectedTab,
    required this.onTabSelected,
  });

  final EmployeeTabs selectedTab;
  final Function(EmployeeTabs tab) onTabSelected;

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 8,
      children: EmployeeTabs.values.map((e) {
        final isSelected = e == selectedTab;
        return Expanded(
          child: GestureDetector(
            onTap: () {
              onTabSelected(e);
            },
            child: Container(
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: isSelected
                    ? context.colors.tertiary
                    : context.colors.tertiary.withValues(alpha: 0.1),
              ),
              padding: EdgeInsets.symmetric(vertical: 13),
              child: Text(
                e.label,
                style: context.textStyles.titleMedium?.copyWith(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: isSelected
                      ? context.colors.surface
                      : context.colors.secondary,
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
