import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';
import 'package:intl/intl.dart';

class AttendanceFilter extends ConsumerWidget {
  const AttendanceFilter({
    super.key,
    required this.selectedDate,
    required this.onFilterSelected,
  });

  final DateTime selectedDate;
  final Function(DateTime selectedDate) onFilterSelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      onTap: () {
        showModalBottomSheet(
          useRootNavigator: true,
          context: context,
          clipBehavior: Clip.antiAlias,
          backgroundColor: context.colors.surface,
          scrollControlDisabledMaxHeightRatio: 0.6,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(14),
          ),
          builder: (context) {
            return AttendanceFilterSheet(
              selectedDate: selectedDate,
              onFilterSelected: onFilterSelected,
            );
          },
        );
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 4, horizontal: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: context.colors.outline,
        ),
        child: Row(
          children: [
            Text(
              DateFormat('MMMM').format(selectedDate),
              style: context.textStyles.titleMedium?.copyWith(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: context.colors.secondary,
              ),
            ),
            Space.x(8),
            Transform.rotate(
              angle: pi,
              child: SVGImage(
                Assets.svgs.dropdownArrowIcon,
                height: 14,
                width: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AttendanceFilterSheet extends HookConsumerWidget {
  const AttendanceFilterSheet({
    super.key,
    required this.selectedDate,
    required this.onFilterSelected,
  });

  final DateTime selectedDate;
  final Function(DateTime selectedDate) onFilterSelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedYear = useState(selectedDate.year);
    final selectedMonth = useState(selectedDate.month);
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Select Filter', style: context.textStyles.titleMedium),
              IconButton.filled(
                onPressed: () {
                  onFilterSelected(
                    DateTime.utc(selectedYear.value, selectedMonth.value),
                  );
                },
                icon: Icon(Icons.check),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                onPressed: () {
                  selectedYear.value = selectedYear.value - 1;
                },
                icon: Icon(Icons.arrow_back),
              ),
              Text('${selectedYear.value}'),
              IconButton(
                onPressed: () {
                  selectedYear.value = selectedYear.value + 1;
                },
                icon: Icon(Icons.arrow_forward),
              ),
            ],
          ),
          Space.y(8),
          Divider(height: 1),
          Space.y(16),
          GridView.count(
            shrinkWrap: true,
            crossAxisCount: 3,
            childAspectRatio: 3,
            mainAxisSpacing: 8,
            crossAxisSpacing: 8,
            children: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((e) {
              final isSelected = e == selectedMonth.value;
              return GestureDetector(
                onTap: () {
                  selectedMonth.value = e;
                },
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 4, horizontal: 16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: isSelected
                        ? context.colors.primary
                        : context.colors.outline,
                  ),
                  child: Center(
                    child: Text(
                      DateFormat(
                        'MMMM',
                      ).format(DateTime.utc(selectedYear.value, e)),
                      style: context.textStyles.titleMedium?.copyWith(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isSelected
                            ? context.colors.onPrimary
                            : context.colors.secondary,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
