import 'dart:math';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/extensions/number.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/employee/presentation/leave_policy.screen.dart';
import 'package:hrms_tst/features/home/<USER>/home.controller.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';

class LeaveBalanceSection extends ConsumerWidget {
  const LeaveBalanceSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final balance = ref.watch(leaveBalanceProvider);
    return switch (balance) {
      AsyncData(:final value) => CardContainer(
        elevation: 1,
        child: Padding(
          padding: const EdgeInsets.all(14.0),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: Row(
                  children: [
                    Text(
                      'Leave balance',
                      style: context.textStyles.titleMedium?.copyWith(
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        color: context.colors.onSurface,
                      ),
                    ),
                    Spacer(),
                    GestureDetector(
                      onTap: () {
                        context.pushNamed(
                          LeavePolicyScreen.name,
                          pathParameters: {
                            'id': value.leavePolicy.id.toString(),
                          },
                        );
                      },
                      child: Text(
                        'Policy',
                        style: context.textStyles.titleMedium?.copyWith(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: context.colors.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Space.y(14),
              Builder(
                builder: (context) {
                  final sickLeave = value.availableLeaveTypes[0];
                  final paidLeave = value.availableLeaveTypes[2];
                  final unpaidLeave = value.availableLeaveTypes[1];
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: _leaveDisplay(
                              context: context,
                              direction: Axis.vertical,
                              label: sickLeave.name,
                              data: sickLeave.balance.showFractionIfAvailable,
                              progressFactor:
                                  sickLeave.balance / sickLeave.annualQuota,
                              color: Colors.red,
                              annualQuota: sickLeave.annualQuota.toDouble(),
                              consumed: sickLeave.consumedLeaves.toDouble(),
                            ),
                          ),
                          Space.x(10),
                          Expanded(
                            child: _leaveDisplay(
                              context: context,
                              direction: Axis.vertical,
                              label: paidLeave.name,
                              data: paidLeave.balance.showFractionIfAvailable,
                              progressFactor:
                                  paidLeave.balance / paidLeave.annualQuota,
                              color: Colors.green,
                              annualQuota: paidLeave.annualQuota.toDouble(),
                              consumed: paidLeave.consumedLeaves.toDouble(),
                            ),
                          ),
                        ],
                      ),
                      Space.y(10),
                      _leaveDisplay(
                        context: context,
                        direction: Axis.horizontal,
                        label: unpaidLeave.name,
                        data: unpaidLeave.balance.showFractionIfAvailable,
                        progressFactor:
                            unpaidLeave.balance / unpaidLeave.annualQuota,
                        color: Colors.blue,
                        annualQuota: 365,
                        consumed: unpaidLeave.consumedLeaves.toDouble(),
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
      AsyncError(:final error) => Center(child: Text(error.toString())),
      _ => Center(child: CircularProgressIndicator.adaptive()),
    };
  }

  Widget _leaveDisplay({
    required BuildContext context,
    required Axis direction,
    required String label,
    required String data,
    required double progressFactor,
    required Color color,
    required double annualQuota,
    required double consumed,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(18),
        border: Border.all(width: 1, color: context.colors.outline),
      ),
      padding: EdgeInsets.all(12),
      child: Flex(
        direction: direction,
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Column(
            children: [
              Text(
                label,
                style: context.textStyles.titleMedium?.copyWith(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
              ),
              Space.y(12),
              Stack(
                children: [
                  SizedBox(
                    width: 90,
                    height: 90,
                    child: Transform.rotate(
                      angle: pi,
                      child: CircularProgressIndicator(
                        value: progressFactor,
                        strokeWidth: 8,
                        backgroundColor: context.colors.outline,
                        valueColor: AlwaysStoppedAnimation<Color>(color),
                        strokeCap: StrokeCap.round,
                      ),
                    ),
                  ),
                  Positioned.fill(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          data == '365' ? '∞' : data.padLeft(2, '0'),
                          style: context.textStyles.bodySmall?.copyWith(
                            fontSize: 24,
                            fontWeight: FontWeight.w700,
                            color: context.colors.onSurface,
                          ),
                        ),
                        Text(
                          'Days',
                          style: context.textStyles.bodySmall?.copyWith(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: context.colors.secondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          Space.y(16),
          Column(
            children: [
              Text(
                'YEARLY QUOTA',
                style: context.textStyles.bodySmall?.copyWith(
                  fontSize: 9,
                  fontWeight: FontWeight.w400,
                  color: context.colors.secondary,
                ),
              ),
              Text(
                annualQuota == 365 ? '∞ days' : '${annualQuota.toInt()} days',
                style: context.textStyles.bodySmall?.copyWith(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: context.colors.secondary,
                ),
              ),
              Space.y(14),
              Text(
                'CONSUMED',
                style: context.textStyles.bodySmall?.copyWith(
                  fontSize: 9,
                  fontWeight: FontWeight.w400,
                  color: context.colors.secondary,
                ),
              ),
              Text(
                '${consumed.toInt()} days',
                style: context.textStyles.bodySmall?.copyWith(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: context.colors.secondary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
