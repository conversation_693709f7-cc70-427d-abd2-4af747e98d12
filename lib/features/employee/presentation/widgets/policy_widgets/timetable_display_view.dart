import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/employee/data/models/attendance_tab.model.dart';
import 'package:hrms_tst/shared/widgets/app_html_view.dart';

class TimeTableDisplayView extends HookConsumerWidget {
  const TimeTableDisplayView({super.key, required this.timetablePolicy});

  final TimetablePolicyModel? timetablePolicy;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedOption = useState(TimetableOption.timetable);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Space.y(8),
        Padding(
          padding: EdgeInsets.symmetric(vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                timetablePolicy?.name ?? '',
                style: context.textStyles.titleMedium?.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: context.colors.onSurface,
                ),
              ),
              Space.y(8),
              Row(
                spacing: 24,
                children: [
                  _timeDetail(
                    context: context,
                    label: 'First Day',
                    data: 'Monday',
                  ),
                  _timeDetail(
                    context: context,
                    label: 'Last Day',
                    data: 'Saturday',
                  ),
                ],
              ),
            ],
          ),
        ),
        Divider(color: context.colors.outlineVariant, height: 1),
        Space.y(10),
        Row(
          spacing: 10,
          children: TimetableOption.values.map((e) {
            final isSelected = e == selectedOption.value;
            return GestureDetector(
              onTap: () {
                selectedOption.value = e;
              },
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: isSelected ? context.colors.tertiary : null,
                  border: Border.all(
                    color: isSelected
                        ? Colors.transparent
                        : context.colors.outline,
                    width: 1,
                  ),
                ),
                padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                child: Text(
                  e.label,
                  style: context.textStyles.titleMedium?.copyWith(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: isSelected
                        ? context.colors.surface
                        : context.colors.secondary,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
        ...switch (selectedOption.value) {
          TimetableOption.timetable =>
            [
              timetablePolicy?.mon ?? 0,
              timetablePolicy?.tue ?? 0,
              timetablePolicy?.wed ?? 0,
              timetablePolicy?.thu ?? 0,
              timetablePolicy?.fri ?? 0,
              timetablePolicy?.sat ?? 0,
              timetablePolicy?.sun ?? 0,
            ].indexed.map(
              (e) => ListTile(
                contentPadding: EdgeInsets.symmetric(horizontal: 4),
                title: Text(switch (e.$1) {
                  0 => 'Monday',
                  1 => 'Tuesday',
                  2 => 'Wednesday',
                  3 => 'Thursday',
                  4 => 'Friday',
                  5 => 'Saturday',
                  6 => 'Sunday',
                  _ => '',
                }),
                trailing: Text(
                  switch (e.$2) {
                    1 => 'Working day',
                    0.5 => 'Alternative day',
                    _ => 'Week-Off day',
                  },
                  style: context.textStyles.bodySmall?.copyWith(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ),
          TimetableOption.policy => [
            Space.y(16),
            HtmlView(htmlContent: timetablePolicy?.policy ?? ''),
          ],
        },
      ],
    );
  }

  Widget _timeDetail({
    required BuildContext context,
    required String label,
    required String data,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: context.textStyles.titleMedium?.copyWith(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: context.colors.secondary,
          ),
        ),
        Text(
          data,
          style: context.textStyles.bodyMedium?.copyWith(
            fontSize: 16,
            fontWeight: FontWeight.w400,
            color: context.colors.onSurface,
          ),
        ),
      ],
    );
  }
}

enum TimetableOption {
  timetable('Timetable'),
  policy('Policy');

  final String label;
  const TimetableOption(this.label);
}
