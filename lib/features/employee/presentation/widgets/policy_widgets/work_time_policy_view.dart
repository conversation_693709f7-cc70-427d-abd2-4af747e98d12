import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/shared/widgets/app_html_view.dart';

import '../../../data/models/attendance_tab.model.dart';

class WorkTimePolicyView extends ConsumerWidget {
  const WorkTimePolicyView({super.key, required this.workPolicy});

  final WorkShiftPolicyModel? workPolicy;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Space.y(8),
        Padding(
          padding: EdgeInsets.symmetric(vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                workPolicy?.name ?? '',
                style: context.textStyles.titleMedium?.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: context.colors.onSurface,
                ),
              ),
              Space.y(8),
              Row(
                spacing: 24,
                children: [
                  _timeDetail(
                    context: context,
                    label: 'Start Time',
                    data: workPolicy?.startTime ?? '',
                  ),
                  _timeDetail(
                    context: context,
                    label: 'End Time',
                    data: workPolicy?.endTime ?? '',
                  ),
                  _timeDetail(
                    context: context,
                    label: 'Break Duration',
                    data: workPolicy?.breakDuration ?? '',
                  ),
                ],
              ),
            ],
          ),
        ),
        Divider(color: context.colors.outlineVariant, height: 1),
        Space.y(8),
        Space.y(10),
        Text(
          'Policy',
          style: context.textStyles.titleMedium?.copyWith(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: context.colors.onSurface,
          ),
        ),
        HtmlView(htmlContent: workPolicy?.policy ?? ''),
      ],
    );
  }

  Widget _timeDetail({
    required BuildContext context,
    required String label,
    required String data,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: context.textStyles.titleMedium?.copyWith(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: context.colors.secondary,
          ),
        ),
        Text(
          data,
          style: context.textStyles.bodyMedium?.copyWith(
            fontSize: 16,
            fontWeight: FontWeight.w400,
            color: context.colors.onSurface,
          ),
        ),
      ],
    );
  }
}
