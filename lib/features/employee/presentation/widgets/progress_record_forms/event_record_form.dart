import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/services/media_picker/media_picker.protocol.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/core/utils/helpers/snackbar.helper.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/core/utils/helpers/validators.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:hrms_tst/features/employee/domain/progress_record.controller.dart';
import 'package:hrms_tst/features/teammates/data/models/team_progress.model.dart';
import 'package:hrms_tst/features/teammates/domain/team_progress.controller.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/app_html_editor.dart';
import 'package:hrms_tst/shared/widgets/app_text_field.dart';
import 'package:hrms_tst/shared/widgets/file_image_with_loading.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';
import 'package:intl/intl.dart';

class AddEventRecordForm extends HookConsumerWidget {
  const AddEventRecordForm({super.key, this.eventProgress});

  final EventProgressModel? eventProgress;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useState(GlobalKey<FormState>());

    final eventNameController = useTextEditingController(
      text: eventProgress?.name ?? '',
    );
    final eventLinkController = useTextEditingController(
      text: eventProgress?.link ?? '',
    );
    final fromDate = useState<DateTime>(
      eventProgress?.startDate ?? DateTime.now(),
    );
    final toDate = useState<DateTime>(eventProgress?.endDate ?? DateTime.now());

    final images = useState<List<File>>([]);

    final notesText = useState(eventProgress?.notes ?? '');

    final editorKey = useState(GlobalKey());

    final imageSize = min(context.screenWidth * 0.3, 100.0);

    return Dialog.fullscreen(
      backgroundColor: context.colors.surface,
      child: Form(
        key: formKey.value,

        child: ListView(
          padding: EdgeInsets.all(16),
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${eventProgress != null ? 'Edit' : 'Add'} Event Record',
                  style: context.textStyles.titleLarge?.copyWith(
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                    color: context.colors.secondary,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    context.pop();
                  },
                  icon: SVGImage(
                    Assets.svgs.closeIcon,
                    height: 24,
                    width: 24,
                    color: context.colors.secondary,
                  ),
                ),
              ],
            ),
            Space.y(10),
            AppTextField(
              labelText: 'Event Name',
              controller: eventNameController,
              fillColor: context.colors.surface,
              validator: Validator(
                fieldName: 'Event Name',
                validations: [
                  Validations.required(error: 'Please enter event name'),
                ],
              ).build,
            ),
            Space.y(10),
            Row(
              children: [
                KeyedSubtree(
                  key: ValueKey('FROM:${fromDate.value}'),
                  child: Expanded(
                    child: AppTextField(
                      readOnly: true,
                      fillColor: context.colors.surface,
                      onTap: () async {
                        fromDate.value =
                            await showDatePicker(
                              context: context,
                              firstDate: DateTime(1000),
                              lastDate: DateTime.now(),
                            ) ??
                            fromDate.value;
                      },
                      labelText: 'From',
                      initialValue: DateFormat(
                        'dd MMM yyyy',
                      ).format(fromDate.value),
                    ),
                  ),
                ),
                Space.x(8),
                KeyedSubtree(
                  key: ValueKey('TO:${toDate.value}'),
                  child: Expanded(
                    child: AppTextField(
                      readOnly: true,
                      fillColor: context.colors.surface,
                      onTap: () async {
                        toDate.value =
                            await showDatePicker(
                              context: context,
                              firstDate: DateTime(1000),
                              lastDate: DateTime.now(),
                            ) ??
                            toDate.value;
                      },
                      labelText: 'To',
                      initialValue: DateFormat(
                        'dd MMM yyyy',
                      ).format(toDate.value),
                    ),
                  ),
                ),
              ],
            ),
            Space.y(10),
            AppTextField(
              labelText: 'Event Link',
              controller: eventLinkController,
              keyboardType: TextInputType.url,
              fillColor: context.colors.surface,
              validator: Validator(
                fieldName: 'Event Link',
                validations: [
                  Validations.url(error: 'Please enter a valid URL'),
                ],
              ).build,
            ),
            if (eventProgress == null) Space.y(10),
            if (eventProgress == null)
              FormField(
                validator: (value) {
                  if (images.value.isEmpty) {
                    return 'Please add an image';
                  }
                  if (images.value.length > 5) {
                    return 'Maximum 5 images are allowed';
                  }
                  return null;
                },
                builder: (field) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Text.rich(
                        TextSpan(
                          children: [
                            TextSpan(
                              text: 'Images',
                              style: context.textStyles.bodyMedium?.copyWith(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: Color(0xFF7A7A7A),
                              ),
                            ),
                            TextSpan(
                              text: field.hasError
                                  ? ' (${field.errorText})'
                                  : '',
                              style: context.textStyles.labelSmall?.copyWith(
                                color: context.colors.error,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Space.y(8),
                      KeyedSubtree(
                        key: ValueKey(images.value),
                        child: Row(
                          spacing: 8,
                          children: [
                            GestureDetector(
                              onTap: () async {
                                final result = await getIt
                                    .get<MediaPickerProtocol>()
                                    .pickMultipleImages(
                                      source: MediaSource.gallery,
                                      maxCount: 5,
                                      onCancelled: () {},
                                    );

                                if (result != null) {
                                  images.value = [
                                    ...images.value,
                                    ...result.map((e) => File(e.path)),
                                  ];
                                }

                                field.validate();
                              },
                              child: Container(
                                height: imageSize,
                                width: imageSize / 2,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: context.colors.primary,
                                    width: 2,
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  Icons.add,
                                  color: context.colors.primary,
                                ),
                              ),
                            ),
                            Expanded(
                              child: SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Row(
                                  spacing: 8,
                                  children: images.value.isNotEmpty
                                      ? images.value
                                            .map(
                                              (e) => Stack(
                                                children: [
                                                  ClipRRect(
                                                    borderRadius:
                                                        BorderRadiusGeometry.circular(
                                                          4,
                                                        ),
                                                    child: FileImageWithLoading(
                                                      file: e,
                                                      imageSize: Size.square(
                                                        imageSize,
                                                      ),
                                                    ),
                                                  ),
                                                  Positioned(
                                                    top: 4,
                                                    right: 4,
                                                    child: GestureDetector(
                                                      onTap: () {
                                                        images.value = [
                                                          ...images.value
                                                            ..remove(e),
                                                        ];
                                                        field.validate();
                                                      },
                                                      child: Container(
                                                        padding: EdgeInsets.all(
                                                          4,
                                                        ),
                                                        decoration: BoxDecoration(
                                                          shape:
                                                              BoxShape.circle,
                                                          color: context
                                                              .colors
                                                              .errorContainer,
                                                        ),
                                                        child: Icon(
                                                          Icons.close,
                                                          color: context
                                                              .colors
                                                              .error,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            )
                                            .toList()
                                      : [],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              ),
            Space.y(10),
            Text(
              'Notes',
              style: context.textStyles.bodyMedium?.copyWith(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Color(0xFF7A7A7A),
              ),
            ),
            Space.y(4),
            FormField(
              builder: (field) => Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(
                    key: editorKey.value,
                    child: AppHTMLEditor(
                      height: context.screenHeight * 0.2,
                      fieldState: field,
                      onFocus: () {
                        Future.delayed(Duration(milliseconds: 400), () {
                          Scrollable.ensureVisible(
                            editorKey.value.currentContext!,
                            duration: Duration(milliseconds: 300),
                          );
                        });
                      },
                      initialValue: notesText.value,
                      onChanged: (value) {
                        notesText.value = value;
                      },
                    ),
                  ),
                  if (field.hasError)
                    Padding(
                      padding: const EdgeInsets.only(top: 4.0),
                      child: Text(
                        field.errorText!,
                        style: context.textStyles.labelMedium?.copyWith(
                          color: context.colors.error,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            Space.y(10),
            OutlinedButton(
              onPressed: () async {
                if (!formKey.value.currentState!.validate()) {
                  return;
                }

                final result = eventProgress != null
                    ? await ref
                          .read(addProgressRecordProvider.notifier)
                          .editEventProgressRecord(
                            id: eventProgress!.id,
                            name: eventNameController.text,
                            startDate: fromDate.value,
                            endDate: toDate.value,
                            docLink: eventLinkController.text,
                            notes: notesText.value,
                          )
                    : await ref
                          .read(addProgressRecordProvider.notifier)
                          .addEventProgressRecord(
                            name: eventNameController.text,
                            startDate: fromDate.value,
                            endDate: toDate.value,
                            docLink: eventLinkController.text,
                            images: images.value,
                            notes: notesText.value,
                          );

                result.fold(
                  onSuccess: (_) {
                    ref.invalidate(eventProgressProvider);
                    context.showSuccess('Event Record Sent for Verification');
                  },
                  onFailure: (message) {
                    context.showError(message);
                  },
                );

                if (context.mounted) {
                  context.pop();
                }
              },
              child: Text(
                '${eventProgress != null ? 'Edit' : 'Add'} Event Record',
                style: context.textStyles.titleMedium?.copyWith(
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  color: context.colors.primary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
