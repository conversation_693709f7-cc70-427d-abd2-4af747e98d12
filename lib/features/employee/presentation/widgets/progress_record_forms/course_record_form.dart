import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/core/utils/helpers/snackbar.helper.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/core/utils/helpers/validators.dart';
import 'package:hrms_tst/features/employee/domain/progress_record.controller.dart';
import 'package:hrms_tst/features/teammates/data/models/team_progress.model.dart';
import 'package:hrms_tst/features/teammates/domain/team_progress.controller.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/app_html_editor.dart';
import 'package:hrms_tst/shared/widgets/app_text_field.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';
import 'package:intl/intl.dart';

class AddCourseRecordForm extends HookConsumerWidget {
  const AddCourseRecordForm({super.key, this.courseProgress});

  final CourseProgressModel? courseProgress;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useState(GlobalKey<FormState>());

    final courseNameController = useTextEditingController(
      text: courseProgress?.name ?? '',
    );
    final completedAt = useState<DateTime>(
      courseProgress?.completedAt ?? DateTime.now(),
    );
    final courseLinkController = useTextEditingController(
      text: courseProgress?.courseLink ?? '',
    );
    final attachmentLinkController = useTextEditingController(
      text: courseProgress?.docLink ?? '',
    );

    final notesText = useState(courseProgress?.notes ?? '');

    final editorKey = useState(GlobalKey());

    return Dialog.fullscreen(
      backgroundColor: context.colors.surface,
      child: Form(
        key: formKey.value,

        child: ListView(
          padding: EdgeInsets.all(16),
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${courseProgress != null ? 'Edit' : 'Add'} Course Record',
                  style: context.textStyles.titleLarge?.copyWith(
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                    color: context.colors.secondary,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    context.pop();
                  },
                  icon: SVGImage(
                    Assets.svgs.closeIcon,
                    height: 24,
                    width: 24,
                    color: context.colors.secondary,
                  ),
                ),
              ],
            ),
            Space.y(10),
            AppTextField(
              labelText: 'Course Name',
              controller: courseNameController,
              fillColor: context.colors.surface,
              validator: Validator(
                fieldName: 'Course Name',
                validations: [
                  Validations.required(error: 'Please enter course name'),
                ],
              ).build,
            ),
            Space.y(10),
            KeyedSubtree(
              key: ValueKey(completedAt.value),
              child: AppTextField(
                readOnly: true,
                fillColor: context.colors.surface,
                onTap: () async {
                  completedAt.value =
                      await showDatePicker(
                        context: context,
                        firstDate: DateTime(1000),
                        lastDate: DateTime.now(),
                      ) ??
                      completedAt.value;
                },
                labelText: 'Completed At',
                initialValue: DateFormat(
                  'dd MMM yyyy',
                ).format(completedAt.value),
              ),
            ),
            Space.y(10),
            AppTextField(
              labelText: 'Course Link',
              controller: courseLinkController,
              keyboardType: TextInputType.url,
              fillColor: context.colors.surface,
              validator: Validator(
                fieldName: 'Course Link',
                validations: [
                  Validations.required(error: 'Please enter course link'),
                  Validations.url(error: 'Please enter a valid URL'),
                ],
              ).build,
            ),

            Space.y(10),
            AppTextField(
              labelText: 'Attachment Link',
              controller: attachmentLinkController,
              keyboardType: TextInputType.url,
              fillColor: context.colors.surface,
              validator: Validator(
                fieldName: 'Attachment Link',
                validations: [
                  Validations.url(error: 'Please enter a valid URL'),
                ],
              ).build,
            ),
            Space.y(10),
            Text(
              'Notes',
              style: context.textStyles.bodyMedium?.copyWith(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Color(0xFF7A7A7A),
              ),
            ),
            Space.y(4),
            FormField(
              builder: (field) => Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(
                    key: editorKey.value,
                    child: AppHTMLEditor(
                      height: context.screenHeight * 0.3,
                      fieldState: field,
                      onFocus: () {
                        Future.delayed(Duration(milliseconds: 400), () {
                          Scrollable.ensureVisible(
                            editorKey.value.currentContext!,
                            duration: Duration(milliseconds: 300),
                          );
                        });
                      },
                      initialValue: notesText.value,
                      onChanged: (value) {
                        notesText.value = value;
                      },
                    ),
                  ),
                  if (field.hasError)
                    Padding(
                      padding: const EdgeInsets.only(top: 4.0),
                      child: Text(
                        field.errorText!,
                        style: context.textStyles.labelMedium?.copyWith(
                          color: context.colors.error,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            Space.y(10),
            OutlinedButton(
              onPressed: () async {
                if (!formKey.value.currentState!.validate()) {
                  return;
                }

                final result = courseProgress != null
                    ? await ref
                          .read(addProgressRecordProvider.notifier)
                          .editCourseProgressRecord(
                            id: courseProgress!.id,
                            name: courseNameController.text,
                            completedAt: completedAt.value,
                            notes: notesText.value,
                            courseLink: courseLinkController.text,
                            docLink: attachmentLinkController.text,
                          )
                    : await ref
                          .read(addProgressRecordProvider.notifier)
                          .addCourseProgressRecord(
                            name: courseNameController.text,
                            completedAt: completedAt.value,
                            notes: notesText.value,
                            courseLink: courseLinkController.text,
                            docLink: attachmentLinkController.text,
                          );

                result.fold(
                  onSuccess: (_) {
                    ref.invalidate(courseProgressProvider);
                    context.pop();
                    context.showSuccess('Course Record Sent for Verification');
                  },
                  onFailure: (message) {
                    context.pop();
                    context.showError(message);
                  },
                );
              },
              child: Text(
                '${courseProgress != null ? 'Edit' : 'Add'} Course Record',
                style: context.textStyles.titleMedium?.copyWith(
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  color: context.colors.primary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
