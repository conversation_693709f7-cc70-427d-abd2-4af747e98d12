import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/core/utils/helpers/snackbar.helper.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/core/utils/helpers/validators.dart';
import 'package:hrms_tst/features/employee/domain/progress_record.controller.dart';
import 'package:hrms_tst/features/teammates/data/models/team_progress.model.dart';
import 'package:hrms_tst/features/teammates/domain/team_progress.controller.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/app_html_editor.dart';
import 'package:hrms_tst/shared/widgets/app_text_field.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';
import 'package:intl/intl.dart';

class AddSessionRecordForm extends HookConsumerWidget {
  const AddSessionRecordForm({super.key, this.sessionProgress});

  final SessionProgressModel? sessionProgress;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useState(GlobalKey<FormState>());

    final sessionNameController = useTextEditingController(
      text: sessionProgress?.name ?? '',
    );
    final topicController = useTextEditingController(
      text: sessionProgress?.topic ?? '',
    );
    final attachmentLinkController = useTextEditingController(
      text: sessionProgress?.docLink ?? '',
    );
    final completedAt = useState<DateTime>(
      sessionProgress?.completedAt ?? DateTime.now(),
    );

    final notesText = useState(sessionProgress?.notes ?? '');

    final editorKey = useState(GlobalKey());

    return Dialog.fullscreen(
      backgroundColor: context.colors.surface,
      child: Form(
        key: formKey.value,

        child: ListView(
          padding: EdgeInsets.all(16),
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${sessionProgress != null ? 'Edit' : 'Add'} Session Record',
                  style: context.textStyles.titleLarge?.copyWith(
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                    color: context.colors.secondary,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    context.pop();
                  },
                  icon: SVGImage(
                    Assets.svgs.closeIcon,
                    height: 24,
                    width: 24,
                    color: context.colors.secondary,
                  ),
                ),
              ],
            ),
            Space.y(10),
            AppTextField(
              labelText: 'Session Name',
              controller: sessionNameController,
              fillColor: context.colors.surface,
              validator: Validator(
                fieldName: 'Session Name',
                validations: [
                  Validations.required(error: 'Please enter session name'),
                ],
              ).build,
            ),
            Space.y(10),
            AppTextField(
              labelText: 'Topic',
              controller: topicController,
              fillColor: context.colors.surface,
              validator: Validator(
                fieldName: 'Topic',
                validations: [
                  Validations.required(error: 'Please enter topic'),
                ],
              ).build,
            ),
            Space.y(10),
            KeyedSubtree(
              key: ValueKey(completedAt.value),
              child: AppTextField(
                readOnly: true,
                fillColor: context.colors.surface,
                onTap: () async {
                  completedAt.value =
                      await showDatePicker(
                        context: context,
                        firstDate: DateTime(1000),
                        lastDate: DateTime.now(),
                      ) ??
                      completedAt.value;
                },
                labelText: 'Completed At',
                initialValue: DateFormat(
                  'dd MMM yyyy',
                ).format(completedAt.value),
              ),
            ),
            Space.y(10),
            AppTextField(
              labelText: 'Attachment-Doc Link',
              controller: attachmentLinkController,
              keyboardType: TextInputType.url,
              fillColor: context.colors.surface,
              validator: Validator(
                fieldName: 'Attachment Link',
                validations: [
                  Validations.url(error: 'Please enter a valid URL'),
                ],
              ).build,
            ),
            Space.y(10),
            Text(
              'Notes',
              style: context.textStyles.bodyMedium?.copyWith(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Color(0xFF7A7A7A),
              ),
            ),
            Space.y(4),
            FormField(
              builder: (field) => Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(
                    key: editorKey.value,
                    child: AppHTMLEditor(
                      height: context.screenHeight * 0.3,
                      fieldState: field,
                      onFocus: () {
                        Future.delayed(Duration(milliseconds: 400), () {
                          Scrollable.ensureVisible(
                            editorKey.value.currentContext!,
                            duration: Duration(milliseconds: 300),
                          );
                        });
                      },
                      initialValue: notesText.value,
                      onChanged: (value) {
                        notesText.value = value;
                      },
                    ),
                  ),
                  if (field.hasError)
                    Padding(
                      padding: const EdgeInsets.only(top: 4.0),
                      child: Text(
                        field.errorText!,
                        style: context.textStyles.labelMedium?.copyWith(
                          color: context.colors.error,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            Space.y(10),
            OutlinedButton(
              onPressed: () async {
                if (!formKey.value.currentState!.validate()) {
                  return;
                }

                final result = sessionProgress != null
                    ? await ref
                          .read(addProgressRecordProvider.notifier)
                          .editSessionProgressRecord(
                            id: sessionProgress!.id,
                            name: sessionNameController.text,
                            topic: topicController.text,
                            completedAt: completedAt.value,
                            notes: notesText.value,
                            docLink: attachmentLinkController.text,
                          )
                    : await ref
                          .read(addProgressRecordProvider.notifier)
                          .addSessionProgressRecord(
                            name: sessionNameController.text,
                            topic: topicController.text,
                            completedAt: completedAt.value,
                            notes: notesText.value,
                            docLink: attachmentLinkController.text,
                          );

                result.fold(
                  onSuccess: (_) {
                    ref.invalidate(sessionProgressProvider);
                    context.pop();
                    context.showSuccess('Session Record Sent for Verification');
                  },
                  onFailure: (message) {
                    context.pop();
                    context.showError(message);
                  },
                );
              },
              child: Text(
                '${sessionProgress != null ? 'Edit' : 'Add'} Session Record',
                style: context.textStyles.titleMedium?.copyWith(
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  color: context.colors.primary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
