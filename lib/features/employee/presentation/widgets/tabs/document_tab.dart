import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/features/employee/domain/employee.controller.dart';
import 'package:hrms_tst/features/employee/presentation/widgets/document_category_tile.dart';

class DocumentTab extends ConsumerWidget {
  const DocumentTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return RefreshIndicator.adaptive(
      onRefresh: () async {
        ref.invalidate(documentCategoriesProvider);
        await ref.read(documentCategoriesProvider.future);
      },
      child: switch (ref.watch(documentCategoriesProvider)) {
        AsyncData(:final value) => ListView.builder(
          itemCount: value.length,
          itemBuilder: (context, index) =>
              DocumentCategoryTile(category: value[index]),
          padding: EdgeInsets.symmetric(horizontal: 8),
        ),
        AsyncError(:final error) => Text(error.toString()),
        _ => Center(child: CircularProgressIndicator.adaptive()),
      },
    );
  }
}
