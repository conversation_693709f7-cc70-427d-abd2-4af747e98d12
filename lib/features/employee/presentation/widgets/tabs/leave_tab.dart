import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/employee/data/models/leave_tab.model.dart';
import 'package:hrms_tst/features/employee/domain/employee.controller.dart';
import 'package:hrms_tst/features/home/<USER>/home.controller.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';
import 'package:hrms_tst/shared/widgets/filters_panel.dart';
import 'package:hrms_tst/shared/widgets/leave_history_tile.dart';
import 'package:hrms_tst/shared/widgets/month_range_selection.dart';
import 'package:hrms_tst/shared/widgets/no_data_view.dart';
import 'package:hrms_tst/shared/widgets/popup_filter_button.dart';

import '../../../../request/presentation/widgets/leave_request_dialog.dart';
import '../leave_balance_section.dart';

class LeaveTab extends ConsumerWidget {
  const LeaveTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      floatingActionButton: FloatingActionButton.extended(
        heroTag: 'leave',
        onPressed: () {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => LeaveRequestDialog(),
          );
        },
        label: Text('Request'),
        icon: Icon(Icons.add),
      ),
      body: RefreshIndicator.adaptive(
        onRefresh: () async {
          ref.invalidate(leaveBalanceProvider);
          ref.invalidate(leaveRequestRecordsProvider);

          await ref.read(leaveBalanceProvider.future);
        },
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: 16).copyWith(bottom: 16),
          children: [
            LeaveBalanceSection(),
            Space.y(16),
            HookBuilder(
              builder: (context) {
                final leaveTypeFilter = useState<int?>(null);
                final leaveStatusFilter = useState<String?>(null);

                final startDate = useState<DateTime?>(null);
                final endDate = useState<DateTime?>(null);
                return CardContainer(
                  elevation: 1,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: Row(
                            children: [
                              Text(
                                'Leave History',
                                style: context.textStyles.titleMedium?.copyWith(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: context.colors.secondary,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Space.y(12),
                        FiltersPanel(
                          filters: [
                            PopupFilterButton<int?>(
                              filterName: 'Leave Type',
                              initialValue: leaveTypeFilter.value,
                              options: [1, 2, 3],
                              stringValue: (value) => leaveTypeFromId(value!),
                              onSelected: (option) {
                                leaveTypeFilter.value = option;
                              },
                            ),

                            Space.x(6),
                            PopupFilterButton(
                              filterName: 'Status',
                              initialValue: leaveStatusFilter.value,
                              options: ['Approved', 'Rejected', 'Pending'],
                              stringValue: (value) => value,
                              onSelected: (option) {
                                leaveStatusFilter.value = option;
                              },
                            ),
                          ],
                          trailing: [
                            MonthRangeSelection(
                              filterButtonColor: context.colors.surface,
                              startDate: startDate.value,
                              endDate: endDate.value,
                              onFilterSelected: (start, end) {
                                startDate.value = start;
                                endDate.value = end;

                                context.pop();
                              },
                            ),
                          ],
                        ),
                        Space.y(8),

                        switch (ref.watch(
                          leaveRequestRecordsProvider(
                            leaveTypeFilter.value,
                            leaveStatusFilter.value,
                            startDate.value,
                            endDate.value,
                          ),
                        )) {
                          AsyncData(:final value) => Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              if (value.isEmpty)
                                Padding(
                                  padding: const EdgeInsets.all(24.0),
                                  child: NoDataView(),
                                ),
                              ...value.map((record) {
                                return Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                  ),
                                  child: LeaveHistoryTile(record: record),
                                );
                              }),
                            ],
                          ),
                          AsyncError(:final error) => Center(
                            child: Text(error.toString()),
                          ),
                          _ => Center(
                            child: Padding(
                              padding: const EdgeInsets.all(24.0),
                              child: CircularProgressIndicator.adaptive(),
                            ),
                          ),
                        },
                      ],
                    ),
                  ),
                );
              },
            ),
            Space.y(70),
          ],
        ),
      ),
    );
  }
}
