import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/employee/domain/employee.controller.dart';
import 'package:hrms_tst/features/employee/presentation/widgets/attendance_filter.dart';
import 'package:hrms_tst/features/employee/presentation/work_policy.screen.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';
import 'package:hrms_tst/shared/widgets/no_data_view.dart';

import '../attendance_history_tile.dart';

class AttendanceTab extends HookConsumerWidget {
  const AttendanceTab({super.key, required this.selectedDateFilter});

  final ValueNotifier<DateTime> selectedDateFilter;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return RefreshIndicator.adaptive(
      onRefresh: () async {
        ref.invalidate(attendanceDataProvider);
        await ref.read(attendanceDataProvider(selectedDateFilter.value).future);
      },
      child: switch (ref.watch(
        attendanceDataProvider(selectedDateFilter.value),
      )) {
        AsyncData(:final value) => ListView(
          padding: EdgeInsets.symmetric(horizontal: 16).copyWith(bottom: 16),
          children: [
            CardContainer(
              elevation: 1,
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Row(
                  children: [
                    Text(
                      'Office Hours & Schedule',
                      style: context.textStyles.titleMedium?.copyWith(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: context.colors.secondary,
                      ),
                    ),
                    Spacer(),
                    GestureDetector(
                      onTap: () {
                        context.pushNamed(
                          WorkPolicyScreen.name,
                          queryParameters: {
                            'workPolicyId': value.workShift?.id.toString(),
                            'timetableId': value.timetableData?.id.toString(),
                          },
                        );
                      },
                      child: Text(
                        'Policy',
                        style: context.textStyles.titleMedium?.copyWith(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: context.colors.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Space.y(16),
            CardContainer(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Text(
                          'Attendance History',
                          style: context.textStyles.titleMedium?.copyWith(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: context.colors.secondary,
                          ),
                        ),
                        Spacer(),
                        AttendanceFilter(
                          selectedDate: selectedDateFilter.value,
                          onFilterSelected: (selectedDate) {
                            selectedDateFilter.value = selectedDate;
                            context.pop();
                          },
                        ),
                      ],
                    ),
                    Space.y(16),
                    Row(
                      children: [
                        Expanded(
                          child: _analyticsContainer(
                            context: context,
                            label: 'On time',
                            data: '---%',
                          ),
                        ),
                        Space.x(16),
                        Expanded(
                          child: _analyticsContainer(
                            context: context,
                            label: 'Absent',
                            data: 'N/A',
                            dataColor: context.colors.error,
                          ),
                        ),
                      ],
                    ),
                    Space.y(8),
                    if (value.attendanceHistory?.isEmpty ?? false)
                      Padding(
                        padding: const EdgeInsets.all(24.0),
                        child: NoDataView(),
                      ),
                    ...?value.attendanceHistory?.map((attendance) {
                      return AttendanceHistoryTile(attendance: attendance);
                    }),
                  ],
                ),
              ),
            ),
          ],
        ),
        AsyncError(:final error) => Center(child: Text(error.toString())),
        _ => Center(child: CircularProgressIndicator.adaptive()),
      },
    );
  }

  Widget _analyticsContainer({
    required BuildContext context,
    required String label,
    required String data,
    Color? dataColor,
  }) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.colors.outline,
        borderRadius: BorderRadius.circular(14),
        border: Border.all(width: 1, color: context.colors.outlineVariant),
      ),
      child: Column(
        spacing: 2,
        children: [
          Text(
            label,
            style: context.textStyles.titleLarge?.copyWith(
              fontSize: 18,
              fontWeight: FontWeight.w400,
            ),
          ),
          Text(
            data,
            style: context.textStyles.titleLarge?.copyWith(
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: dataColor ?? context.colors.primary,
            ),
          ),
        ],
      ),
    );
  }
}
