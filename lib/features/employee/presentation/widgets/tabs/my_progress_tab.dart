import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/employee/presentation/widgets/progress_record_forms/course_record_form.dart';
import 'package:hrms_tst/features/teammates/domain/team_progress.controller.dart';
import 'package:hrms_tst/features/teammates/presentation/team_progress.screen.dart';
import 'package:hrms_tst/features/teammates/presentation/widgets/team_progress_tabs/book_progress_tab_view.dart';
import 'package:hrms_tst/features/teammates/presentation/widgets/team_progress_tabs/course_progress_tab_view.dart';
import 'package:hrms_tst/features/teammates/presentation/widgets/team_progress_tabs/event_progress_tab_view.dart';
import 'package:hrms_tst/features/teammates/presentation/widgets/team_progress_tabs/gd_progress_tab_view.dart';
import 'package:hrms_tst/features/teammates/presentation/widgets/team_progress_tabs/session_progress_tab_view.dart';
import 'package:hrms_tst/shared/providers/auth.provider.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';
import 'package:hrms_tst/shared/widgets/month_range_selection.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';

import '../progress_record_forms/book_record_form.dart';
import '../progress_record_forms/event_record_form.dart';
import '../progress_record_forms/gd_record_form.dart';
import '../progress_record_forms/session_record_form.dart';

class MyProgressTab extends HookConsumerWidget {
  const MyProgressTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userEmployeeId = ref.read(authProvider).requireValue!.details!.id;
    final selectedTab = useState(TeamProgressTab.book);

    final startDate = useState<DateTime?>(null);
    final endDate = useState<DateTime?>(null);

    final bookProgress = ref.watch(
      bookProgressProvider(startDate.value, endDate.value, userEmployeeId),
    );
    final courseProgress = ref.watch(
      courseProgressProvider(startDate.value, endDate.value, userEmployeeId),
    );
    final gdProgress = ref.watch(
      gdProgressProvider(startDate.value, endDate.value, userEmployeeId),
    );
    final sessionProgress = ref.watch(
      sessionProgressProvider(startDate.value, endDate.value, userEmployeeId),
    );
    final eventProgress = ref.watch(
      eventProgressProvider(startDate.value, endDate.value, userEmployeeId),
    );
    return Scaffold(
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          showDialog(
            context: context,
            builder: (context) {
              return switch (selectedTab.value) {
                TeamProgressTab.book => AddBookRecordForm(),
                TeamProgressTab.course => AddCourseRecordForm(),
                TeamProgressTab.gd => AddGDRecordForm(),
                TeamProgressTab.session => AddSessionRecordForm(),
                TeamProgressTab.event => AddEventRecordForm(),
              };
            },
          );
        },
        icon: Icon(Icons.add),
        label: Text(switch (selectedTab.value) {
          TeamProgressTab.book => 'Book',
          TeamProgressTab.course => 'Course',
          TeamProgressTab.gd => 'GD',
          TeamProgressTab.session => 'Session',
          TeamProgressTab.event => 'Event',
        }),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8).copyWith(bottom: 8),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: TeamProgressTab.values.map((e) {
                  final isSelected = e == selectedTab.value;
                  return GestureDetector(
                    onTap: () {
                      selectedTab.value = e;
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: context.colors.tertiary.withValues(
                          alpha: isSelected ? 1 : 0.1,
                        ),
                      ),
                      padding: EdgeInsets.all(12),
                      child: AnimatedSize(
                        duration: const Duration(milliseconds: 200),
                        curve: Curves.ease,
                        alignment: Alignment.centerLeft,
                        child: Row(
                          spacing: 4,
                          children: [
                            SVGImage(
                              e.iconPath,
                              height: 24,
                              width: 24,
                              color: isSelected
                                  ? context.colors.surface
                                  : context.colors.tertiary,
                            ),
                            if (isSelected)
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8.0,
                                ),
                                child: Text(
                                  e.label,
                                  style: context.textStyles.titleMedium
                                      ?.copyWith(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: context.colors.surface,
                                      ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
            Space.y(8),
            Expanded(
              child: CardContainer(
                elevation: 1,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Row(
                        children: [
                          Text(
                            'Progress',
                            style: context.textStyles.titleMedium?.copyWith(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: context.colors.onSurface,
                            ),
                          ),
                          Spacer(),
                          MonthRangeSelection(
                            startDate: startDate.value,
                            endDate: endDate.value,
                            onFilterSelected: (start, end) {
                              startDate.value = start;
                              endDate.value = end;

                              context.pop();
                            },
                          ),
                        ],
                      ),
                      Space.y(8),
                      Expanded(
                        child: switch (selectedTab.value) {
                          TeamProgressTab.book => RefreshIndicator.adaptive(
                            onRefresh: () async {
                              ref.invalidate(bookProgressProvider);
                            },
                            child: BookProgressTabView(
                              state: bookProgress,
                              showStatus: true,
                            ),
                          ),
                          TeamProgressTab.course => RefreshIndicator.adaptive(
                            onRefresh: () async {
                              ref.invalidate(courseProgressProvider);
                            },
                            child: CourseProgressTabView(
                              state: courseProgress,
                              showStatus: true,
                            ),
                          ),
                          TeamProgressTab.gd => RefreshIndicator.adaptive(
                            onRefresh: () async {
                              ref.invalidate(gdProgressProvider);
                            },
                            child: GdProgressTabView(
                              state: gdProgress,
                              showStatus: true,
                            ),
                          ),
                          TeamProgressTab.session => RefreshIndicator.adaptive(
                            onRefresh: () async {
                              ref.invalidate(sessionProgressProvider);
                            },
                            child: SessionProgressTabView(
                              state: sessionProgress,
                              showStatus: true,
                            ),
                          ),
                          TeamProgressTab.event => RefreshIndicator.adaptive(
                            onRefresh: () async {
                              ref.invalidate(eventProgressProvider);
                            },
                            child: EventProgressTabView(
                              state: eventProgress,
                              showStatus: true,
                            ),
                          ),
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
