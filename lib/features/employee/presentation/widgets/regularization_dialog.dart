import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/core/utils/helpers/snackbar.helper.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/core/utils/helpers/validators.dart';
import 'package:hrms_tst/features/employee/data/models/attendance_tab.model.dart';
import 'package:hrms_tst/features/employee/domain/employee.controller.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/app_text_field.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';
import 'package:intl/intl.dart';

class RegularizationDialog extends HookConsumerWidget {
  const RegularizationDialog({super.key, required this.attendance});

  final AttendanceHistoryModel attendance;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useState(GlobalKey<FormState>());

    final attendanceDate = attendance.createdAt!.toLocal();

    final clockInTime = useState<DateTime>(
      attendanceDate.copyWith(hour: 10, minute: 00),
    );
    final clockOutTime = useState<DateTime>(
      attendanceDate.copyWith(hour: 19, minute: 00),
    );

    final reasonController = useTextEditingController();

    return Dialog(
      backgroundColor: context.colors.surface,
      insetPadding: EdgeInsets.symmetric(horizontal: 20),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Form(
        key: formKey.value,

        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Regularization Request',
                    style: context.textStyles.titleLarge?.copyWith(
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                      color: context.colors.secondary,
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      context.pop();
                    },
                    icon: SVGImage(
                      Assets.svgs.closeIcon,
                      height: 24,
                      width: 24,
                      color: context.colors.secondary,
                    ),
                  ),
                ],
              ),
              Space.y(16),
              Row(
                spacing: 8,
                children: [
                  KeyedSubtree(
                    key: ValueKey(clockInTime.value),
                    child: Expanded(
                      child: AppTextField(
                        readOnly: true,
                        fillColor: context.colors.surface,
                        onTap: () async {
                          final pickedTime = await showTimePicker(
                            context: context,
                            initialTime: TimeOfDay.fromDateTime(
                              clockInTime.value,
                            ),
                          );

                          if (pickedTime != null) {
                            clockInTime.value = attendanceDate.copyWith(
                              hour: pickedTime.hour,
                              minute: pickedTime.minute,
                            );
                          }
                        },
                        labelText: 'Clock In Time',
                        initialValue: DateFormat('hh:mm a').format(
                          attendanceDate.copyWith(
                            hour: clockInTime.value.hour,
                            minute: clockInTime.value.minute,
                          ),
                        ),
                      ),
                    ),
                  ),
                  KeyedSubtree(
                    key: ValueKey(clockOutTime.value),
                    child: Expanded(
                      child: AppTextField(
                        readOnly: true,
                        fillColor: context.colors.surface,
                        onTap: () async {
                          final pickedTime = await showTimePicker(
                            context: context,
                            initialTime: TimeOfDay.fromDateTime(
                              clockOutTime.value,
                            ),
                          );

                          if (pickedTime != null) {
                            clockOutTime.value = attendanceDate.copyWith(
                              hour: pickedTime.hour,
                              minute: pickedTime.minute,
                            );
                          }
                        },
                        labelText: 'Clock Out Time',
                        initialValue: DateFormat('hh:mm a').format(
                          attendanceDate.copyWith(
                            hour: clockOutTime.value.hour,
                            minute: clockOutTime.value.minute,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              Space.y(12),
              AppTextField(
                labelText: 'Reason',
                controller: reasonController,
                fillColor: context.colors.surface,
                maxLines: 3,
                validator: Validator(
                  fieldName: 'Reason',
                  validations: [
                    Validations.required(error: 'Please enter a reason'),
                  ],
                ).build,
              ),
              Space.y(16),

              OutlinedButton(
                onPressed: () async {
                  if (!formKey.value.currentState!.validate()) {
                    return;
                  }

                  final result = await ref
                      .read(attendanceDataProvider(null).notifier)
                      .requestRegularization(
                        id: attendance.id,
                        clockIn: clockInTime.value,
                        clockOut: clockOutTime.value,
                        reason: reasonController.text,
                      );

                  result.fold(
                    onSuccess: (_) {
                      ref.invalidate(attendanceDataProvider);
                      context.pop();
                      context.showSuccess(
                        'Regularization Request Sent Successfully',
                      );
                    },
                    onFailure: (message) {
                      context.pop();
                      context.showError(message);
                    },
                  );
                },
                child: Text(
                  'Request Regularization',
                  style: context.textStyles.titleMedium?.copyWith(
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                    color: context.colors.primary,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
