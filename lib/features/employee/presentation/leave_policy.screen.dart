import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/employee/data/models/leave_tab.model.dart';
import 'package:hrms_tst/shared/widgets/app_html_view.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';
import 'package:hrms_tst/shared/widgets/filters_panel.dart';
import 'package:hrms_tst/shared/widgets/leave_history_tile.dart';
import 'package:hrms_tst/shared/widgets/month_range_selection.dart';
import 'package:hrms_tst/shared/widgets/no_data_view.dart';
import 'package:hrms_tst/shared/widgets/popup_filter_button.dart';
import 'package:hrms_tst/shared/widgets/screen_back_button.dart';

import '../domain/employee.controller.dart';
import 'widgets/policy_filter.dart';

class LeavePolicyScreen extends HookConsumerWidget {
  const LeavePolicyScreen({super.key, required this.leavePolicyId});

  static const String name = 'Leave Policy';
  static const String path = 'leave-policy/:id';

  static GoRoute route({List<RouteBase> routes = const []}) => GoRoute(
    path: path,
    name: name,
    routes: routes,
    pageBuilder: (context, state) => NoTransitionPage(
      child: LeavePolicyScreen(
        leavePolicyId: int.parse(state.pathParameters['id']!),
      ),
    ),
  );

  final int leavePolicyId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final leavePolicy = ref.watch(
      leavePolicyProvider(leavePolicyId: leavePolicyId),
    );

    final selectedTab = useState(PolicyFilter.policy);

    final startDate = useState<DateTime?>(null);
    final endDate = useState<DateTime?>(null);

    final leaveTypeFilter = useState<int?>(null);
    final leaveStatusFilter = useState<String?>(null);

    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: switch (leavePolicy) {
            AsyncData(:final value) => Column(
              children: [
                ScreenBackButton(),
                Space.y(16),
                PolicyFilterOptions(
                  selectedTab: selectedTab.value,
                  onTabSelected: (tab) {
                    selectedTab.value = tab;
                  },
                ),
                Space.y(20),
                Expanded(
                  child: CardContainer(
                    elevation: 0.5,
                    child: switch (selectedTab.value) {
                      PolicyFilter.policy => HookBuilder(
                        builder: (context) {
                          final selectedLeaveTypeAllotIndex = useState(0);
                          return Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                Text(
                                  value.name ?? '',
                                  style: context.textStyles.titleMedium
                                      ?.copyWith(
                                        fontSize: 20,
                                        fontWeight: FontWeight.w600,
                                        color: context.colors.onSurface,
                                      ),
                                ),
                                Space.y(16),
                                Row(
                                  spacing: 8,
                                  children: value.leaveTypeAllotments.indexed
                                      .map((e) {
                                        final isSelected =
                                            e.$1 ==
                                            selectedLeaveTypeAllotIndex.value;
                                        return Expanded(
                                          child: GestureDetector(
                                            onTap: () {
                                              selectedLeaveTypeAllotIndex
                                                      .value =
                                                  e.$1;
                                            },
                                            child: Container(
                                              alignment: Alignment.center,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                color: isSelected
                                                    ? context.colors.tertiary
                                                    : context.colors.tertiary
                                                          .withValues(
                                                            alpha: 0.1,
                                                          ),
                                              ),
                                              padding: EdgeInsets.symmetric(
                                                vertical: 8,
                                              ),
                                              child: Text(
                                                e.$2.leaveType?.name ?? '',
                                                style: context
                                                    .textStyles
                                                    .titleMedium
                                                    ?.copyWith(
                                                      fontSize: 10,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color: isSelected
                                                          ? context
                                                                .colors
                                                                .surface
                                                          : context
                                                                .colors
                                                                .secondary,
                                                    ),
                                              ),
                                            ),
                                          ),
                                        );
                                      })
                                      .toList(),
                                ),

                                Space.y(12),

                                Text(
                                  'Policy',
                                  style: context.textStyles.titleMedium
                                      ?.copyWith(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: context.colors.onSurface,
                                      ),
                                ),
                                Expanded(
                                  child: SingleChildScrollView(
                                    child: Column(
                                      children: [
                                        Builder(
                                          builder: (context) {
                                            final content = value
                                                .leaveTypeAllotments[selectedLeaveTypeAllotIndex
                                                    .value]
                                                .leaveType
                                                ?.policy;
                                            return HtmlView(
                                              htmlContent: content,
                                            );
                                          },
                                        ),
                                        HtmlView(htmlContent: value.policy),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                      PolicyFilter.requestHistory => switch (ref.watch(
                        leaveRequestRecordsProvider(
                          leaveTypeFilter.value,
                          leaveStatusFilter.value,
                          startDate.value,
                          endDate.value,
                        ),
                      )) {
                        AsyncData(:final value) => ListView(
                          children: [
                            Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(
                                    16.0,
                                  ).copyWith(bottom: 0),
                                  child: Row(
                                    children: [
                                      Text(
                                        'History',
                                        style: context.textStyles.titleMedium
                                            ?.copyWith(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500,
                                              color: context.colors.secondary,
                                            ),
                                      ),
                                    ],
                                  ),
                                ),
                                Space.y(12),
                                FiltersPanel(
                                  filters: [
                                    PopupFilterButton<int?>(
                                      filterName: 'Leave Type',
                                      initialValue: leaveTypeFilter.value,
                                      options: [1, 2, 3],
                                      stringValue: (value) =>
                                          leaveTypeFromId(value!),
                                      onSelected: (option) {
                                        leaveTypeFilter.value = option;
                                      },
                                    ),

                                    Space.x(6),
                                    PopupFilterButton(
                                      filterName: 'Status',
                                      initialValue: leaveStatusFilter.value,
                                      options: [
                                        'Approved',
                                        'Rejected',
                                        'Pending',
                                      ],
                                      stringValue: (value) => value,
                                      onSelected: (option) {
                                        leaveStatusFilter.value = option;
                                      },
                                    ),
                                  ],
                                  trailing: [
                                    MonthRangeSelection(
                                      filterButtonColor: context.colors.surface,
                                      startDate: startDate.value,
                                      endDate: endDate.value,
                                      onFilterSelected: (start, end) {
                                        startDate.value = start;
                                        endDate.value = end;

                                        context.pop();
                                      },
                                    ),
                                  ],
                                ),
                                Space.y(8),
                                if (value.isEmpty)
                                  Padding(
                                    padding: const EdgeInsets.all(24.0),
                                    child: NoDataView(),
                                  ),
                                ...value.map((record) {
                                  return Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                    ),
                                    child: LeaveHistoryTile(record: record),
                                  );
                                }),
                              ],
                            ),
                          ],
                        ),
                        AsyncError(:final error) => Center(
                          child: Text(error.toString()),
                        ),
                        _ => Center(
                          child: CircularProgressIndicator.adaptive(),
                        ),
                      },
                    },
                  ),
                ),
              ],
            ),
            AsyncError(:final error) => Center(child: Text(error.toString())),
            _ => Center(child: CircularProgressIndicator.adaptive()),
          },
        ),
      ),
    );
  }
}
