import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/features/employee/presentation/widgets/employee_tabs_options.dart';
import 'package:hrms_tst/features/employee/presentation/widgets/tabs/attendance_tab.dart';
import 'package:hrms_tst/features/employee/presentation/widgets/tabs/document_tab.dart';
import 'package:hrms_tst/features/employee/presentation/widgets/tabs/leave_tab.dart';

import 'widgets/tabs/my_progress_tab.dart';

class EmployeeScreen extends HookConsumerWidget {
  const EmployeeScreen({super.key});

  static const String name = 'Me';
  static const String path = '/me';

  static GoRoute route({List<RouteBase> routes = const []}) => GoRoute(
    path: path,
    name: name,
    routes: routes,
    pageBuilder: (context, state) =>
        const NoTransitionPage(child: EmployeeScreen()),
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedTab = useState(EmployeeTabs.attendance);

    final selectedDateFilter = useState(DateTime.now().toUtc());
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: EmployeeTabsOptions(
                selectedTab: selectedTab.value,
                onTabSelected: (tab) {
                  selectedTab.value = tab;
                },
              ),
            ),
            Expanded(
              child: switch (selectedTab.value) {
                EmployeeTabs.attendance => AttendanceTab(
                  selectedDateFilter: selectedDateFilter,
                ),
                EmployeeTabs.leave => LeaveTab(),
                EmployeeTabs.documents => DocumentTab(),
                EmployeeTabs.progress => MyProgressTab(),
              },
            ),
          ],
        ),
      ),
    );
  }
}
