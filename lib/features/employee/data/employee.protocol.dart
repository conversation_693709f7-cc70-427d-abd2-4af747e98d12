import 'dart:io';

import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/features/employee/data/models/attendance_tab.model.dart';
import 'package:hrms_tst/features/employee/data/models/leave_tab.model.dart';

import 'models/document.model.dart';

abstract interface class EmployeeProtocol {
  Future<Result<AttendanceTabModel>> getAttendenceData({
    required DateTime? dateFilter,
  });
  Future<Result<void>> requestRegularization({
    required int id,
    required DateTime clockIn,
    required DateTime clockOut,
    required String reason,
  });

  Future<Result<WorkPolicyModel>> getWorkPolicy({
    required int workPolicyId,
    required int timetablePolicyId,
  });

  Future<Result<List<LeaveRequestRecordModel>>> getLeaveRequestRecord({
    required int? leaveTypeId,
    required String? status,
    required DateTime? startDate,
    required DateTime? endDate,
  });
  Future<Result<LeavePolicyModel>> getLeavePolicy({required int leavePolicyId});

  Future<Result<List<DocumentCategoryModel>>> getDocumentCategories();
  Future<Result<List<SingleDocumentModel>>> getDocuments(int categoryId);

  Future<Result<void>> addBookProgressRecord({
    required String name,
    required DateTime completedAt,
    required String notes,
    required String docLink,
    required int pages,
  });
  Future<Result<void>> addCourseProgressRecord({
    required String name,
    required DateTime completedAt,
    required String courseLink,
    required String docLink,
    required String notes,
  });
  Future<Result<void>> addGDProgressRecord({
    required String name,
    required String topic,
    required DateTime completedAt,
    required String docLink,
    required String notes,
  });
  Future<Result<void>> addSessionProgressRecord({
    required String name,
    required String topic,
    required DateTime completedAt,
    required String docLink,
    required String notes,
  });
  Future<Result<void>> addEventProgressRecord({
    required String name,
    required DateTime startDate,
    required DateTime endDate,
    required String docLink,
    required List<File> images,
    required String notes,
  });

  Future<Result<void>> editBookProgressRecord({
    required int id,
    required String name,
    required DateTime completedAt,
    required String notes,
    required String docLink,
    required int pages,
  });
  Future<Result<void>> editCourseProgressRecord({
    required int id,
    required String name,
    required DateTime completedAt,
    required String courseLink,
    required String docLink,
    required String notes,
  });
  Future<Result<void>> editGDProgressRecord({
    required int id,
    required String name,
    required String topic,
    required DateTime completedAt,
    required String docLink,
    required String notes,
  });
  Future<Result<void>> editSessionProgressRecord({
    required int id,
    required String name,
    required String topic,
    required DateTime completedAt,
    required String docLink,
    required String notes,
  });
  Future<Result<void>> editEventProgressRecord({
    required int id,
    required String name,
    required DateTime startDate,
    required DateTime endDate,
    required String docLink,
    required String notes,
  });

  Future<Result<void>> deleteBookProgressRecord({required int id});
  Future<Result<void>> deleteCourseProgressRecord({required int id});
  Future<Result<void>> deleteEventProgressRecord({required int id});
  Future<Result<void>> deleteGDProgressRecord({required int id});
  Future<Result<void>> deleteSessionProgressRecord({required int id});
}
