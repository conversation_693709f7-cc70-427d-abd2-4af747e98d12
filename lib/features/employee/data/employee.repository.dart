import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:hrms_tst/core/services/network/network.service.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';

import 'package:hrms_tst/features/employee/data/models/attendance_tab.model.dart';
import 'package:hrms_tst/features/employee/data/models/document.model.dart';
import 'package:hrms_tst/features/employee/data/models/leave_tab.model.dart';

import 'employee.protocol.dart';

class EmployeeRepository implements EmployeeProtocol {
  final networkService = getIt.get<NetworkService>();

  @override
  Future<Result<AttendanceTabModel>> getAttendenceData({
    required DateTime? dateFilter,
  }) async {
    return await networkService.get(
      '/attendances/history',
      queryParameters: {
        if (dateFilter != null) 'date': dateFilter.toIso8601String(),
      },
      mapper: (response) {
        return AttendanceTabModel.fromJson(response.data['data']);
      },
    );
  }

  @override
  Future<Result<void>> requestRegularization({
    required int id,
    required DateTime clockIn,
    required DateTime clockOut,
    required String reason,
  }) {
    return networkService.post(
      '/attendances/regularize/${id.toString()}',
      data: {
        'clockIn': clockIn.toUtc().toIso8601String(),
        'clockOut': clockOut.toUtc().toIso8601String(),
        'reason': reason,
      },
      mapper: (response) {
        return;
      },
    );
  }

  @override
  Future<Result<WorkPolicyModel>> getWorkPolicy({
    required int workPolicyId,
    required int timetablePolicyId,
  }) async {
    final workShiftPolicy = await networkService.get(
      '/work-shifts/$workPolicyId',
      mapper: (response) {
        return WorkShiftPolicyModel.fromJson(
          response.data['data']['workShift'],
        );
      },
    );

    final timetablePolicy = await networkService.get(
      '/timetables/$timetablePolicyId',
      mapper: (response) {
        return TimetablePolicyModel.fromJson(
          response.data['data']['timetable'],
        );
      },
    );

    if (workShiftPolicy.isFailure || timetablePolicy.isFailure) {
      log(workShiftPolicy.failure.message);
      log(timetablePolicy.failure.message);
      return Failure('Unable to get policy');
    }

    return Success(
      WorkPolicyModel(
        timetablePolicy: timetablePolicy.success.data,
        workShiftPolicy: workShiftPolicy.success.data,
      ),
    );
  }

  @override
  Future<Result<List<LeaveRequestRecordModel>>> getLeaveRequestRecord({
    required int? leaveTypeId,
    required String? status,
    required DateTime? startDate,
    required DateTime? endDate,
  }) async {
    return await networkService.get(
      '/leave-wfh-requests/my',
      queryParameters: {
        'type': 'leave',
        if (leaveTypeId != null) 'LeaveTypeId': leaveTypeId,
        if (status != null) 'status': status,
        if (startDate != null) 'fromDate': startDate.toIso8601String(),
        if (endDate != null) 'toDate': endDate.toIso8601String(),
      },
      mapper: (response) {
        return (response.data['data']['leaveWfhRequestRecords']
                as List<dynamic>)
            .map((e) => LeaveRequestRecordModel.fromJson(e))
            .toList();
      },
    );
  }

  @override
  Future<Result<LeavePolicyModel>> getLeavePolicy({
    required int leavePolicyId,
  }) async {
    return await networkService.get(
      '/leave-policies/$leavePolicyId',
      mapper: (response) {
        return LeavePolicyModel.fromJson(response.data['data']['leavePolicy']);
      },
    );
  }

  @override
  Future<Result<List<DocumentCategoryModel>>> getDocumentCategories() async {
    return await networkService.get(
      '/doc-categories',
      mapper: (response) {
        return (response.data['data'] as List<dynamic>)
            .map((e) => DocumentCategoryModel.fromJson(e))
            .toList();
      },
    );
  }

  @override
  Future<Result<List<SingleDocumentModel>>> getDocuments(int categoryId) async {
    return await networkService.get(
      '/documents/my-user-docs',
      queryParameters: {'DocCategoryId': categoryId},
      mapper: (response) {
        return (response.data['data']['rows'] as List<dynamic>)
            .map((e) => SingleDocumentModel.fromJson(e))
            .where((element) => element.data != null)
            .toList();
      },
    );
  }

  @override
  Future<Result<void>> addBookProgressRecord({
    required String name,
    required DateTime completedAt,
    required String notes,
    required String docLink,
    required int pages,
  }) async {
    return await networkService.post(
      '/book-summaries',
      data: {
        'name': name,
        'completedAt': completedAt.toIso8601String(),
        'notes': notes,
        'docLink': docLink,
        'pages': pages,
      },
      mapper: (response) {
        return;
      },
    );
  }

  @override
  Future<Result<void>> addCourseProgressRecord({
    required String name,
    required DateTime completedAt,
    required String courseLink,
    required String docLink,
    required String notes,
  }) async {
    return networkService.post(
      '/course-summaries',
      data: {
        'name': name,
        'completedAt': completedAt.toIso8601String(),
        'notes': notes,
        'courseLink': courseLink,
        'docLink': docLink,
      },
      mapper: (response) {
        return;
      },
    );
  }

  @override
  Future<Result<void>> addGDProgressRecord({
    required String name,
    required String topic,
    required DateTime completedAt,
    required String docLink,
    required String notes,
  }) async {
    return networkService.post(
      '/gd-summaries',
      data: {
        'name': name,
        'topic': topic,
        'completedAt': completedAt.toIso8601String(),
        'notes': notes,
        'docLink': docLink,
      },
      mapper: (response) {
        return;
      },
    );
  }

  @override
  Future<Result<void>> addSessionProgressRecord({
    required String name,
    required String topic,
    required DateTime completedAt,
    required String docLink,
    required String notes,
  }) async {
    return networkService.post(
      '/session-summaries',
      data: {
        'name': name,
        'topic': topic,
        'completedAt': completedAt.toIso8601String(),
        'notes': notes,
        'docLink': docLink,
      },
      mapper: (response) {
        return;
      },
    );
  }

  @override
  Future<Result<void>> addEventProgressRecord({
    required String name,
    required DateTime startDate,
    required DateTime endDate,
    required String docLink,
    required List<File> images,
    required String notes,
  }) async {
    final imageBinaries = await Future.wait(
      images.map(
        (e) => MultipartFile.fromFile(e.path, filename: e.path.split('/').last),
      ),
    );
    return networkService.post(
      '/event-summaries',
      data: FormData.fromMap({
        'name': name,
        'startDate': startDate.toIso8601String(),
        'endDate': endDate.toIso8601String(),
        'link': docLink,
        'image': imageBinaries,
        'notes': notes,
      }),
      mapper: (response) {
        return;
      },
    );
  }

  @override
  Future<Result<void>> editBookProgressRecord({
    required int id,
    required String name,
    required DateTime completedAt,
    required String notes,
    required String docLink,
    required int pages,
  }) async {
    return await networkService.patch(
      '/book-summaries/${id.toString()}',
      data: {
        'name': name,
        'completedAt': completedAt.toIso8601String(),
        'notes': notes,
        'docLink': docLink,
        'pages': pages,
      },
      mapper: (response) {
        return;
      },
    );
  }

  @override
  Future<Result<void>> editCourseProgressRecord({
    required int id,
    required String name,
    required DateTime completedAt,
    required String courseLink,
    required String docLink,
    required String notes,
  }) async {
    return networkService.patch(
      '/course-summaries/${id.toString()}',
      data: {
        'name': name,
        'completedAt': completedAt.toIso8601String(),
        'notes': notes,
        'courseLink': courseLink,
        'docLink': docLink,
      },
      mapper: (response) {
        return;
      },
    );
  }

  @override
  Future<Result<void>> editEventProgressRecord({
    required int id,
    required String name,
    required DateTime startDate,
    required DateTime endDate,
    required String docLink,
    required String notes,
  }) async {
    return networkService.patch(
      '/event-summaries/${id.toString()}',
      data: {
        'name': name,
        'startDate': startDate.toIso8601String(),
        'endDate': endDate.toIso8601String(),
        'link': docLink,
        'notes': notes,
      },
      mapper: (response) {
        return;
      },
    );
  }

  @override
  Future<Result<void>> editGDProgressRecord({
    required int id,
    required String name,
    required String topic,
    required DateTime completedAt,
    required String docLink,
    required String notes,
  }) async {
    return networkService.patch(
      '/gd-summaries/${id.toString()}',
      data: {
        'name': name,
        'topic': topic,
        'completedAt': completedAt.toIso8601String(),
        'notes': notes,
        'docLink': docLink,
      },
      mapper: (response) {
        return;
      },
    );
  }

  @override
  Future<Result<void>> editSessionProgressRecord({
    required int id,
    required String name,
    required String topic,
    required DateTime completedAt,
    required String docLink,
    required String notes,
  }) async {
    return networkService.patch(
      '/session-summaries/${id.toString()}',
      data: {
        'name': name,
        'topic': topic,
        'completedAt': completedAt.toIso8601String(),
        'notes': notes,
        'docLink': docLink,
      },
      mapper: (response) {
        return;
      },
    );
  }

  @override
  Future<Result<void>> deleteBookProgressRecord({required int id}) async {
    return await networkService.delete(
      '/book-summaries/${id.toString()}',
      mapper: (response) {
        return;
      },
    );
  }

  @override
  Future<Result<void>> deleteCourseProgressRecord({required int id}) async {
    return networkService.delete(
      '/course-summaries/${id.toString()}',

      mapper: (response) {
        return;
      },
    );
  }

  @override
  Future<Result<void>> deleteEventProgressRecord({required int id}) async {
    return networkService.delete(
      '/event-summaries/${id.toString()}',
      mapper: (response) {
        return;
      },
    );
  }

  @override
  Future<Result<void>> deleteGDProgressRecord({required int id}) async {
    return networkService.delete(
      '/gd-summaries/${id.toString()}',
      mapper: (response) {
        return;
      },
    );
  }

  @override
  Future<Result<void>> deleteSessionProgressRecord({required int id}) async {
    return networkService.delete(
      '/session-summaries/${id.toString()}',
      mapper: (response) {
        return;
      },
    );
  }
}
