import 'package:freezed_annotation/freezed_annotation.dart';

part 'attendance_tab.model.freezed.dart';
part 'attendance_tab.model.g.dart';

List<AttendanceHistoryModel> attendanceHistoryFromJson(
  Map<String, dynamic> json,
) => (json['attendanceRecords'] as List<dynamic>)
    .map((e) => AttendanceHistoryModel.fromJson(e))
    .toList();

@freezed
abstract class AttendanceTabModel with _$AttendanceTabModel {
  @JsonSerializable(explicitToJson: true)
  const factory AttendanceTabModel({
    final WorkShiftModel? workShift,
    final TimeTableDataModel? timetableData,
    @J<PERSON><PERSON>ey(name: 'history', fromJson: attendanceHistoryFromJson)
    final List<AttendanceHistoryModel>? attendanceHistory,
  }) = _AttendanceTabModel;

  factory AttendanceTabModel.fromJson(Map<String, dynamic> json) =>
      _$AttendanceTabModelFromJson(json);
}

@freezed
abstract class WorkShiftModel with _$WorkShiftModel {
  const factory WorkShiftModel({
    required final int id,
    final String? name,
    final String? startTime,
    final String? endTime,
  }) = _WorkShiftModel;

  factory WorkShiftModel.fromJson(Map<String, dynamic> json) =>
      _$WorkShiftModelFromJson(json);
}

@freezed
abstract class TimeTableDataModel with _$TimeTableDataModel {
  const factory TimeTableDataModel({
    required final int id,
    final String? name,
    final String? firstWorkingDay,
    final String? lastWorkingDay,
  }) = _TimeTableDataModel;

  factory TimeTableDataModel.fromJson(Map<String, dynamic> json) =>
      _$TimeTableDataModelFromJson(json);
}

@freezed
abstract class AttendanceHistoryModel with _$AttendanceHistoryModel {
  const factory AttendanceHistoryModel({
    required final int id,
    required final String status,
    final String? clockInStatus,
    final String? clockOutStatus,
    final DateTime? clockIn,
    final DateTime? clockOut,
    final double? lat,
    final double? long,
    final double? hoursOverTime,
    final double? hoursWorked,
    final String? regularization,
    final RegularizationTimes? regularizationTimes,
    final DateTime? createdAt,
    final Map<String, dynamic>? leaveWfhRequest,
    final Map<String, dynamic>? holiday,
  }) = _AttendanceHistoryModel;

  factory AttendanceHistoryModel.fromJson(Map<String, dynamic> json) =>
      _$AttendanceHistoryModelFromJson(json);
}

@freezed
abstract class RegularizationTimes with _$RegularizationTimes {
  const factory RegularizationTimes({
    final DateTime? clockIn,
    final DateTime? clockOut,
  }) = _RegularizationTimes;

  factory RegularizationTimes.fromJson(Map<String, dynamic> json) =>
      _$RegularizationTimesFromJson(json);
}

@freezed
abstract class WorkPolicyModel with _$WorkPolicyModel {
  const factory WorkPolicyModel({
    final WorkShiftPolicyModel? workShiftPolicy,
    final TimetablePolicyModel? timetablePolicy,
  }) = _WorkPolicyModel;

  factory WorkPolicyModel.fromJson(Map<String, dynamic> json) =>
      _$WorkPolicyModelFromJson(json);
}

@freezed
abstract class WorkShiftPolicyModel with _$WorkShiftPolicyModel {
  const factory WorkShiftPolicyModel({
    required final int id,
    final String? name,
    final String? startTime,
    final String? endTime,
    final String? breakDuration,
    final String? policy,
    final DateTime? createdAt,
    final DateTime? deletedAt,
    final DateTime? removedAt,
    @JsonKey(name: 'OfficeId') final int? officeId,
  }) = _WorkShiftPolicyModel;

  factory WorkShiftPolicyModel.fromJson(Map<String, dynamic> json) =>
      _$WorkShiftPolicyModelFromJson(json);
}

@freezed
abstract class TimetablePolicyModel with _$TimetablePolicyModel {
  const factory TimetablePolicyModel({
    required final int id,
    final String? name,
    final double? sun,
    final double? mon,
    final double? tue,
    final double? wed,
    final double? thu,
    final double? fri,
    final double? sat,
    final String? policy,
    final DateTime? createdAt,
    final DateTime? deletedAt,
    final DateTime? removedAt,
    @JsonKey(name: 'OfficeId') final int? officeId,
  }) = _TimetablePolicyModel;

  factory TimetablePolicyModel.fromJson(Map<String, dynamic> json) =>
      _$TimetablePolicyModelFromJson(json);
}
