// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attendance_tab.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AttendanceTabModel _$AttendanceTabModelFromJson(Map<String, dynamic> json) =>
    _AttendanceTabModel(
      workShift: json['workShift'] == null
          ? null
          : WorkShiftModel.fromJson(json['workShift'] as Map<String, dynamic>),
      timetableData: json['timetableData'] == null
          ? null
          : TimeTableDataModel.fromJson(
              json['timetableData'] as Map<String, dynamic>,
            ),
      attendanceHistory: attendanceHistoryFromJson(
        json['history'] as Map<String, dynamic>,
      ),
    );

Map<String, dynamic> _$AttendanceTabModelToJson(_AttendanceTabModel instance) =>
    <String, dynamic>{
      'workShift': instance.workShift?.toJson(),
      'timetableData': instance.timetableData?.toJson(),
      'history': instance.attendanceHistory?.map((e) => e.toJson()).toList(),
    };

_WorkShiftModel _$WorkShiftModelFromJson(Map<String, dynamic> json) =>
    _WorkShiftModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String?,
      startTime: json['startTime'] as String?,
      endTime: json['endTime'] as String?,
    );

Map<String, dynamic> _$WorkShiftModelToJson(_WorkShiftModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
    };

_TimeTableDataModel _$TimeTableDataModelFromJson(Map<String, dynamic> json) =>
    _TimeTableDataModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String?,
      firstWorkingDay: json['firstWorkingDay'] as String?,
      lastWorkingDay: json['lastWorkingDay'] as String?,
    );

Map<String, dynamic> _$TimeTableDataModelToJson(_TimeTableDataModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'firstWorkingDay': instance.firstWorkingDay,
      'lastWorkingDay': instance.lastWorkingDay,
    };

_AttendanceHistoryModel _$AttendanceHistoryModelFromJson(
  Map<String, dynamic> json,
) => _AttendanceHistoryModel(
  id: (json['id'] as num).toInt(),
  status: json['status'] as String,
  clockInStatus: json['clockInStatus'] as String?,
  clockOutStatus: json['clockOutStatus'] as String?,
  clockIn: json['clockIn'] == null
      ? null
      : DateTime.parse(json['clockIn'] as String),
  clockOut: json['clockOut'] == null
      ? null
      : DateTime.parse(json['clockOut'] as String),
  lat: (json['lat'] as num?)?.toDouble(),
  long: (json['long'] as num?)?.toDouble(),
  hoursOverTime: (json['hoursOverTime'] as num?)?.toDouble(),
  hoursWorked: (json['hoursWorked'] as num?)?.toDouble(),
  regularization: json['regularization'] as String?,
  regularizationTimes: json['regularizationTimes'] == null
      ? null
      : RegularizationTimes.fromJson(
          json['regularizationTimes'] as Map<String, dynamic>,
        ),
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  leaveWfhRequest: json['leaveWfhRequest'] as Map<String, dynamic>?,
  holiday: json['holiday'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$AttendanceHistoryModelToJson(
  _AttendanceHistoryModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'status': instance.status,
  'clockInStatus': instance.clockInStatus,
  'clockOutStatus': instance.clockOutStatus,
  'clockIn': instance.clockIn?.toIso8601String(),
  'clockOut': instance.clockOut?.toIso8601String(),
  'lat': instance.lat,
  'long': instance.long,
  'hoursOverTime': instance.hoursOverTime,
  'hoursWorked': instance.hoursWorked,
  'regularization': instance.regularization,
  'regularizationTimes': instance.regularizationTimes,
  'createdAt': instance.createdAt?.toIso8601String(),
  'leaveWfhRequest': instance.leaveWfhRequest,
  'holiday': instance.holiday,
};

_RegularizationTimes _$RegularizationTimesFromJson(Map<String, dynamic> json) =>
    _RegularizationTimes(
      clockIn: json['clockIn'] == null
          ? null
          : DateTime.parse(json['clockIn'] as String),
      clockOut: json['clockOut'] == null
          ? null
          : DateTime.parse(json['clockOut'] as String),
    );

Map<String, dynamic> _$RegularizationTimesToJson(
  _RegularizationTimes instance,
) => <String, dynamic>{
  'clockIn': instance.clockIn?.toIso8601String(),
  'clockOut': instance.clockOut?.toIso8601String(),
};

_WorkPolicyModel _$WorkPolicyModelFromJson(Map<String, dynamic> json) =>
    _WorkPolicyModel(
      workShiftPolicy: json['workShiftPolicy'] == null
          ? null
          : WorkShiftPolicyModel.fromJson(
              json['workShiftPolicy'] as Map<String, dynamic>,
            ),
      timetablePolicy: json['timetablePolicy'] == null
          ? null
          : TimetablePolicyModel.fromJson(
              json['timetablePolicy'] as Map<String, dynamic>,
            ),
    );

Map<String, dynamic> _$WorkPolicyModelToJson(_WorkPolicyModel instance) =>
    <String, dynamic>{
      'workShiftPolicy': instance.workShiftPolicy,
      'timetablePolicy': instance.timetablePolicy,
    };

_WorkShiftPolicyModel _$WorkShiftPolicyModelFromJson(
  Map<String, dynamic> json,
) => _WorkShiftPolicyModel(
  id: (json['id'] as num).toInt(),
  name: json['name'] as String?,
  startTime: json['startTime'] as String?,
  endTime: json['endTime'] as String?,
  breakDuration: json['breakDuration'] as String?,
  policy: json['policy'] as String?,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  deletedAt: json['deletedAt'] == null
      ? null
      : DateTime.parse(json['deletedAt'] as String),
  removedAt: json['removedAt'] == null
      ? null
      : DateTime.parse(json['removedAt'] as String),
  officeId: (json['OfficeId'] as num?)?.toInt(),
);

Map<String, dynamic> _$WorkShiftPolicyModelToJson(
  _WorkShiftPolicyModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'startTime': instance.startTime,
  'endTime': instance.endTime,
  'breakDuration': instance.breakDuration,
  'policy': instance.policy,
  'createdAt': instance.createdAt?.toIso8601String(),
  'deletedAt': instance.deletedAt?.toIso8601String(),
  'removedAt': instance.removedAt?.toIso8601String(),
  'OfficeId': instance.officeId,
};

_TimetablePolicyModel _$TimetablePolicyModelFromJson(
  Map<String, dynamic> json,
) => _TimetablePolicyModel(
  id: (json['id'] as num).toInt(),
  name: json['name'] as String?,
  sun: (json['sun'] as num?)?.toDouble(),
  mon: (json['mon'] as num?)?.toDouble(),
  tue: (json['tue'] as num?)?.toDouble(),
  wed: (json['wed'] as num?)?.toDouble(),
  thu: (json['thu'] as num?)?.toDouble(),
  fri: (json['fri'] as num?)?.toDouble(),
  sat: (json['sat'] as num?)?.toDouble(),
  policy: json['policy'] as String?,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  deletedAt: json['deletedAt'] == null
      ? null
      : DateTime.parse(json['deletedAt'] as String),
  removedAt: json['removedAt'] == null
      ? null
      : DateTime.parse(json['removedAt'] as String),
  officeId: (json['OfficeId'] as num?)?.toInt(),
);

Map<String, dynamic> _$TimetablePolicyModelToJson(
  _TimetablePolicyModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'sun': instance.sun,
  'mon': instance.mon,
  'tue': instance.tue,
  'wed': instance.wed,
  'thu': instance.thu,
  'fri': instance.fri,
  'sat': instance.sat,
  'policy': instance.policy,
  'createdAt': instance.createdAt?.toIso8601String(),
  'deletedAt': instance.deletedAt?.toIso8601String(),
  'removedAt': instance.removedAt?.toIso8601String(),
  'OfficeId': instance.officeId,
};
