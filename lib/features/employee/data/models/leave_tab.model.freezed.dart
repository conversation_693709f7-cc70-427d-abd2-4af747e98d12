// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'leave_tab.model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LeaveRequestRecordModel {

 int get id; String? get type; DateTime? get startDate; DateTime? get endDate; String? get startDaySlot; String? get endDaySlot; String? get reason; String? get rejectReason; String? get status; DateTime? get createdAt; LeaveType? get leaveType;@JsonKey(name: 'reviewer', fromJson: reviewerFromJson) LeaveReviewerModel? get reviewer;
/// Create a copy of LeaveRequestRecordModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LeaveRequestRecordModelCopyWith<LeaveRequestRecordModel> get copyWith => _$LeaveRequestRecordModelCopyWithImpl<LeaveRequestRecordModel>(this as LeaveRequestRecordModel, _$identity);

  /// Serializes this LeaveRequestRecordModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LeaveRequestRecordModel&&(identical(other.id, id) || other.id == id)&&(identical(other.type, type) || other.type == type)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.startDaySlot, startDaySlot) || other.startDaySlot == startDaySlot)&&(identical(other.endDaySlot, endDaySlot) || other.endDaySlot == endDaySlot)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.rejectReason, rejectReason) || other.rejectReason == rejectReason)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.leaveType, leaveType) || other.leaveType == leaveType)&&(identical(other.reviewer, reviewer) || other.reviewer == reviewer));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,type,startDate,endDate,startDaySlot,endDaySlot,reason,rejectReason,status,createdAt,leaveType,reviewer);

@override
String toString() {
  return 'LeaveRequestRecordModel(id: $id, type: $type, startDate: $startDate, endDate: $endDate, startDaySlot: $startDaySlot, endDaySlot: $endDaySlot, reason: $reason, rejectReason: $rejectReason, status: $status, createdAt: $createdAt, leaveType: $leaveType, reviewer: $reviewer)';
}


}

/// @nodoc
abstract mixin class $LeaveRequestRecordModelCopyWith<$Res>  {
  factory $LeaveRequestRecordModelCopyWith(LeaveRequestRecordModel value, $Res Function(LeaveRequestRecordModel) _then) = _$LeaveRequestRecordModelCopyWithImpl;
@useResult
$Res call({
 int id, String? type, DateTime? startDate, DateTime? endDate, String? startDaySlot, String? endDaySlot, String? reason, String? rejectReason, String? status, DateTime? createdAt, LeaveType? leaveType,@JsonKey(name: 'reviewer', fromJson: reviewerFromJson) LeaveReviewerModel? reviewer
});


$LeaveTypeCopyWith<$Res>? get leaveType;$LeaveReviewerModelCopyWith<$Res>? get reviewer;

}
/// @nodoc
class _$LeaveRequestRecordModelCopyWithImpl<$Res>
    implements $LeaveRequestRecordModelCopyWith<$Res> {
  _$LeaveRequestRecordModelCopyWithImpl(this._self, this._then);

  final LeaveRequestRecordModel _self;
  final $Res Function(LeaveRequestRecordModel) _then;

/// Create a copy of LeaveRequestRecordModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? type = freezed,Object? startDate = freezed,Object? endDate = freezed,Object? startDaySlot = freezed,Object? endDaySlot = freezed,Object? reason = freezed,Object? rejectReason = freezed,Object? status = freezed,Object? createdAt = freezed,Object? leaveType = freezed,Object? reviewer = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,type: freezed == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String?,startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,startDaySlot: freezed == startDaySlot ? _self.startDaySlot : startDaySlot // ignore: cast_nullable_to_non_nullable
as String?,endDaySlot: freezed == endDaySlot ? _self.endDaySlot : endDaySlot // ignore: cast_nullable_to_non_nullable
as String?,reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,rejectReason: freezed == rejectReason ? _self.rejectReason : rejectReason // ignore: cast_nullable_to_non_nullable
as String?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,leaveType: freezed == leaveType ? _self.leaveType : leaveType // ignore: cast_nullable_to_non_nullable
as LeaveType?,reviewer: freezed == reviewer ? _self.reviewer : reviewer // ignore: cast_nullable_to_non_nullable
as LeaveReviewerModel?,
  ));
}
/// Create a copy of LeaveRequestRecordModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeaveTypeCopyWith<$Res>? get leaveType {
    if (_self.leaveType == null) {
    return null;
  }

  return $LeaveTypeCopyWith<$Res>(_self.leaveType!, (value) {
    return _then(_self.copyWith(leaveType: value));
  });
}/// Create a copy of LeaveRequestRecordModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeaveReviewerModelCopyWith<$Res>? get reviewer {
    if (_self.reviewer == null) {
    return null;
  }

  return $LeaveReviewerModelCopyWith<$Res>(_self.reviewer!, (value) {
    return _then(_self.copyWith(reviewer: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _LeaveRequestRecordModel implements LeaveRequestRecordModel {
  const _LeaveRequestRecordModel({required this.id, this.type, this.startDate, this.endDate, this.startDaySlot, this.endDaySlot, this.reason, this.rejectReason, this.status, this.createdAt, this.leaveType, @JsonKey(name: 'reviewer', fromJson: reviewerFromJson) this.reviewer});
  factory _LeaveRequestRecordModel.fromJson(Map<String, dynamic> json) => _$LeaveRequestRecordModelFromJson(json);

@override final  int id;
@override final  String? type;
@override final  DateTime? startDate;
@override final  DateTime? endDate;
@override final  String? startDaySlot;
@override final  String? endDaySlot;
@override final  String? reason;
@override final  String? rejectReason;
@override final  String? status;
@override final  DateTime? createdAt;
@override final  LeaveType? leaveType;
@override@JsonKey(name: 'reviewer', fromJson: reviewerFromJson) final  LeaveReviewerModel? reviewer;

/// Create a copy of LeaveRequestRecordModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LeaveRequestRecordModelCopyWith<_LeaveRequestRecordModel> get copyWith => __$LeaveRequestRecordModelCopyWithImpl<_LeaveRequestRecordModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LeaveRequestRecordModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LeaveRequestRecordModel&&(identical(other.id, id) || other.id == id)&&(identical(other.type, type) || other.type == type)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.startDaySlot, startDaySlot) || other.startDaySlot == startDaySlot)&&(identical(other.endDaySlot, endDaySlot) || other.endDaySlot == endDaySlot)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.rejectReason, rejectReason) || other.rejectReason == rejectReason)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.leaveType, leaveType) || other.leaveType == leaveType)&&(identical(other.reviewer, reviewer) || other.reviewer == reviewer));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,type,startDate,endDate,startDaySlot,endDaySlot,reason,rejectReason,status,createdAt,leaveType,reviewer);

@override
String toString() {
  return 'LeaveRequestRecordModel(id: $id, type: $type, startDate: $startDate, endDate: $endDate, startDaySlot: $startDaySlot, endDaySlot: $endDaySlot, reason: $reason, rejectReason: $rejectReason, status: $status, createdAt: $createdAt, leaveType: $leaveType, reviewer: $reviewer)';
}


}

/// @nodoc
abstract mixin class _$LeaveRequestRecordModelCopyWith<$Res> implements $LeaveRequestRecordModelCopyWith<$Res> {
  factory _$LeaveRequestRecordModelCopyWith(_LeaveRequestRecordModel value, $Res Function(_LeaveRequestRecordModel) _then) = __$LeaveRequestRecordModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String? type, DateTime? startDate, DateTime? endDate, String? startDaySlot, String? endDaySlot, String? reason, String? rejectReason, String? status, DateTime? createdAt, LeaveType? leaveType,@JsonKey(name: 'reviewer', fromJson: reviewerFromJson) LeaveReviewerModel? reviewer
});


@override $LeaveTypeCopyWith<$Res>? get leaveType;@override $LeaveReviewerModelCopyWith<$Res>? get reviewer;

}
/// @nodoc
class __$LeaveRequestRecordModelCopyWithImpl<$Res>
    implements _$LeaveRequestRecordModelCopyWith<$Res> {
  __$LeaveRequestRecordModelCopyWithImpl(this._self, this._then);

  final _LeaveRequestRecordModel _self;
  final $Res Function(_LeaveRequestRecordModel) _then;

/// Create a copy of LeaveRequestRecordModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? type = freezed,Object? startDate = freezed,Object? endDate = freezed,Object? startDaySlot = freezed,Object? endDaySlot = freezed,Object? reason = freezed,Object? rejectReason = freezed,Object? status = freezed,Object? createdAt = freezed,Object? leaveType = freezed,Object? reviewer = freezed,}) {
  return _then(_LeaveRequestRecordModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,type: freezed == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String?,startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,startDaySlot: freezed == startDaySlot ? _self.startDaySlot : startDaySlot // ignore: cast_nullable_to_non_nullable
as String?,endDaySlot: freezed == endDaySlot ? _self.endDaySlot : endDaySlot // ignore: cast_nullable_to_non_nullable
as String?,reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,rejectReason: freezed == rejectReason ? _self.rejectReason : rejectReason // ignore: cast_nullable_to_non_nullable
as String?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,leaveType: freezed == leaveType ? _self.leaveType : leaveType // ignore: cast_nullable_to_non_nullable
as LeaveType?,reviewer: freezed == reviewer ? _self.reviewer : reviewer // ignore: cast_nullable_to_non_nullable
as LeaveReviewerModel?,
  ));
}

/// Create a copy of LeaveRequestRecordModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeaveTypeCopyWith<$Res>? get leaveType {
    if (_self.leaveType == null) {
    return null;
  }

  return $LeaveTypeCopyWith<$Res>(_self.leaveType!, (value) {
    return _then(_self.copyWith(leaveType: value));
  });
}/// Create a copy of LeaveRequestRecordModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeaveReviewerModelCopyWith<$Res>? get reviewer {
    if (_self.reviewer == null) {
    return null;
  }

  return $LeaveReviewerModelCopyWith<$Res>(_self.reviewer!, (value) {
    return _then(_self.copyWith(reviewer: value));
  });
}
}


/// @nodoc
mixin _$LeaveReviewerModel {

 int? get id; String? get displayName;
/// Create a copy of LeaveReviewerModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LeaveReviewerModelCopyWith<LeaveReviewerModel> get copyWith => _$LeaveReviewerModelCopyWithImpl<LeaveReviewerModel>(this as LeaveReviewerModel, _$identity);

  /// Serializes this LeaveReviewerModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LeaveReviewerModel&&(identical(other.id, id) || other.id == id)&&(identical(other.displayName, displayName) || other.displayName == displayName));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,displayName);

@override
String toString() {
  return 'LeaveReviewerModel(id: $id, displayName: $displayName)';
}


}

/// @nodoc
abstract mixin class $LeaveReviewerModelCopyWith<$Res>  {
  factory $LeaveReviewerModelCopyWith(LeaveReviewerModel value, $Res Function(LeaveReviewerModel) _then) = _$LeaveReviewerModelCopyWithImpl;
@useResult
$Res call({
 int? id, String? displayName
});




}
/// @nodoc
class _$LeaveReviewerModelCopyWithImpl<$Res>
    implements $LeaveReviewerModelCopyWith<$Res> {
  _$LeaveReviewerModelCopyWithImpl(this._self, this._then);

  final LeaveReviewerModel _self;
  final $Res Function(LeaveReviewerModel) _then;

/// Create a copy of LeaveReviewerModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? displayName = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,displayName: freezed == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _LeaveReviewerModel implements LeaveReviewerModel {
  const _LeaveReviewerModel({this.id, this.displayName});
  factory _LeaveReviewerModel.fromJson(Map<String, dynamic> json) => _$LeaveReviewerModelFromJson(json);

@override final  int? id;
@override final  String? displayName;

/// Create a copy of LeaveReviewerModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LeaveReviewerModelCopyWith<_LeaveReviewerModel> get copyWith => __$LeaveReviewerModelCopyWithImpl<_LeaveReviewerModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LeaveReviewerModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LeaveReviewerModel&&(identical(other.id, id) || other.id == id)&&(identical(other.displayName, displayName) || other.displayName == displayName));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,displayName);

@override
String toString() {
  return 'LeaveReviewerModel(id: $id, displayName: $displayName)';
}


}

/// @nodoc
abstract mixin class _$LeaveReviewerModelCopyWith<$Res> implements $LeaveReviewerModelCopyWith<$Res> {
  factory _$LeaveReviewerModelCopyWith(_LeaveReviewerModel value, $Res Function(_LeaveReviewerModel) _then) = __$LeaveReviewerModelCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? displayName
});




}
/// @nodoc
class __$LeaveReviewerModelCopyWithImpl<$Res>
    implements _$LeaveReviewerModelCopyWith<$Res> {
  __$LeaveReviewerModelCopyWithImpl(this._self, this._then);

  final _LeaveReviewerModel _self;
  final $Res Function(_LeaveReviewerModel) _then;

/// Create a copy of LeaveReviewerModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? displayName = freezed,}) {
  return _then(_LeaveReviewerModel(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,displayName: freezed == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$LeavePolicyModel {

 int get id; String? get name; String? get policy; String? get gender; DateTime? get createdAt; DateTime? get deletedAt; DateTime? get removedAt;@JsonKey(name: 'OfficeId') int? get officeId;@JsonKey(name: 'leaveTypeAllots') List<LeaveAllotmentModel> get leaveTypeAllotments;
/// Create a copy of LeavePolicyModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LeavePolicyModelCopyWith<LeavePolicyModel> get copyWith => _$LeavePolicyModelCopyWithImpl<LeavePolicyModel>(this as LeavePolicyModel, _$identity);

  /// Serializes this LeavePolicyModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LeavePolicyModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.policy, policy) || other.policy == policy)&&(identical(other.gender, gender) || other.gender == gender)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt)&&(identical(other.removedAt, removedAt) || other.removedAt == removedAt)&&(identical(other.officeId, officeId) || other.officeId == officeId)&&const DeepCollectionEquality().equals(other.leaveTypeAllotments, leaveTypeAllotments));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,policy,gender,createdAt,deletedAt,removedAt,officeId,const DeepCollectionEquality().hash(leaveTypeAllotments));

@override
String toString() {
  return 'LeavePolicyModel(id: $id, name: $name, policy: $policy, gender: $gender, createdAt: $createdAt, deletedAt: $deletedAt, removedAt: $removedAt, officeId: $officeId, leaveTypeAllotments: $leaveTypeAllotments)';
}


}

/// @nodoc
abstract mixin class $LeavePolicyModelCopyWith<$Res>  {
  factory $LeavePolicyModelCopyWith(LeavePolicyModel value, $Res Function(LeavePolicyModel) _then) = _$LeavePolicyModelCopyWithImpl;
@useResult
$Res call({
 int id, String? name, String? policy, String? gender, DateTime? createdAt, DateTime? deletedAt, DateTime? removedAt,@JsonKey(name: 'OfficeId') int? officeId,@JsonKey(name: 'leaveTypeAllots') List<LeaveAllotmentModel> leaveTypeAllotments
});




}
/// @nodoc
class _$LeavePolicyModelCopyWithImpl<$Res>
    implements $LeavePolicyModelCopyWith<$Res> {
  _$LeavePolicyModelCopyWithImpl(this._self, this._then);

  final LeavePolicyModel _self;
  final $Res Function(LeavePolicyModel) _then;

/// Create a copy of LeavePolicyModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = freezed,Object? policy = freezed,Object? gender = freezed,Object? createdAt = freezed,Object? deletedAt = freezed,Object? removedAt = freezed,Object? officeId = freezed,Object? leaveTypeAllotments = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,policy: freezed == policy ? _self.policy : policy // ignore: cast_nullable_to_non_nullable
as String?,gender: freezed == gender ? _self.gender : gender // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,removedAt: freezed == removedAt ? _self.removedAt : removedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,officeId: freezed == officeId ? _self.officeId : officeId // ignore: cast_nullable_to_non_nullable
as int?,leaveTypeAllotments: null == leaveTypeAllotments ? _self.leaveTypeAllotments : leaveTypeAllotments // ignore: cast_nullable_to_non_nullable
as List<LeaveAllotmentModel>,
  ));
}

}


/// @nodoc

@JsonSerializable(explicitToJson: true)
class _LeavePolicyModel implements LeavePolicyModel {
  const _LeavePolicyModel({required this.id, this.name, this.policy, this.gender, this.createdAt, this.deletedAt, this.removedAt, @JsonKey(name: 'OfficeId') this.officeId, @JsonKey(name: 'leaveTypeAllots') required final  List<LeaveAllotmentModel> leaveTypeAllotments}): _leaveTypeAllotments = leaveTypeAllotments;
  factory _LeavePolicyModel.fromJson(Map<String, dynamic> json) => _$LeavePolicyModelFromJson(json);

@override final  int id;
@override final  String? name;
@override final  String? policy;
@override final  String? gender;
@override final  DateTime? createdAt;
@override final  DateTime? deletedAt;
@override final  DateTime? removedAt;
@override@JsonKey(name: 'OfficeId') final  int? officeId;
 final  List<LeaveAllotmentModel> _leaveTypeAllotments;
@override@JsonKey(name: 'leaveTypeAllots') List<LeaveAllotmentModel> get leaveTypeAllotments {
  if (_leaveTypeAllotments is EqualUnmodifiableListView) return _leaveTypeAllotments;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_leaveTypeAllotments);
}


/// Create a copy of LeavePolicyModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LeavePolicyModelCopyWith<_LeavePolicyModel> get copyWith => __$LeavePolicyModelCopyWithImpl<_LeavePolicyModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LeavePolicyModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LeavePolicyModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.policy, policy) || other.policy == policy)&&(identical(other.gender, gender) || other.gender == gender)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt)&&(identical(other.removedAt, removedAt) || other.removedAt == removedAt)&&(identical(other.officeId, officeId) || other.officeId == officeId)&&const DeepCollectionEquality().equals(other._leaveTypeAllotments, _leaveTypeAllotments));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,policy,gender,createdAt,deletedAt,removedAt,officeId,const DeepCollectionEquality().hash(_leaveTypeAllotments));

@override
String toString() {
  return 'LeavePolicyModel(id: $id, name: $name, policy: $policy, gender: $gender, createdAt: $createdAt, deletedAt: $deletedAt, removedAt: $removedAt, officeId: $officeId, leaveTypeAllotments: $leaveTypeAllotments)';
}


}

/// @nodoc
abstract mixin class _$LeavePolicyModelCopyWith<$Res> implements $LeavePolicyModelCopyWith<$Res> {
  factory _$LeavePolicyModelCopyWith(_LeavePolicyModel value, $Res Function(_LeavePolicyModel) _then) = __$LeavePolicyModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String? name, String? policy, String? gender, DateTime? createdAt, DateTime? deletedAt, DateTime? removedAt,@JsonKey(name: 'OfficeId') int? officeId,@JsonKey(name: 'leaveTypeAllots') List<LeaveAllotmentModel> leaveTypeAllotments
});




}
/// @nodoc
class __$LeavePolicyModelCopyWithImpl<$Res>
    implements _$LeavePolicyModelCopyWith<$Res> {
  __$LeavePolicyModelCopyWithImpl(this._self, this._then);

  final _LeavePolicyModel _self;
  final $Res Function(_LeavePolicyModel) _then;

/// Create a copy of LeavePolicyModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = freezed,Object? policy = freezed,Object? gender = freezed,Object? createdAt = freezed,Object? deletedAt = freezed,Object? removedAt = freezed,Object? officeId = freezed,Object? leaveTypeAllotments = null,}) {
  return _then(_LeavePolicyModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,policy: freezed == policy ? _self.policy : policy // ignore: cast_nullable_to_non_nullable
as String?,gender: freezed == gender ? _self.gender : gender // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,removedAt: freezed == removedAt ? _self.removedAt : removedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,officeId: freezed == officeId ? _self.officeId : officeId // ignore: cast_nullable_to_non_nullable
as int?,leaveTypeAllotments: null == leaveTypeAllotments ? _self._leaveTypeAllotments : leaveTypeAllotments // ignore: cast_nullable_to_non_nullable
as List<LeaveAllotmentModel>,
  ));
}


}


/// @nodoc
mixin _$LeaveAllotmentModel {

@JsonKey(name: 'LeavePolicyId') int get id;@JsonKey(name: 'LeaveTypeId') int? get leaveTypeId;@JsonKey(name: 'monthlyQuota') int? get monthlyQuota;@JsonKey(name: 'annualQuota') int? get annualQuota; String? get policy; DateTime? get createdAt; LeaveTypePolicyModel? get leaveType;
/// Create a copy of LeaveAllotmentModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LeaveAllotmentModelCopyWith<LeaveAllotmentModel> get copyWith => _$LeaveAllotmentModelCopyWithImpl<LeaveAllotmentModel>(this as LeaveAllotmentModel, _$identity);

  /// Serializes this LeaveAllotmentModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LeaveAllotmentModel&&(identical(other.id, id) || other.id == id)&&(identical(other.leaveTypeId, leaveTypeId) || other.leaveTypeId == leaveTypeId)&&(identical(other.monthlyQuota, monthlyQuota) || other.monthlyQuota == monthlyQuota)&&(identical(other.annualQuota, annualQuota) || other.annualQuota == annualQuota)&&(identical(other.policy, policy) || other.policy == policy)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.leaveType, leaveType) || other.leaveType == leaveType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,leaveTypeId,monthlyQuota,annualQuota,policy,createdAt,leaveType);

@override
String toString() {
  return 'LeaveAllotmentModel(id: $id, leaveTypeId: $leaveTypeId, monthlyQuota: $monthlyQuota, annualQuota: $annualQuota, policy: $policy, createdAt: $createdAt, leaveType: $leaveType)';
}


}

/// @nodoc
abstract mixin class $LeaveAllotmentModelCopyWith<$Res>  {
  factory $LeaveAllotmentModelCopyWith(LeaveAllotmentModel value, $Res Function(LeaveAllotmentModel) _then) = _$LeaveAllotmentModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'LeavePolicyId') int id,@JsonKey(name: 'LeaveTypeId') int? leaveTypeId,@JsonKey(name: 'monthlyQuota') int? monthlyQuota,@JsonKey(name: 'annualQuota') int? annualQuota, String? policy, DateTime? createdAt, LeaveTypePolicyModel? leaveType
});


$LeaveTypePolicyModelCopyWith<$Res>? get leaveType;

}
/// @nodoc
class _$LeaveAllotmentModelCopyWithImpl<$Res>
    implements $LeaveAllotmentModelCopyWith<$Res> {
  _$LeaveAllotmentModelCopyWithImpl(this._self, this._then);

  final LeaveAllotmentModel _self;
  final $Res Function(LeaveAllotmentModel) _then;

/// Create a copy of LeaveAllotmentModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? leaveTypeId = freezed,Object? monthlyQuota = freezed,Object? annualQuota = freezed,Object? policy = freezed,Object? createdAt = freezed,Object? leaveType = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,leaveTypeId: freezed == leaveTypeId ? _self.leaveTypeId : leaveTypeId // ignore: cast_nullable_to_non_nullable
as int?,monthlyQuota: freezed == monthlyQuota ? _self.monthlyQuota : monthlyQuota // ignore: cast_nullable_to_non_nullable
as int?,annualQuota: freezed == annualQuota ? _self.annualQuota : annualQuota // ignore: cast_nullable_to_non_nullable
as int?,policy: freezed == policy ? _self.policy : policy // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,leaveType: freezed == leaveType ? _self.leaveType : leaveType // ignore: cast_nullable_to_non_nullable
as LeaveTypePolicyModel?,
  ));
}
/// Create a copy of LeaveAllotmentModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeaveTypePolicyModelCopyWith<$Res>? get leaveType {
    if (_self.leaveType == null) {
    return null;
  }

  return $LeaveTypePolicyModelCopyWith<$Res>(_self.leaveType!, (value) {
    return _then(_self.copyWith(leaveType: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _LeaveAllotmentModel implements LeaveAllotmentModel {
  const _LeaveAllotmentModel({@JsonKey(name: 'LeavePolicyId') required this.id, @JsonKey(name: 'LeaveTypeId') this.leaveTypeId, @JsonKey(name: 'monthlyQuota') this.monthlyQuota, @JsonKey(name: 'annualQuota') this.annualQuota, this.policy, this.createdAt, this.leaveType});
  factory _LeaveAllotmentModel.fromJson(Map<String, dynamic> json) => _$LeaveAllotmentModelFromJson(json);

@override@JsonKey(name: 'LeavePolicyId') final  int id;
@override@JsonKey(name: 'LeaveTypeId') final  int? leaveTypeId;
@override@JsonKey(name: 'monthlyQuota') final  int? monthlyQuota;
@override@JsonKey(name: 'annualQuota') final  int? annualQuota;
@override final  String? policy;
@override final  DateTime? createdAt;
@override final  LeaveTypePolicyModel? leaveType;

/// Create a copy of LeaveAllotmentModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LeaveAllotmentModelCopyWith<_LeaveAllotmentModel> get copyWith => __$LeaveAllotmentModelCopyWithImpl<_LeaveAllotmentModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LeaveAllotmentModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LeaveAllotmentModel&&(identical(other.id, id) || other.id == id)&&(identical(other.leaveTypeId, leaveTypeId) || other.leaveTypeId == leaveTypeId)&&(identical(other.monthlyQuota, monthlyQuota) || other.monthlyQuota == monthlyQuota)&&(identical(other.annualQuota, annualQuota) || other.annualQuota == annualQuota)&&(identical(other.policy, policy) || other.policy == policy)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.leaveType, leaveType) || other.leaveType == leaveType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,leaveTypeId,monthlyQuota,annualQuota,policy,createdAt,leaveType);

@override
String toString() {
  return 'LeaveAllotmentModel(id: $id, leaveTypeId: $leaveTypeId, monthlyQuota: $monthlyQuota, annualQuota: $annualQuota, policy: $policy, createdAt: $createdAt, leaveType: $leaveType)';
}


}

/// @nodoc
abstract mixin class _$LeaveAllotmentModelCopyWith<$Res> implements $LeaveAllotmentModelCopyWith<$Res> {
  factory _$LeaveAllotmentModelCopyWith(_LeaveAllotmentModel value, $Res Function(_LeaveAllotmentModel) _then) = __$LeaveAllotmentModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'LeavePolicyId') int id,@JsonKey(name: 'LeaveTypeId') int? leaveTypeId,@JsonKey(name: 'monthlyQuota') int? monthlyQuota,@JsonKey(name: 'annualQuota') int? annualQuota, String? policy, DateTime? createdAt, LeaveTypePolicyModel? leaveType
});


@override $LeaveTypePolicyModelCopyWith<$Res>? get leaveType;

}
/// @nodoc
class __$LeaveAllotmentModelCopyWithImpl<$Res>
    implements _$LeaveAllotmentModelCopyWith<$Res> {
  __$LeaveAllotmentModelCopyWithImpl(this._self, this._then);

  final _LeaveAllotmentModel _self;
  final $Res Function(_LeaveAllotmentModel) _then;

/// Create a copy of LeaveAllotmentModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? leaveTypeId = freezed,Object? monthlyQuota = freezed,Object? annualQuota = freezed,Object? policy = freezed,Object? createdAt = freezed,Object? leaveType = freezed,}) {
  return _then(_LeaveAllotmentModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,leaveTypeId: freezed == leaveTypeId ? _self.leaveTypeId : leaveTypeId // ignore: cast_nullable_to_non_nullable
as int?,monthlyQuota: freezed == monthlyQuota ? _self.monthlyQuota : monthlyQuota // ignore: cast_nullable_to_non_nullable
as int?,annualQuota: freezed == annualQuota ? _self.annualQuota : annualQuota // ignore: cast_nullable_to_non_nullable
as int?,policy: freezed == policy ? _self.policy : policy // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,leaveType: freezed == leaveType ? _self.leaveType : leaveType // ignore: cast_nullable_to_non_nullable
as LeaveTypePolicyModel?,
  ));
}

/// Create a copy of LeaveAllotmentModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeaveTypePolicyModelCopyWith<$Res>? get leaveType {
    if (_self.leaveType == null) {
    return null;
  }

  return $LeaveTypePolicyModelCopyWith<$Res>(_self.leaveType!, (value) {
    return _then(_self.copyWith(leaveType: value));
  });
}
}


/// @nodoc
mixin _$LeaveTypePolicyModel {

 int get id; String? get name; String? get policy; DateTime? get createdAt;@JsonKey(name: 'OrganizationId') int? get organizationId;
/// Create a copy of LeaveTypePolicyModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LeaveTypePolicyModelCopyWith<LeaveTypePolicyModel> get copyWith => _$LeaveTypePolicyModelCopyWithImpl<LeaveTypePolicyModel>(this as LeaveTypePolicyModel, _$identity);

  /// Serializes this LeaveTypePolicyModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LeaveTypePolicyModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.policy, policy) || other.policy == policy)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.organizationId, organizationId) || other.organizationId == organizationId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,policy,createdAt,organizationId);

@override
String toString() {
  return 'LeaveTypePolicyModel(id: $id, name: $name, policy: $policy, createdAt: $createdAt, organizationId: $organizationId)';
}


}

/// @nodoc
abstract mixin class $LeaveTypePolicyModelCopyWith<$Res>  {
  factory $LeaveTypePolicyModelCopyWith(LeaveTypePolicyModel value, $Res Function(LeaveTypePolicyModel) _then) = _$LeaveTypePolicyModelCopyWithImpl;
@useResult
$Res call({
 int id, String? name, String? policy, DateTime? createdAt,@JsonKey(name: 'OrganizationId') int? organizationId
});




}
/// @nodoc
class _$LeaveTypePolicyModelCopyWithImpl<$Res>
    implements $LeaveTypePolicyModelCopyWith<$Res> {
  _$LeaveTypePolicyModelCopyWithImpl(this._self, this._then);

  final LeaveTypePolicyModel _self;
  final $Res Function(LeaveTypePolicyModel) _then;

/// Create a copy of LeaveTypePolicyModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = freezed,Object? policy = freezed,Object? createdAt = freezed,Object? organizationId = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,policy: freezed == policy ? _self.policy : policy // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,organizationId: freezed == organizationId ? _self.organizationId : organizationId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _LeaveTypePolicyModel implements LeaveTypePolicyModel {
  const _LeaveTypePolicyModel({required this.id, this.name, this.policy, this.createdAt, @JsonKey(name: 'OrganizationId') this.organizationId});
  factory _LeaveTypePolicyModel.fromJson(Map<String, dynamic> json) => _$LeaveTypePolicyModelFromJson(json);

@override final  int id;
@override final  String? name;
@override final  String? policy;
@override final  DateTime? createdAt;
@override@JsonKey(name: 'OrganizationId') final  int? organizationId;

/// Create a copy of LeaveTypePolicyModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LeaveTypePolicyModelCopyWith<_LeaveTypePolicyModel> get copyWith => __$LeaveTypePolicyModelCopyWithImpl<_LeaveTypePolicyModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LeaveTypePolicyModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LeaveTypePolicyModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.policy, policy) || other.policy == policy)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.organizationId, organizationId) || other.organizationId == organizationId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,policy,createdAt,organizationId);

@override
String toString() {
  return 'LeaveTypePolicyModel(id: $id, name: $name, policy: $policy, createdAt: $createdAt, organizationId: $organizationId)';
}


}

/// @nodoc
abstract mixin class _$LeaveTypePolicyModelCopyWith<$Res> implements $LeaveTypePolicyModelCopyWith<$Res> {
  factory _$LeaveTypePolicyModelCopyWith(_LeaveTypePolicyModel value, $Res Function(_LeaveTypePolicyModel) _then) = __$LeaveTypePolicyModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String? name, String? policy, DateTime? createdAt,@JsonKey(name: 'OrganizationId') int? organizationId
});




}
/// @nodoc
class __$LeaveTypePolicyModelCopyWithImpl<$Res>
    implements _$LeaveTypePolicyModelCopyWith<$Res> {
  __$LeaveTypePolicyModelCopyWithImpl(this._self, this._then);

  final _LeaveTypePolicyModel _self;
  final $Res Function(_LeaveTypePolicyModel) _then;

/// Create a copy of LeaveTypePolicyModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = freezed,Object? policy = freezed,Object? createdAt = freezed,Object? organizationId = freezed,}) {
  return _then(_LeaveTypePolicyModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,policy: freezed == policy ? _self.policy : policy // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,organizationId: freezed == organizationId ? _self.organizationId : organizationId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on
