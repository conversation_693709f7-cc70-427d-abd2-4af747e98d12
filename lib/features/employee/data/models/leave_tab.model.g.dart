// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'leave_tab.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_LeaveRequestRecordModel _$LeaveRequestRecordModelFromJson(
  Map<String, dynamic> json,
) => _LeaveRequestRecordModel(
  id: (json['id'] as num).toInt(),
  type: json['type'] as String?,
  startDate: json['startDate'] == null
      ? null
      : DateTime.parse(json['startDate'] as String),
  endDate: json['endDate'] == null
      ? null
      : DateTime.parse(json['endDate'] as String),
  startDaySlot: json['startDaySlot'] as String?,
  endDaySlot: json['endDaySlot'] as String?,
  reason: json['reason'] as String?,
  rejectReason: json['rejectReason'] as String?,
  status: json['status'] as String?,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  leaveType: json['leaveType'] == null
      ? null
      : LeaveType.fromJson(json['leaveType'] as Map<String, dynamic>),
  reviewer: reviewerFromJson(json['reviewer'] as Map<String, dynamic>?),
);

Map<String, dynamic> _$LeaveRequestRecordModelToJson(
  _LeaveRequestRecordModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'type': instance.type,
  'startDate': instance.startDate?.toIso8601String(),
  'endDate': instance.endDate?.toIso8601String(),
  'startDaySlot': instance.startDaySlot,
  'endDaySlot': instance.endDaySlot,
  'reason': instance.reason,
  'rejectReason': instance.rejectReason,
  'status': instance.status,
  'createdAt': instance.createdAt?.toIso8601String(),
  'leaveType': instance.leaveType,
  'reviewer': instance.reviewer,
};

_LeaveReviewerModel _$LeaveReviewerModelFromJson(Map<String, dynamic> json) =>
    _LeaveReviewerModel(
      id: (json['id'] as num?)?.toInt(),
      displayName: json['displayName'] as String?,
    );

Map<String, dynamic> _$LeaveReviewerModelToJson(_LeaveReviewerModel instance) =>
    <String, dynamic>{'id': instance.id, 'displayName': instance.displayName};

_LeavePolicyModel _$LeavePolicyModelFromJson(Map<String, dynamic> json) =>
    _LeavePolicyModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String?,
      policy: json['policy'] as String?,
      gender: json['gender'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      removedAt: json['removedAt'] == null
          ? null
          : DateTime.parse(json['removedAt'] as String),
      officeId: (json['OfficeId'] as num?)?.toInt(),
      leaveTypeAllotments: (json['leaveTypeAllots'] as List<dynamic>)
          .map((e) => LeaveAllotmentModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$LeavePolicyModelToJson(_LeavePolicyModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'policy': instance.policy,
      'gender': instance.gender,
      'createdAt': instance.createdAt?.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'removedAt': instance.removedAt?.toIso8601String(),
      'OfficeId': instance.officeId,
      'leaveTypeAllots': instance.leaveTypeAllotments
          .map((e) => e.toJson())
          .toList(),
    };

_LeaveAllotmentModel _$LeaveAllotmentModelFromJson(Map<String, dynamic> json) =>
    _LeaveAllotmentModel(
      id: (json['LeavePolicyId'] as num).toInt(),
      leaveTypeId: (json['LeaveTypeId'] as num?)?.toInt(),
      monthlyQuota: (json['monthlyQuota'] as num?)?.toInt(),
      annualQuota: (json['annualQuota'] as num?)?.toInt(),
      policy: json['policy'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      leaveType: json['leaveType'] == null
          ? null
          : LeaveTypePolicyModel.fromJson(
              json['leaveType'] as Map<String, dynamic>,
            ),
    );

Map<String, dynamic> _$LeaveAllotmentModelToJson(
  _LeaveAllotmentModel instance,
) => <String, dynamic>{
  'LeavePolicyId': instance.id,
  'LeaveTypeId': instance.leaveTypeId,
  'monthlyQuota': instance.monthlyQuota,
  'annualQuota': instance.annualQuota,
  'policy': instance.policy,
  'createdAt': instance.createdAt?.toIso8601String(),
  'leaveType': instance.leaveType,
};

_LeaveTypePolicyModel _$LeaveTypePolicyModelFromJson(
  Map<String, dynamic> json,
) => _LeaveTypePolicyModel(
  id: (json['id'] as num).toInt(),
  name: json['name'] as String?,
  policy: json['policy'] as String?,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  organizationId: (json['OrganizationId'] as num?)?.toInt(),
);

Map<String, dynamic> _$LeaveTypePolicyModelToJson(
  _LeaveTypePolicyModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'policy': instance.policy,
  'createdAt': instance.createdAt?.toIso8601String(),
  'OrganizationId': instance.organizationId,
};
