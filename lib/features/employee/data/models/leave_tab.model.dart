import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../home/<USER>/models/leave_balance.model.dart';

part 'leave_tab.model.freezed.dart';
part 'leave_tab.model.g.dart';

String leaveTypeFromId(int id) => switch (id) {
  1 => 'Paid Leave',
  2 => 'Sick Leave',
  3 => 'Unpaid Leave',
  _ => 'Unknown',
};

int idFromLeaveName(String leaveName) => switch (leaveName) {
  'Paid Leave' => 1,
  'Sick Leave' => 2,
  'Unpaid Leave' => 3,
  _ => 0,
};

LeaveReviewerModel reviewerFromJson(Map<String, dynamic>? json) =>
    LeaveReviewerModel.fromJson(json?['user'] ?? {});

@freezed
abstract class LeaveRequestRecordModel with _$LeaveRequestRecordModel {
  const factory LeaveRequestRecordModel({
    required final int id,
    final String? type,
    final DateTime? startDate,
    final DateTime? endDate,
    final String? startDaySlot,
    final String? endDaySlot,
    final String? reason,
    final String? rejectReason,
    final String? status,
    final DateTime? createdAt,
    final LeaveType? leaveType,

    @JsonKey(name: 'reviewer', fromJson: reviewerFromJson)
    final LeaveReviewerModel? reviewer,
  }) = _LeaveRequestRecordModel;

  factory LeaveRequestRecordModel.fromJson(Map<String, dynamic> json) =>
      _$LeaveRequestRecordModelFromJson(json);
}

@freezed
abstract class LeaveReviewerModel with _$LeaveReviewerModel {
  const factory LeaveReviewerModel({final int? id, final String? displayName}) =
      _LeaveReviewerModel;

  factory LeaveReviewerModel.fromJson(Map<String, dynamic> json) =>
      _$LeaveReviewerModelFromJson(json);
}

@freezed
abstract class LeavePolicyModel with _$LeavePolicyModel {
  @JsonSerializable(explicitToJson: true)
  const factory LeavePolicyModel({
    required final int id,
    final String? name,
    final String? policy,
    final String? gender,
    final DateTime? createdAt,
    final DateTime? deletedAt,
    final DateTime? removedAt,
    @JsonKey(name: 'OfficeId') final int? officeId,
    @JsonKey(name: 'leaveTypeAllots')
    required final List<LeaveAllotmentModel> leaveTypeAllotments,
  }) = _LeavePolicyModel;

  factory LeavePolicyModel.fromJson(Map<String, dynamic> json) =>
      _$LeavePolicyModelFromJson(json);
}

@freezed
abstract class LeaveAllotmentModel with _$LeaveAllotmentModel {
  const factory LeaveAllotmentModel({
    @JsonKey(name: 'LeavePolicyId') required final int id,
    @JsonKey(name: 'LeaveTypeId') final int? leaveTypeId,
    @JsonKey(name: 'monthlyQuota') final int? monthlyQuota,
    @JsonKey(name: 'annualQuota') final int? annualQuota,
    final String? policy,
    final DateTime? createdAt,
    final LeaveTypePolicyModel? leaveType,
  }) = _LeaveAllotmentModel;

  factory LeaveAllotmentModel.fromJson(Map<String, dynamic> json) =>
      _$LeaveAllotmentModelFromJson(json);
}

@freezed
abstract class LeaveTypePolicyModel with _$LeaveTypePolicyModel {
  const factory LeaveTypePolicyModel({
    required final int id,
    final String? name,
    final String? policy,
    final DateTime? createdAt,
    @JsonKey(name: 'OrganizationId') final int? organizationId,
  }) = _LeaveTypePolicyModel;

  factory LeaveTypePolicyModel.fromJson(Map<String, dynamic> json) =>
      _$LeaveTypePolicyModelFromJson(json);
}
