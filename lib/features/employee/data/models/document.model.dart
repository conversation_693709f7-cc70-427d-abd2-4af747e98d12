import 'package:freezed_annotation/freezed_annotation.dart';

part 'document.model.freezed.dart';
part 'document.model.g.dart';

@freezed
abstract class DocumentCategoryModel with _$DocumentCategoryModel {
  const factory DocumentCategoryModel({
    @J<PERSON><PERSON>ey(name: 'docCategory_id') required final int id,
    @Json<PERSON>ey(name: 'docCategory_name') final String? name,
    @Json<PERSON>ey(name: 'documentCount') final String? count,
  }) = _DocumentCategoryModel;

  factory DocumentCategoryModel.fromJson(Map<String, dynamic> json) =>
      _$DocumentCategoryModelFromJson(json);
}

DocumentDataModel? docDataFromDocumentJson(List<dynamic> json) =>
    json.isEmpty ? null : DocumentDataModel.fromJson(json.first);

@freezed
abstract class SingleDocumentModel with _$SingleDocumentModel {
  const factory SingleDocumentModel({
    required final int id,
    final String? name,
    final DateTime? createdAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'DocCategoryId') final int? categoryId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'userDocs', fromJson: docDataFromDocumentJson)
    final DocumentDataModel? data,
  }) = _SingleDocumentModel;

  factory SingleDocumentModel.fromJson(Map<String, dynamic> json) =>
      _$SingleDocumentModelFromJson(json);
}

@freezed
abstract class DocumentDataModel with _$DocumentDataModel {
  const factory DocumentDataModel({
    @JsonKey(name: 'docLink') final String? docLink,
    @JsonKey(name: 'docNumber') final String? docNumber,
  }) = _DocumentDataModel;

  factory DocumentDataModel.fromJson(Map<String, dynamic> json) =>
      _$DocumentDataModelFromJson(json);
}
