// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'attendance_tab.model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AttendanceTabModel {

 WorkShiftModel? get workShift; TimeTableDataModel? get timetableData;@JsonKey(name: 'history', fromJson: attendanceHistoryFromJson) List<AttendanceHistoryModel>? get attendanceHistory;
/// Create a copy of AttendanceTabModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AttendanceTabModelCopyWith<AttendanceTabModel> get copyWith => _$AttendanceTabModelCopyWithImpl<AttendanceTabModel>(this as AttendanceTabModel, _$identity);

  /// Serializes this AttendanceTabModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AttendanceTabModel&&(identical(other.workShift, workShift) || other.workShift == workShift)&&(identical(other.timetableData, timetableData) || other.timetableData == timetableData)&&const DeepCollectionEquality().equals(other.attendanceHistory, attendanceHistory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,workShift,timetableData,const DeepCollectionEquality().hash(attendanceHistory));

@override
String toString() {
  return 'AttendanceTabModel(workShift: $workShift, timetableData: $timetableData, attendanceHistory: $attendanceHistory)';
}


}

/// @nodoc
abstract mixin class $AttendanceTabModelCopyWith<$Res>  {
  factory $AttendanceTabModelCopyWith(AttendanceTabModel value, $Res Function(AttendanceTabModel) _then) = _$AttendanceTabModelCopyWithImpl;
@useResult
$Res call({
 WorkShiftModel? workShift, TimeTableDataModel? timetableData,@JsonKey(name: 'history', fromJson: attendanceHistoryFromJson) List<AttendanceHistoryModel>? attendanceHistory
});


$WorkShiftModelCopyWith<$Res>? get workShift;$TimeTableDataModelCopyWith<$Res>? get timetableData;

}
/// @nodoc
class _$AttendanceTabModelCopyWithImpl<$Res>
    implements $AttendanceTabModelCopyWith<$Res> {
  _$AttendanceTabModelCopyWithImpl(this._self, this._then);

  final AttendanceTabModel _self;
  final $Res Function(AttendanceTabModel) _then;

/// Create a copy of AttendanceTabModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? workShift = freezed,Object? timetableData = freezed,Object? attendanceHistory = freezed,}) {
  return _then(_self.copyWith(
workShift: freezed == workShift ? _self.workShift : workShift // ignore: cast_nullable_to_non_nullable
as WorkShiftModel?,timetableData: freezed == timetableData ? _self.timetableData : timetableData // ignore: cast_nullable_to_non_nullable
as TimeTableDataModel?,attendanceHistory: freezed == attendanceHistory ? _self.attendanceHistory : attendanceHistory // ignore: cast_nullable_to_non_nullable
as List<AttendanceHistoryModel>?,
  ));
}
/// Create a copy of AttendanceTabModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WorkShiftModelCopyWith<$Res>? get workShift {
    if (_self.workShift == null) {
    return null;
  }

  return $WorkShiftModelCopyWith<$Res>(_self.workShift!, (value) {
    return _then(_self.copyWith(workShift: value));
  });
}/// Create a copy of AttendanceTabModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TimeTableDataModelCopyWith<$Res>? get timetableData {
    if (_self.timetableData == null) {
    return null;
  }

  return $TimeTableDataModelCopyWith<$Res>(_self.timetableData!, (value) {
    return _then(_self.copyWith(timetableData: value));
  });
}
}


/// @nodoc

@JsonSerializable(explicitToJson: true)
class _AttendanceTabModel implements AttendanceTabModel {
  const _AttendanceTabModel({this.workShift, this.timetableData, @JsonKey(name: 'history', fromJson: attendanceHistoryFromJson) final  List<AttendanceHistoryModel>? attendanceHistory}): _attendanceHistory = attendanceHistory;
  factory _AttendanceTabModel.fromJson(Map<String, dynamic> json) => _$AttendanceTabModelFromJson(json);

@override final  WorkShiftModel? workShift;
@override final  TimeTableDataModel? timetableData;
 final  List<AttendanceHistoryModel>? _attendanceHistory;
@override@JsonKey(name: 'history', fromJson: attendanceHistoryFromJson) List<AttendanceHistoryModel>? get attendanceHistory {
  final value = _attendanceHistory;
  if (value == null) return null;
  if (_attendanceHistory is EqualUnmodifiableListView) return _attendanceHistory;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of AttendanceTabModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AttendanceTabModelCopyWith<_AttendanceTabModel> get copyWith => __$AttendanceTabModelCopyWithImpl<_AttendanceTabModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AttendanceTabModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AttendanceTabModel&&(identical(other.workShift, workShift) || other.workShift == workShift)&&(identical(other.timetableData, timetableData) || other.timetableData == timetableData)&&const DeepCollectionEquality().equals(other._attendanceHistory, _attendanceHistory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,workShift,timetableData,const DeepCollectionEquality().hash(_attendanceHistory));

@override
String toString() {
  return 'AttendanceTabModel(workShift: $workShift, timetableData: $timetableData, attendanceHistory: $attendanceHistory)';
}


}

/// @nodoc
abstract mixin class _$AttendanceTabModelCopyWith<$Res> implements $AttendanceTabModelCopyWith<$Res> {
  factory _$AttendanceTabModelCopyWith(_AttendanceTabModel value, $Res Function(_AttendanceTabModel) _then) = __$AttendanceTabModelCopyWithImpl;
@override @useResult
$Res call({
 WorkShiftModel? workShift, TimeTableDataModel? timetableData,@JsonKey(name: 'history', fromJson: attendanceHistoryFromJson) List<AttendanceHistoryModel>? attendanceHistory
});


@override $WorkShiftModelCopyWith<$Res>? get workShift;@override $TimeTableDataModelCopyWith<$Res>? get timetableData;

}
/// @nodoc
class __$AttendanceTabModelCopyWithImpl<$Res>
    implements _$AttendanceTabModelCopyWith<$Res> {
  __$AttendanceTabModelCopyWithImpl(this._self, this._then);

  final _AttendanceTabModel _self;
  final $Res Function(_AttendanceTabModel) _then;

/// Create a copy of AttendanceTabModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? workShift = freezed,Object? timetableData = freezed,Object? attendanceHistory = freezed,}) {
  return _then(_AttendanceTabModel(
workShift: freezed == workShift ? _self.workShift : workShift // ignore: cast_nullable_to_non_nullable
as WorkShiftModel?,timetableData: freezed == timetableData ? _self.timetableData : timetableData // ignore: cast_nullable_to_non_nullable
as TimeTableDataModel?,attendanceHistory: freezed == attendanceHistory ? _self._attendanceHistory : attendanceHistory // ignore: cast_nullable_to_non_nullable
as List<AttendanceHistoryModel>?,
  ));
}

/// Create a copy of AttendanceTabModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WorkShiftModelCopyWith<$Res>? get workShift {
    if (_self.workShift == null) {
    return null;
  }

  return $WorkShiftModelCopyWith<$Res>(_self.workShift!, (value) {
    return _then(_self.copyWith(workShift: value));
  });
}/// Create a copy of AttendanceTabModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TimeTableDataModelCopyWith<$Res>? get timetableData {
    if (_self.timetableData == null) {
    return null;
  }

  return $TimeTableDataModelCopyWith<$Res>(_self.timetableData!, (value) {
    return _then(_self.copyWith(timetableData: value));
  });
}
}


/// @nodoc
mixin _$WorkShiftModel {

 int get id; String? get name; String? get startTime; String? get endTime;
/// Create a copy of WorkShiftModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WorkShiftModelCopyWith<WorkShiftModel> get copyWith => _$WorkShiftModelCopyWithImpl<WorkShiftModel>(this as WorkShiftModel, _$identity);

  /// Serializes this WorkShiftModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WorkShiftModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.startTime, startTime) || other.startTime == startTime)&&(identical(other.endTime, endTime) || other.endTime == endTime));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,startTime,endTime);

@override
String toString() {
  return 'WorkShiftModel(id: $id, name: $name, startTime: $startTime, endTime: $endTime)';
}


}

/// @nodoc
abstract mixin class $WorkShiftModelCopyWith<$Res>  {
  factory $WorkShiftModelCopyWith(WorkShiftModel value, $Res Function(WorkShiftModel) _then) = _$WorkShiftModelCopyWithImpl;
@useResult
$Res call({
 int id, String? name, String? startTime, String? endTime
});




}
/// @nodoc
class _$WorkShiftModelCopyWithImpl<$Res>
    implements $WorkShiftModelCopyWith<$Res> {
  _$WorkShiftModelCopyWithImpl(this._self, this._then);

  final WorkShiftModel _self;
  final $Res Function(WorkShiftModel) _then;

/// Create a copy of WorkShiftModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = freezed,Object? startTime = freezed,Object? endTime = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,startTime: freezed == startTime ? _self.startTime : startTime // ignore: cast_nullable_to_non_nullable
as String?,endTime: freezed == endTime ? _self.endTime : endTime // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _WorkShiftModel implements WorkShiftModel {
  const _WorkShiftModel({required this.id, this.name, this.startTime, this.endTime});
  factory _WorkShiftModel.fromJson(Map<String, dynamic> json) => _$WorkShiftModelFromJson(json);

@override final  int id;
@override final  String? name;
@override final  String? startTime;
@override final  String? endTime;

/// Create a copy of WorkShiftModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WorkShiftModelCopyWith<_WorkShiftModel> get copyWith => __$WorkShiftModelCopyWithImpl<_WorkShiftModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WorkShiftModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WorkShiftModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.startTime, startTime) || other.startTime == startTime)&&(identical(other.endTime, endTime) || other.endTime == endTime));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,startTime,endTime);

@override
String toString() {
  return 'WorkShiftModel(id: $id, name: $name, startTime: $startTime, endTime: $endTime)';
}


}

/// @nodoc
abstract mixin class _$WorkShiftModelCopyWith<$Res> implements $WorkShiftModelCopyWith<$Res> {
  factory _$WorkShiftModelCopyWith(_WorkShiftModel value, $Res Function(_WorkShiftModel) _then) = __$WorkShiftModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String? name, String? startTime, String? endTime
});




}
/// @nodoc
class __$WorkShiftModelCopyWithImpl<$Res>
    implements _$WorkShiftModelCopyWith<$Res> {
  __$WorkShiftModelCopyWithImpl(this._self, this._then);

  final _WorkShiftModel _self;
  final $Res Function(_WorkShiftModel) _then;

/// Create a copy of WorkShiftModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = freezed,Object? startTime = freezed,Object? endTime = freezed,}) {
  return _then(_WorkShiftModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,startTime: freezed == startTime ? _self.startTime : startTime // ignore: cast_nullable_to_non_nullable
as String?,endTime: freezed == endTime ? _self.endTime : endTime // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$TimeTableDataModel {

 int get id; String? get name; String? get firstWorkingDay; String? get lastWorkingDay;
/// Create a copy of TimeTableDataModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TimeTableDataModelCopyWith<TimeTableDataModel> get copyWith => _$TimeTableDataModelCopyWithImpl<TimeTableDataModel>(this as TimeTableDataModel, _$identity);

  /// Serializes this TimeTableDataModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TimeTableDataModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.firstWorkingDay, firstWorkingDay) || other.firstWorkingDay == firstWorkingDay)&&(identical(other.lastWorkingDay, lastWorkingDay) || other.lastWorkingDay == lastWorkingDay));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,firstWorkingDay,lastWorkingDay);

@override
String toString() {
  return 'TimeTableDataModel(id: $id, name: $name, firstWorkingDay: $firstWorkingDay, lastWorkingDay: $lastWorkingDay)';
}


}

/// @nodoc
abstract mixin class $TimeTableDataModelCopyWith<$Res>  {
  factory $TimeTableDataModelCopyWith(TimeTableDataModel value, $Res Function(TimeTableDataModel) _then) = _$TimeTableDataModelCopyWithImpl;
@useResult
$Res call({
 int id, String? name, String? firstWorkingDay, String? lastWorkingDay
});




}
/// @nodoc
class _$TimeTableDataModelCopyWithImpl<$Res>
    implements $TimeTableDataModelCopyWith<$Res> {
  _$TimeTableDataModelCopyWithImpl(this._self, this._then);

  final TimeTableDataModel _self;
  final $Res Function(TimeTableDataModel) _then;

/// Create a copy of TimeTableDataModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = freezed,Object? firstWorkingDay = freezed,Object? lastWorkingDay = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,firstWorkingDay: freezed == firstWorkingDay ? _self.firstWorkingDay : firstWorkingDay // ignore: cast_nullable_to_non_nullable
as String?,lastWorkingDay: freezed == lastWorkingDay ? _self.lastWorkingDay : lastWorkingDay // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _TimeTableDataModel implements TimeTableDataModel {
  const _TimeTableDataModel({required this.id, this.name, this.firstWorkingDay, this.lastWorkingDay});
  factory _TimeTableDataModel.fromJson(Map<String, dynamic> json) => _$TimeTableDataModelFromJson(json);

@override final  int id;
@override final  String? name;
@override final  String? firstWorkingDay;
@override final  String? lastWorkingDay;

/// Create a copy of TimeTableDataModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TimeTableDataModelCopyWith<_TimeTableDataModel> get copyWith => __$TimeTableDataModelCopyWithImpl<_TimeTableDataModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TimeTableDataModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TimeTableDataModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.firstWorkingDay, firstWorkingDay) || other.firstWorkingDay == firstWorkingDay)&&(identical(other.lastWorkingDay, lastWorkingDay) || other.lastWorkingDay == lastWorkingDay));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,firstWorkingDay,lastWorkingDay);

@override
String toString() {
  return 'TimeTableDataModel(id: $id, name: $name, firstWorkingDay: $firstWorkingDay, lastWorkingDay: $lastWorkingDay)';
}


}

/// @nodoc
abstract mixin class _$TimeTableDataModelCopyWith<$Res> implements $TimeTableDataModelCopyWith<$Res> {
  factory _$TimeTableDataModelCopyWith(_TimeTableDataModel value, $Res Function(_TimeTableDataModel) _then) = __$TimeTableDataModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String? name, String? firstWorkingDay, String? lastWorkingDay
});




}
/// @nodoc
class __$TimeTableDataModelCopyWithImpl<$Res>
    implements _$TimeTableDataModelCopyWith<$Res> {
  __$TimeTableDataModelCopyWithImpl(this._self, this._then);

  final _TimeTableDataModel _self;
  final $Res Function(_TimeTableDataModel) _then;

/// Create a copy of TimeTableDataModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = freezed,Object? firstWorkingDay = freezed,Object? lastWorkingDay = freezed,}) {
  return _then(_TimeTableDataModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,firstWorkingDay: freezed == firstWorkingDay ? _self.firstWorkingDay : firstWorkingDay // ignore: cast_nullable_to_non_nullable
as String?,lastWorkingDay: freezed == lastWorkingDay ? _self.lastWorkingDay : lastWorkingDay // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$AttendanceHistoryModel {

 int get id; String get status; String? get clockInStatus; String? get clockOutStatus; DateTime? get clockIn; DateTime? get clockOut; double? get lat; double? get long; double? get hoursOverTime; double? get hoursWorked; String? get regularization; RegularizationTimes? get regularizationTimes; DateTime? get createdAt; Map<String, dynamic>? get leaveWfhRequest; Map<String, dynamic>? get holiday;
/// Create a copy of AttendanceHistoryModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AttendanceHistoryModelCopyWith<AttendanceHistoryModel> get copyWith => _$AttendanceHistoryModelCopyWithImpl<AttendanceHistoryModel>(this as AttendanceHistoryModel, _$identity);

  /// Serializes this AttendanceHistoryModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AttendanceHistoryModel&&(identical(other.id, id) || other.id == id)&&(identical(other.status, status) || other.status == status)&&(identical(other.clockInStatus, clockInStatus) || other.clockInStatus == clockInStatus)&&(identical(other.clockOutStatus, clockOutStatus) || other.clockOutStatus == clockOutStatus)&&(identical(other.clockIn, clockIn) || other.clockIn == clockIn)&&(identical(other.clockOut, clockOut) || other.clockOut == clockOut)&&(identical(other.lat, lat) || other.lat == lat)&&(identical(other.long, long) || other.long == long)&&(identical(other.hoursOverTime, hoursOverTime) || other.hoursOverTime == hoursOverTime)&&(identical(other.hoursWorked, hoursWorked) || other.hoursWorked == hoursWorked)&&(identical(other.regularization, regularization) || other.regularization == regularization)&&(identical(other.regularizationTimes, regularizationTimes) || other.regularizationTimes == regularizationTimes)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&const DeepCollectionEquality().equals(other.leaveWfhRequest, leaveWfhRequest)&&const DeepCollectionEquality().equals(other.holiday, holiday));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,status,clockInStatus,clockOutStatus,clockIn,clockOut,lat,long,hoursOverTime,hoursWorked,regularization,regularizationTimes,createdAt,const DeepCollectionEquality().hash(leaveWfhRequest),const DeepCollectionEquality().hash(holiday));

@override
String toString() {
  return 'AttendanceHistoryModel(id: $id, status: $status, clockInStatus: $clockInStatus, clockOutStatus: $clockOutStatus, clockIn: $clockIn, clockOut: $clockOut, lat: $lat, long: $long, hoursOverTime: $hoursOverTime, hoursWorked: $hoursWorked, regularization: $regularization, regularizationTimes: $regularizationTimes, createdAt: $createdAt, leaveWfhRequest: $leaveWfhRequest, holiday: $holiday)';
}


}

/// @nodoc
abstract mixin class $AttendanceHistoryModelCopyWith<$Res>  {
  factory $AttendanceHistoryModelCopyWith(AttendanceHistoryModel value, $Res Function(AttendanceHistoryModel) _then) = _$AttendanceHistoryModelCopyWithImpl;
@useResult
$Res call({
 int id, String status, String? clockInStatus, String? clockOutStatus, DateTime? clockIn, DateTime? clockOut, double? lat, double? long, double? hoursOverTime, double? hoursWorked, String? regularization, RegularizationTimes? regularizationTimes, DateTime? createdAt, Map<String, dynamic>? leaveWfhRequest, Map<String, dynamic>? holiday
});


$RegularizationTimesCopyWith<$Res>? get regularizationTimes;

}
/// @nodoc
class _$AttendanceHistoryModelCopyWithImpl<$Res>
    implements $AttendanceHistoryModelCopyWith<$Res> {
  _$AttendanceHistoryModelCopyWithImpl(this._self, this._then);

  final AttendanceHistoryModel _self;
  final $Res Function(AttendanceHistoryModel) _then;

/// Create a copy of AttendanceHistoryModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? status = null,Object? clockInStatus = freezed,Object? clockOutStatus = freezed,Object? clockIn = freezed,Object? clockOut = freezed,Object? lat = freezed,Object? long = freezed,Object? hoursOverTime = freezed,Object? hoursWorked = freezed,Object? regularization = freezed,Object? regularizationTimes = freezed,Object? createdAt = freezed,Object? leaveWfhRequest = freezed,Object? holiday = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,clockInStatus: freezed == clockInStatus ? _self.clockInStatus : clockInStatus // ignore: cast_nullable_to_non_nullable
as String?,clockOutStatus: freezed == clockOutStatus ? _self.clockOutStatus : clockOutStatus // ignore: cast_nullable_to_non_nullable
as String?,clockIn: freezed == clockIn ? _self.clockIn : clockIn // ignore: cast_nullable_to_non_nullable
as DateTime?,clockOut: freezed == clockOut ? _self.clockOut : clockOut // ignore: cast_nullable_to_non_nullable
as DateTime?,lat: freezed == lat ? _self.lat : lat // ignore: cast_nullable_to_non_nullable
as double?,long: freezed == long ? _self.long : long // ignore: cast_nullable_to_non_nullable
as double?,hoursOverTime: freezed == hoursOverTime ? _self.hoursOverTime : hoursOverTime // ignore: cast_nullable_to_non_nullable
as double?,hoursWorked: freezed == hoursWorked ? _self.hoursWorked : hoursWorked // ignore: cast_nullable_to_non_nullable
as double?,regularization: freezed == regularization ? _self.regularization : regularization // ignore: cast_nullable_to_non_nullable
as String?,regularizationTimes: freezed == regularizationTimes ? _self.regularizationTimes : regularizationTimes // ignore: cast_nullable_to_non_nullable
as RegularizationTimes?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,leaveWfhRequest: freezed == leaveWfhRequest ? _self.leaveWfhRequest : leaveWfhRequest // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,holiday: freezed == holiday ? _self.holiday : holiday // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}
/// Create a copy of AttendanceHistoryModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RegularizationTimesCopyWith<$Res>? get regularizationTimes {
    if (_self.regularizationTimes == null) {
    return null;
  }

  return $RegularizationTimesCopyWith<$Res>(_self.regularizationTimes!, (value) {
    return _then(_self.copyWith(regularizationTimes: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _AttendanceHistoryModel implements AttendanceHistoryModel {
  const _AttendanceHistoryModel({required this.id, required this.status, this.clockInStatus, this.clockOutStatus, this.clockIn, this.clockOut, this.lat, this.long, this.hoursOverTime, this.hoursWorked, this.regularization, this.regularizationTimes, this.createdAt, final  Map<String, dynamic>? leaveWfhRequest, final  Map<String, dynamic>? holiday}): _leaveWfhRequest = leaveWfhRequest,_holiday = holiday;
  factory _AttendanceHistoryModel.fromJson(Map<String, dynamic> json) => _$AttendanceHistoryModelFromJson(json);

@override final  int id;
@override final  String status;
@override final  String? clockInStatus;
@override final  String? clockOutStatus;
@override final  DateTime? clockIn;
@override final  DateTime? clockOut;
@override final  double? lat;
@override final  double? long;
@override final  double? hoursOverTime;
@override final  double? hoursWorked;
@override final  String? regularization;
@override final  RegularizationTimes? regularizationTimes;
@override final  DateTime? createdAt;
 final  Map<String, dynamic>? _leaveWfhRequest;
@override Map<String, dynamic>? get leaveWfhRequest {
  final value = _leaveWfhRequest;
  if (value == null) return null;
  if (_leaveWfhRequest is EqualUnmodifiableMapView) return _leaveWfhRequest;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  Map<String, dynamic>? _holiday;
@override Map<String, dynamic>? get holiday {
  final value = _holiday;
  if (value == null) return null;
  if (_holiday is EqualUnmodifiableMapView) return _holiday;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of AttendanceHistoryModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AttendanceHistoryModelCopyWith<_AttendanceHistoryModel> get copyWith => __$AttendanceHistoryModelCopyWithImpl<_AttendanceHistoryModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AttendanceHistoryModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AttendanceHistoryModel&&(identical(other.id, id) || other.id == id)&&(identical(other.status, status) || other.status == status)&&(identical(other.clockInStatus, clockInStatus) || other.clockInStatus == clockInStatus)&&(identical(other.clockOutStatus, clockOutStatus) || other.clockOutStatus == clockOutStatus)&&(identical(other.clockIn, clockIn) || other.clockIn == clockIn)&&(identical(other.clockOut, clockOut) || other.clockOut == clockOut)&&(identical(other.lat, lat) || other.lat == lat)&&(identical(other.long, long) || other.long == long)&&(identical(other.hoursOverTime, hoursOverTime) || other.hoursOverTime == hoursOverTime)&&(identical(other.hoursWorked, hoursWorked) || other.hoursWorked == hoursWorked)&&(identical(other.regularization, regularization) || other.regularization == regularization)&&(identical(other.regularizationTimes, regularizationTimes) || other.regularizationTimes == regularizationTimes)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&const DeepCollectionEquality().equals(other._leaveWfhRequest, _leaveWfhRequest)&&const DeepCollectionEquality().equals(other._holiday, _holiday));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,status,clockInStatus,clockOutStatus,clockIn,clockOut,lat,long,hoursOverTime,hoursWorked,regularization,regularizationTimes,createdAt,const DeepCollectionEquality().hash(_leaveWfhRequest),const DeepCollectionEquality().hash(_holiday));

@override
String toString() {
  return 'AttendanceHistoryModel(id: $id, status: $status, clockInStatus: $clockInStatus, clockOutStatus: $clockOutStatus, clockIn: $clockIn, clockOut: $clockOut, lat: $lat, long: $long, hoursOverTime: $hoursOverTime, hoursWorked: $hoursWorked, regularization: $regularization, regularizationTimes: $regularizationTimes, createdAt: $createdAt, leaveWfhRequest: $leaveWfhRequest, holiday: $holiday)';
}


}

/// @nodoc
abstract mixin class _$AttendanceHistoryModelCopyWith<$Res> implements $AttendanceHistoryModelCopyWith<$Res> {
  factory _$AttendanceHistoryModelCopyWith(_AttendanceHistoryModel value, $Res Function(_AttendanceHistoryModel) _then) = __$AttendanceHistoryModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String status, String? clockInStatus, String? clockOutStatus, DateTime? clockIn, DateTime? clockOut, double? lat, double? long, double? hoursOverTime, double? hoursWorked, String? regularization, RegularizationTimes? regularizationTimes, DateTime? createdAt, Map<String, dynamic>? leaveWfhRequest, Map<String, dynamic>? holiday
});


@override $RegularizationTimesCopyWith<$Res>? get regularizationTimes;

}
/// @nodoc
class __$AttendanceHistoryModelCopyWithImpl<$Res>
    implements _$AttendanceHistoryModelCopyWith<$Res> {
  __$AttendanceHistoryModelCopyWithImpl(this._self, this._then);

  final _AttendanceHistoryModel _self;
  final $Res Function(_AttendanceHistoryModel) _then;

/// Create a copy of AttendanceHistoryModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? status = null,Object? clockInStatus = freezed,Object? clockOutStatus = freezed,Object? clockIn = freezed,Object? clockOut = freezed,Object? lat = freezed,Object? long = freezed,Object? hoursOverTime = freezed,Object? hoursWorked = freezed,Object? regularization = freezed,Object? regularizationTimes = freezed,Object? createdAt = freezed,Object? leaveWfhRequest = freezed,Object? holiday = freezed,}) {
  return _then(_AttendanceHistoryModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,clockInStatus: freezed == clockInStatus ? _self.clockInStatus : clockInStatus // ignore: cast_nullable_to_non_nullable
as String?,clockOutStatus: freezed == clockOutStatus ? _self.clockOutStatus : clockOutStatus // ignore: cast_nullable_to_non_nullable
as String?,clockIn: freezed == clockIn ? _self.clockIn : clockIn // ignore: cast_nullable_to_non_nullable
as DateTime?,clockOut: freezed == clockOut ? _self.clockOut : clockOut // ignore: cast_nullable_to_non_nullable
as DateTime?,lat: freezed == lat ? _self.lat : lat // ignore: cast_nullable_to_non_nullable
as double?,long: freezed == long ? _self.long : long // ignore: cast_nullable_to_non_nullable
as double?,hoursOverTime: freezed == hoursOverTime ? _self.hoursOverTime : hoursOverTime // ignore: cast_nullable_to_non_nullable
as double?,hoursWorked: freezed == hoursWorked ? _self.hoursWorked : hoursWorked // ignore: cast_nullable_to_non_nullable
as double?,regularization: freezed == regularization ? _self.regularization : regularization // ignore: cast_nullable_to_non_nullable
as String?,regularizationTimes: freezed == regularizationTimes ? _self.regularizationTimes : regularizationTimes // ignore: cast_nullable_to_non_nullable
as RegularizationTimes?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,leaveWfhRequest: freezed == leaveWfhRequest ? _self._leaveWfhRequest : leaveWfhRequest // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,holiday: freezed == holiday ? _self._holiday : holiday // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

/// Create a copy of AttendanceHistoryModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RegularizationTimesCopyWith<$Res>? get regularizationTimes {
    if (_self.regularizationTimes == null) {
    return null;
  }

  return $RegularizationTimesCopyWith<$Res>(_self.regularizationTimes!, (value) {
    return _then(_self.copyWith(regularizationTimes: value));
  });
}
}


/// @nodoc
mixin _$RegularizationTimes {

 DateTime? get clockIn; DateTime? get clockOut;
/// Create a copy of RegularizationTimes
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RegularizationTimesCopyWith<RegularizationTimes> get copyWith => _$RegularizationTimesCopyWithImpl<RegularizationTimes>(this as RegularizationTimes, _$identity);

  /// Serializes this RegularizationTimes to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RegularizationTimes&&(identical(other.clockIn, clockIn) || other.clockIn == clockIn)&&(identical(other.clockOut, clockOut) || other.clockOut == clockOut));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,clockIn,clockOut);

@override
String toString() {
  return 'RegularizationTimes(clockIn: $clockIn, clockOut: $clockOut)';
}


}

/// @nodoc
abstract mixin class $RegularizationTimesCopyWith<$Res>  {
  factory $RegularizationTimesCopyWith(RegularizationTimes value, $Res Function(RegularizationTimes) _then) = _$RegularizationTimesCopyWithImpl;
@useResult
$Res call({
 DateTime? clockIn, DateTime? clockOut
});




}
/// @nodoc
class _$RegularizationTimesCopyWithImpl<$Res>
    implements $RegularizationTimesCopyWith<$Res> {
  _$RegularizationTimesCopyWithImpl(this._self, this._then);

  final RegularizationTimes _self;
  final $Res Function(RegularizationTimes) _then;

/// Create a copy of RegularizationTimes
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? clockIn = freezed,Object? clockOut = freezed,}) {
  return _then(_self.copyWith(
clockIn: freezed == clockIn ? _self.clockIn : clockIn // ignore: cast_nullable_to_non_nullable
as DateTime?,clockOut: freezed == clockOut ? _self.clockOut : clockOut // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _RegularizationTimes implements RegularizationTimes {
  const _RegularizationTimes({this.clockIn, this.clockOut});
  factory _RegularizationTimes.fromJson(Map<String, dynamic> json) => _$RegularizationTimesFromJson(json);

@override final  DateTime? clockIn;
@override final  DateTime? clockOut;

/// Create a copy of RegularizationTimes
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RegularizationTimesCopyWith<_RegularizationTimes> get copyWith => __$RegularizationTimesCopyWithImpl<_RegularizationTimes>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$RegularizationTimesToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RegularizationTimes&&(identical(other.clockIn, clockIn) || other.clockIn == clockIn)&&(identical(other.clockOut, clockOut) || other.clockOut == clockOut));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,clockIn,clockOut);

@override
String toString() {
  return 'RegularizationTimes(clockIn: $clockIn, clockOut: $clockOut)';
}


}

/// @nodoc
abstract mixin class _$RegularizationTimesCopyWith<$Res> implements $RegularizationTimesCopyWith<$Res> {
  factory _$RegularizationTimesCopyWith(_RegularizationTimes value, $Res Function(_RegularizationTimes) _then) = __$RegularizationTimesCopyWithImpl;
@override @useResult
$Res call({
 DateTime? clockIn, DateTime? clockOut
});




}
/// @nodoc
class __$RegularizationTimesCopyWithImpl<$Res>
    implements _$RegularizationTimesCopyWith<$Res> {
  __$RegularizationTimesCopyWithImpl(this._self, this._then);

  final _RegularizationTimes _self;
  final $Res Function(_RegularizationTimes) _then;

/// Create a copy of RegularizationTimes
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? clockIn = freezed,Object? clockOut = freezed,}) {
  return _then(_RegularizationTimes(
clockIn: freezed == clockIn ? _self.clockIn : clockIn // ignore: cast_nullable_to_non_nullable
as DateTime?,clockOut: freezed == clockOut ? _self.clockOut : clockOut // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$WorkPolicyModel {

 WorkShiftPolicyModel? get workShiftPolicy; TimetablePolicyModel? get timetablePolicy;
/// Create a copy of WorkPolicyModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WorkPolicyModelCopyWith<WorkPolicyModel> get copyWith => _$WorkPolicyModelCopyWithImpl<WorkPolicyModel>(this as WorkPolicyModel, _$identity);

  /// Serializes this WorkPolicyModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WorkPolicyModel&&(identical(other.workShiftPolicy, workShiftPolicy) || other.workShiftPolicy == workShiftPolicy)&&(identical(other.timetablePolicy, timetablePolicy) || other.timetablePolicy == timetablePolicy));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,workShiftPolicy,timetablePolicy);

@override
String toString() {
  return 'WorkPolicyModel(workShiftPolicy: $workShiftPolicy, timetablePolicy: $timetablePolicy)';
}


}

/// @nodoc
abstract mixin class $WorkPolicyModelCopyWith<$Res>  {
  factory $WorkPolicyModelCopyWith(WorkPolicyModel value, $Res Function(WorkPolicyModel) _then) = _$WorkPolicyModelCopyWithImpl;
@useResult
$Res call({
 WorkShiftPolicyModel? workShiftPolicy, TimetablePolicyModel? timetablePolicy
});


$WorkShiftPolicyModelCopyWith<$Res>? get workShiftPolicy;$TimetablePolicyModelCopyWith<$Res>? get timetablePolicy;

}
/// @nodoc
class _$WorkPolicyModelCopyWithImpl<$Res>
    implements $WorkPolicyModelCopyWith<$Res> {
  _$WorkPolicyModelCopyWithImpl(this._self, this._then);

  final WorkPolicyModel _self;
  final $Res Function(WorkPolicyModel) _then;

/// Create a copy of WorkPolicyModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? workShiftPolicy = freezed,Object? timetablePolicy = freezed,}) {
  return _then(_self.copyWith(
workShiftPolicy: freezed == workShiftPolicy ? _self.workShiftPolicy : workShiftPolicy // ignore: cast_nullable_to_non_nullable
as WorkShiftPolicyModel?,timetablePolicy: freezed == timetablePolicy ? _self.timetablePolicy : timetablePolicy // ignore: cast_nullable_to_non_nullable
as TimetablePolicyModel?,
  ));
}
/// Create a copy of WorkPolicyModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WorkShiftPolicyModelCopyWith<$Res>? get workShiftPolicy {
    if (_self.workShiftPolicy == null) {
    return null;
  }

  return $WorkShiftPolicyModelCopyWith<$Res>(_self.workShiftPolicy!, (value) {
    return _then(_self.copyWith(workShiftPolicy: value));
  });
}/// Create a copy of WorkPolicyModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TimetablePolicyModelCopyWith<$Res>? get timetablePolicy {
    if (_self.timetablePolicy == null) {
    return null;
  }

  return $TimetablePolicyModelCopyWith<$Res>(_self.timetablePolicy!, (value) {
    return _then(_self.copyWith(timetablePolicy: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _WorkPolicyModel implements WorkPolicyModel {
  const _WorkPolicyModel({this.workShiftPolicy, this.timetablePolicy});
  factory _WorkPolicyModel.fromJson(Map<String, dynamic> json) => _$WorkPolicyModelFromJson(json);

@override final  WorkShiftPolicyModel? workShiftPolicy;
@override final  TimetablePolicyModel? timetablePolicy;

/// Create a copy of WorkPolicyModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WorkPolicyModelCopyWith<_WorkPolicyModel> get copyWith => __$WorkPolicyModelCopyWithImpl<_WorkPolicyModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WorkPolicyModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WorkPolicyModel&&(identical(other.workShiftPolicy, workShiftPolicy) || other.workShiftPolicy == workShiftPolicy)&&(identical(other.timetablePolicy, timetablePolicy) || other.timetablePolicy == timetablePolicy));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,workShiftPolicy,timetablePolicy);

@override
String toString() {
  return 'WorkPolicyModel(workShiftPolicy: $workShiftPolicy, timetablePolicy: $timetablePolicy)';
}


}

/// @nodoc
abstract mixin class _$WorkPolicyModelCopyWith<$Res> implements $WorkPolicyModelCopyWith<$Res> {
  factory _$WorkPolicyModelCopyWith(_WorkPolicyModel value, $Res Function(_WorkPolicyModel) _then) = __$WorkPolicyModelCopyWithImpl;
@override @useResult
$Res call({
 WorkShiftPolicyModel? workShiftPolicy, TimetablePolicyModel? timetablePolicy
});


@override $WorkShiftPolicyModelCopyWith<$Res>? get workShiftPolicy;@override $TimetablePolicyModelCopyWith<$Res>? get timetablePolicy;

}
/// @nodoc
class __$WorkPolicyModelCopyWithImpl<$Res>
    implements _$WorkPolicyModelCopyWith<$Res> {
  __$WorkPolicyModelCopyWithImpl(this._self, this._then);

  final _WorkPolicyModel _self;
  final $Res Function(_WorkPolicyModel) _then;

/// Create a copy of WorkPolicyModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? workShiftPolicy = freezed,Object? timetablePolicy = freezed,}) {
  return _then(_WorkPolicyModel(
workShiftPolicy: freezed == workShiftPolicy ? _self.workShiftPolicy : workShiftPolicy // ignore: cast_nullable_to_non_nullable
as WorkShiftPolicyModel?,timetablePolicy: freezed == timetablePolicy ? _self.timetablePolicy : timetablePolicy // ignore: cast_nullable_to_non_nullable
as TimetablePolicyModel?,
  ));
}

/// Create a copy of WorkPolicyModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WorkShiftPolicyModelCopyWith<$Res>? get workShiftPolicy {
    if (_self.workShiftPolicy == null) {
    return null;
  }

  return $WorkShiftPolicyModelCopyWith<$Res>(_self.workShiftPolicy!, (value) {
    return _then(_self.copyWith(workShiftPolicy: value));
  });
}/// Create a copy of WorkPolicyModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TimetablePolicyModelCopyWith<$Res>? get timetablePolicy {
    if (_self.timetablePolicy == null) {
    return null;
  }

  return $TimetablePolicyModelCopyWith<$Res>(_self.timetablePolicy!, (value) {
    return _then(_self.copyWith(timetablePolicy: value));
  });
}
}


/// @nodoc
mixin _$WorkShiftPolicyModel {

 int get id; String? get name; String? get startTime; String? get endTime; String? get breakDuration; String? get policy; DateTime? get createdAt; DateTime? get deletedAt; DateTime? get removedAt;@JsonKey(name: 'OfficeId') int? get officeId;
/// Create a copy of WorkShiftPolicyModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WorkShiftPolicyModelCopyWith<WorkShiftPolicyModel> get copyWith => _$WorkShiftPolicyModelCopyWithImpl<WorkShiftPolicyModel>(this as WorkShiftPolicyModel, _$identity);

  /// Serializes this WorkShiftPolicyModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WorkShiftPolicyModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.startTime, startTime) || other.startTime == startTime)&&(identical(other.endTime, endTime) || other.endTime == endTime)&&(identical(other.breakDuration, breakDuration) || other.breakDuration == breakDuration)&&(identical(other.policy, policy) || other.policy == policy)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt)&&(identical(other.removedAt, removedAt) || other.removedAt == removedAt)&&(identical(other.officeId, officeId) || other.officeId == officeId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,startTime,endTime,breakDuration,policy,createdAt,deletedAt,removedAt,officeId);

@override
String toString() {
  return 'WorkShiftPolicyModel(id: $id, name: $name, startTime: $startTime, endTime: $endTime, breakDuration: $breakDuration, policy: $policy, createdAt: $createdAt, deletedAt: $deletedAt, removedAt: $removedAt, officeId: $officeId)';
}


}

/// @nodoc
abstract mixin class $WorkShiftPolicyModelCopyWith<$Res>  {
  factory $WorkShiftPolicyModelCopyWith(WorkShiftPolicyModel value, $Res Function(WorkShiftPolicyModel) _then) = _$WorkShiftPolicyModelCopyWithImpl;
@useResult
$Res call({
 int id, String? name, String? startTime, String? endTime, String? breakDuration, String? policy, DateTime? createdAt, DateTime? deletedAt, DateTime? removedAt,@JsonKey(name: 'OfficeId') int? officeId
});




}
/// @nodoc
class _$WorkShiftPolicyModelCopyWithImpl<$Res>
    implements $WorkShiftPolicyModelCopyWith<$Res> {
  _$WorkShiftPolicyModelCopyWithImpl(this._self, this._then);

  final WorkShiftPolicyModel _self;
  final $Res Function(WorkShiftPolicyModel) _then;

/// Create a copy of WorkShiftPolicyModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = freezed,Object? startTime = freezed,Object? endTime = freezed,Object? breakDuration = freezed,Object? policy = freezed,Object? createdAt = freezed,Object? deletedAt = freezed,Object? removedAt = freezed,Object? officeId = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,startTime: freezed == startTime ? _self.startTime : startTime // ignore: cast_nullable_to_non_nullable
as String?,endTime: freezed == endTime ? _self.endTime : endTime // ignore: cast_nullable_to_non_nullable
as String?,breakDuration: freezed == breakDuration ? _self.breakDuration : breakDuration // ignore: cast_nullable_to_non_nullable
as String?,policy: freezed == policy ? _self.policy : policy // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,removedAt: freezed == removedAt ? _self.removedAt : removedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,officeId: freezed == officeId ? _self.officeId : officeId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _WorkShiftPolicyModel implements WorkShiftPolicyModel {
  const _WorkShiftPolicyModel({required this.id, this.name, this.startTime, this.endTime, this.breakDuration, this.policy, this.createdAt, this.deletedAt, this.removedAt, @JsonKey(name: 'OfficeId') this.officeId});
  factory _WorkShiftPolicyModel.fromJson(Map<String, dynamic> json) => _$WorkShiftPolicyModelFromJson(json);

@override final  int id;
@override final  String? name;
@override final  String? startTime;
@override final  String? endTime;
@override final  String? breakDuration;
@override final  String? policy;
@override final  DateTime? createdAt;
@override final  DateTime? deletedAt;
@override final  DateTime? removedAt;
@override@JsonKey(name: 'OfficeId') final  int? officeId;

/// Create a copy of WorkShiftPolicyModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WorkShiftPolicyModelCopyWith<_WorkShiftPolicyModel> get copyWith => __$WorkShiftPolicyModelCopyWithImpl<_WorkShiftPolicyModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WorkShiftPolicyModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WorkShiftPolicyModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.startTime, startTime) || other.startTime == startTime)&&(identical(other.endTime, endTime) || other.endTime == endTime)&&(identical(other.breakDuration, breakDuration) || other.breakDuration == breakDuration)&&(identical(other.policy, policy) || other.policy == policy)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt)&&(identical(other.removedAt, removedAt) || other.removedAt == removedAt)&&(identical(other.officeId, officeId) || other.officeId == officeId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,startTime,endTime,breakDuration,policy,createdAt,deletedAt,removedAt,officeId);

@override
String toString() {
  return 'WorkShiftPolicyModel(id: $id, name: $name, startTime: $startTime, endTime: $endTime, breakDuration: $breakDuration, policy: $policy, createdAt: $createdAt, deletedAt: $deletedAt, removedAt: $removedAt, officeId: $officeId)';
}


}

/// @nodoc
abstract mixin class _$WorkShiftPolicyModelCopyWith<$Res> implements $WorkShiftPolicyModelCopyWith<$Res> {
  factory _$WorkShiftPolicyModelCopyWith(_WorkShiftPolicyModel value, $Res Function(_WorkShiftPolicyModel) _then) = __$WorkShiftPolicyModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String? name, String? startTime, String? endTime, String? breakDuration, String? policy, DateTime? createdAt, DateTime? deletedAt, DateTime? removedAt,@JsonKey(name: 'OfficeId') int? officeId
});




}
/// @nodoc
class __$WorkShiftPolicyModelCopyWithImpl<$Res>
    implements _$WorkShiftPolicyModelCopyWith<$Res> {
  __$WorkShiftPolicyModelCopyWithImpl(this._self, this._then);

  final _WorkShiftPolicyModel _self;
  final $Res Function(_WorkShiftPolicyModel) _then;

/// Create a copy of WorkShiftPolicyModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = freezed,Object? startTime = freezed,Object? endTime = freezed,Object? breakDuration = freezed,Object? policy = freezed,Object? createdAt = freezed,Object? deletedAt = freezed,Object? removedAt = freezed,Object? officeId = freezed,}) {
  return _then(_WorkShiftPolicyModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,startTime: freezed == startTime ? _self.startTime : startTime // ignore: cast_nullable_to_non_nullable
as String?,endTime: freezed == endTime ? _self.endTime : endTime // ignore: cast_nullable_to_non_nullable
as String?,breakDuration: freezed == breakDuration ? _self.breakDuration : breakDuration // ignore: cast_nullable_to_non_nullable
as String?,policy: freezed == policy ? _self.policy : policy // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,removedAt: freezed == removedAt ? _self.removedAt : removedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,officeId: freezed == officeId ? _self.officeId : officeId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}


/// @nodoc
mixin _$TimetablePolicyModel {

 int get id; String? get name; double? get sun; double? get mon; double? get tue; double? get wed; double? get thu; double? get fri; double? get sat; String? get policy; DateTime? get createdAt; DateTime? get deletedAt; DateTime? get removedAt;@JsonKey(name: 'OfficeId') int? get officeId;
/// Create a copy of TimetablePolicyModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TimetablePolicyModelCopyWith<TimetablePolicyModel> get copyWith => _$TimetablePolicyModelCopyWithImpl<TimetablePolicyModel>(this as TimetablePolicyModel, _$identity);

  /// Serializes this TimetablePolicyModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TimetablePolicyModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.sun, sun) || other.sun == sun)&&(identical(other.mon, mon) || other.mon == mon)&&(identical(other.tue, tue) || other.tue == tue)&&(identical(other.wed, wed) || other.wed == wed)&&(identical(other.thu, thu) || other.thu == thu)&&(identical(other.fri, fri) || other.fri == fri)&&(identical(other.sat, sat) || other.sat == sat)&&(identical(other.policy, policy) || other.policy == policy)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt)&&(identical(other.removedAt, removedAt) || other.removedAt == removedAt)&&(identical(other.officeId, officeId) || other.officeId == officeId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,sun,mon,tue,wed,thu,fri,sat,policy,createdAt,deletedAt,removedAt,officeId);

@override
String toString() {
  return 'TimetablePolicyModel(id: $id, name: $name, sun: $sun, mon: $mon, tue: $tue, wed: $wed, thu: $thu, fri: $fri, sat: $sat, policy: $policy, createdAt: $createdAt, deletedAt: $deletedAt, removedAt: $removedAt, officeId: $officeId)';
}


}

/// @nodoc
abstract mixin class $TimetablePolicyModelCopyWith<$Res>  {
  factory $TimetablePolicyModelCopyWith(TimetablePolicyModel value, $Res Function(TimetablePolicyModel) _then) = _$TimetablePolicyModelCopyWithImpl;
@useResult
$Res call({
 int id, String? name, double? sun, double? mon, double? tue, double? wed, double? thu, double? fri, double? sat, String? policy, DateTime? createdAt, DateTime? deletedAt, DateTime? removedAt,@JsonKey(name: 'OfficeId') int? officeId
});




}
/// @nodoc
class _$TimetablePolicyModelCopyWithImpl<$Res>
    implements $TimetablePolicyModelCopyWith<$Res> {
  _$TimetablePolicyModelCopyWithImpl(this._self, this._then);

  final TimetablePolicyModel _self;
  final $Res Function(TimetablePolicyModel) _then;

/// Create a copy of TimetablePolicyModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = freezed,Object? sun = freezed,Object? mon = freezed,Object? tue = freezed,Object? wed = freezed,Object? thu = freezed,Object? fri = freezed,Object? sat = freezed,Object? policy = freezed,Object? createdAt = freezed,Object? deletedAt = freezed,Object? removedAt = freezed,Object? officeId = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,sun: freezed == sun ? _self.sun : sun // ignore: cast_nullable_to_non_nullable
as double?,mon: freezed == mon ? _self.mon : mon // ignore: cast_nullable_to_non_nullable
as double?,tue: freezed == tue ? _self.tue : tue // ignore: cast_nullable_to_non_nullable
as double?,wed: freezed == wed ? _self.wed : wed // ignore: cast_nullable_to_non_nullable
as double?,thu: freezed == thu ? _self.thu : thu // ignore: cast_nullable_to_non_nullable
as double?,fri: freezed == fri ? _self.fri : fri // ignore: cast_nullable_to_non_nullable
as double?,sat: freezed == sat ? _self.sat : sat // ignore: cast_nullable_to_non_nullable
as double?,policy: freezed == policy ? _self.policy : policy // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,removedAt: freezed == removedAt ? _self.removedAt : removedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,officeId: freezed == officeId ? _self.officeId : officeId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _TimetablePolicyModel implements TimetablePolicyModel {
  const _TimetablePolicyModel({required this.id, this.name, this.sun, this.mon, this.tue, this.wed, this.thu, this.fri, this.sat, this.policy, this.createdAt, this.deletedAt, this.removedAt, @JsonKey(name: 'OfficeId') this.officeId});
  factory _TimetablePolicyModel.fromJson(Map<String, dynamic> json) => _$TimetablePolicyModelFromJson(json);

@override final  int id;
@override final  String? name;
@override final  double? sun;
@override final  double? mon;
@override final  double? tue;
@override final  double? wed;
@override final  double? thu;
@override final  double? fri;
@override final  double? sat;
@override final  String? policy;
@override final  DateTime? createdAt;
@override final  DateTime? deletedAt;
@override final  DateTime? removedAt;
@override@JsonKey(name: 'OfficeId') final  int? officeId;

/// Create a copy of TimetablePolicyModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TimetablePolicyModelCopyWith<_TimetablePolicyModel> get copyWith => __$TimetablePolicyModelCopyWithImpl<_TimetablePolicyModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TimetablePolicyModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TimetablePolicyModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.sun, sun) || other.sun == sun)&&(identical(other.mon, mon) || other.mon == mon)&&(identical(other.tue, tue) || other.tue == tue)&&(identical(other.wed, wed) || other.wed == wed)&&(identical(other.thu, thu) || other.thu == thu)&&(identical(other.fri, fri) || other.fri == fri)&&(identical(other.sat, sat) || other.sat == sat)&&(identical(other.policy, policy) || other.policy == policy)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt)&&(identical(other.removedAt, removedAt) || other.removedAt == removedAt)&&(identical(other.officeId, officeId) || other.officeId == officeId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,sun,mon,tue,wed,thu,fri,sat,policy,createdAt,deletedAt,removedAt,officeId);

@override
String toString() {
  return 'TimetablePolicyModel(id: $id, name: $name, sun: $sun, mon: $mon, tue: $tue, wed: $wed, thu: $thu, fri: $fri, sat: $sat, policy: $policy, createdAt: $createdAt, deletedAt: $deletedAt, removedAt: $removedAt, officeId: $officeId)';
}


}

/// @nodoc
abstract mixin class _$TimetablePolicyModelCopyWith<$Res> implements $TimetablePolicyModelCopyWith<$Res> {
  factory _$TimetablePolicyModelCopyWith(_TimetablePolicyModel value, $Res Function(_TimetablePolicyModel) _then) = __$TimetablePolicyModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String? name, double? sun, double? mon, double? tue, double? wed, double? thu, double? fri, double? sat, String? policy, DateTime? createdAt, DateTime? deletedAt, DateTime? removedAt,@JsonKey(name: 'OfficeId') int? officeId
});




}
/// @nodoc
class __$TimetablePolicyModelCopyWithImpl<$Res>
    implements _$TimetablePolicyModelCopyWith<$Res> {
  __$TimetablePolicyModelCopyWithImpl(this._self, this._then);

  final _TimetablePolicyModel _self;
  final $Res Function(_TimetablePolicyModel) _then;

/// Create a copy of TimetablePolicyModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = freezed,Object? sun = freezed,Object? mon = freezed,Object? tue = freezed,Object? wed = freezed,Object? thu = freezed,Object? fri = freezed,Object? sat = freezed,Object? policy = freezed,Object? createdAt = freezed,Object? deletedAt = freezed,Object? removedAt = freezed,Object? officeId = freezed,}) {
  return _then(_TimetablePolicyModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,sun: freezed == sun ? _self.sun : sun // ignore: cast_nullable_to_non_nullable
as double?,mon: freezed == mon ? _self.mon : mon // ignore: cast_nullable_to_non_nullable
as double?,tue: freezed == tue ? _self.tue : tue // ignore: cast_nullable_to_non_nullable
as double?,wed: freezed == wed ? _self.wed : wed // ignore: cast_nullable_to_non_nullable
as double?,thu: freezed == thu ? _self.thu : thu // ignore: cast_nullable_to_non_nullable
as double?,fri: freezed == fri ? _self.fri : fri // ignore: cast_nullable_to_non_nullable
as double?,sat: freezed == sat ? _self.sat : sat // ignore: cast_nullable_to_non_nullable
as double?,policy: freezed == policy ? _self.policy : policy // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,removedAt: freezed == removedAt ? _self.removedAt : removedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,officeId: freezed == officeId ? _self.officeId : officeId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on
