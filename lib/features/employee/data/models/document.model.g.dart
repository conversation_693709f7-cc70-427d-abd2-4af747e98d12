// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'document.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_DocumentCategoryModel _$DocumentCategoryModelFromJson(
  Map<String, dynamic> json,
) => _DocumentCategoryModel(
  id: (json['docCategory_id'] as num).toInt(),
  name: json['docCategory_name'] as String?,
  count: json['documentCount'] as String?,
);

Map<String, dynamic> _$DocumentCategoryModelToJson(
  _DocumentCategoryModel instance,
) => <String, dynamic>{
  'docCategory_id': instance.id,
  'docCategory_name': instance.name,
  'documentCount': instance.count,
};

_SingleDocumentModel _$SingleDocumentModelFromJson(Map<String, dynamic> json) =>
    _SingleDocumentModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      categoryId: (json['DocCategoryId'] as num?)?.toInt(),
      data: docDataFromDocumentJson(json['userDocs'] as List),
    );

Map<String, dynamic> _$SingleDocumentModelToJson(
  _SingleDocumentModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'createdAt': instance.createdAt?.toIso8601String(),
  'DocCategoryId': instance.categoryId,
  'userDocs': instance.data,
};

_DocumentDataModel _$DocumentDataModelFromJson(Map<String, dynamic> json) =>
    _DocumentDataModel(
      docLink: json['docLink'] as String?,
      docNumber: json['docNumber'] as String?,
    );

Map<String, dynamic> _$DocumentDataModelToJson(_DocumentDataModel instance) =>
    <String, dynamic>{
      'docLink': instance.docLink,
      'docNumber': instance.docNumber,
    };
