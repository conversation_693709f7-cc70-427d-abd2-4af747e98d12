// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'document.model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DocumentCategoryModel {

@JsonKey(name: 'docCategory_id') int get id;@JsonKey(name: 'docCategory_name') String? get name;@JsonKey(name: 'documentCount') String? get count;
/// Create a copy of DocumentCategoryModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DocumentCategoryModelCopyWith<DocumentCategoryModel> get copyWith => _$DocumentCategoryModelCopyWithImpl<DocumentCategoryModel>(this as DocumentCategoryModel, _$identity);

  /// Serializes this DocumentCategoryModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DocumentCategoryModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.count, count) || other.count == count));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,count);

@override
String toString() {
  return 'DocumentCategoryModel(id: $id, name: $name, count: $count)';
}


}

/// @nodoc
abstract mixin class $DocumentCategoryModelCopyWith<$Res>  {
  factory $DocumentCategoryModelCopyWith(DocumentCategoryModel value, $Res Function(DocumentCategoryModel) _then) = _$DocumentCategoryModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'docCategory_id') int id,@JsonKey(name: 'docCategory_name') String? name,@JsonKey(name: 'documentCount') String? count
});




}
/// @nodoc
class _$DocumentCategoryModelCopyWithImpl<$Res>
    implements $DocumentCategoryModelCopyWith<$Res> {
  _$DocumentCategoryModelCopyWithImpl(this._self, this._then);

  final DocumentCategoryModel _self;
  final $Res Function(DocumentCategoryModel) _then;

/// Create a copy of DocumentCategoryModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = freezed,Object? count = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,count: freezed == count ? _self.count : count // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _DocumentCategoryModel implements DocumentCategoryModel {
  const _DocumentCategoryModel({@JsonKey(name: 'docCategory_id') required this.id, @JsonKey(name: 'docCategory_name') this.name, @JsonKey(name: 'documentCount') this.count});
  factory _DocumentCategoryModel.fromJson(Map<String, dynamic> json) => _$DocumentCategoryModelFromJson(json);

@override@JsonKey(name: 'docCategory_id') final  int id;
@override@JsonKey(name: 'docCategory_name') final  String? name;
@override@JsonKey(name: 'documentCount') final  String? count;

/// Create a copy of DocumentCategoryModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DocumentCategoryModelCopyWith<_DocumentCategoryModel> get copyWith => __$DocumentCategoryModelCopyWithImpl<_DocumentCategoryModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DocumentCategoryModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DocumentCategoryModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.count, count) || other.count == count));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,count);

@override
String toString() {
  return 'DocumentCategoryModel(id: $id, name: $name, count: $count)';
}


}

/// @nodoc
abstract mixin class _$DocumentCategoryModelCopyWith<$Res> implements $DocumentCategoryModelCopyWith<$Res> {
  factory _$DocumentCategoryModelCopyWith(_DocumentCategoryModel value, $Res Function(_DocumentCategoryModel) _then) = __$DocumentCategoryModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'docCategory_id') int id,@JsonKey(name: 'docCategory_name') String? name,@JsonKey(name: 'documentCount') String? count
});




}
/// @nodoc
class __$DocumentCategoryModelCopyWithImpl<$Res>
    implements _$DocumentCategoryModelCopyWith<$Res> {
  __$DocumentCategoryModelCopyWithImpl(this._self, this._then);

  final _DocumentCategoryModel _self;
  final $Res Function(_DocumentCategoryModel) _then;

/// Create a copy of DocumentCategoryModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = freezed,Object? count = freezed,}) {
  return _then(_DocumentCategoryModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,count: freezed == count ? _self.count : count // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$SingleDocumentModel {

 int get id; String? get name; DateTime? get createdAt;@JsonKey(name: 'DocCategoryId') int? get categoryId;@JsonKey(name: 'userDocs', fromJson: docDataFromDocumentJson) DocumentDataModel? get data;
/// Create a copy of SingleDocumentModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SingleDocumentModelCopyWith<SingleDocumentModel> get copyWith => _$SingleDocumentModelCopyWithImpl<SingleDocumentModel>(this as SingleDocumentModel, _$identity);

  /// Serializes this SingleDocumentModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SingleDocumentModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.categoryId, categoryId) || other.categoryId == categoryId)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,createdAt,categoryId,data);

@override
String toString() {
  return 'SingleDocumentModel(id: $id, name: $name, createdAt: $createdAt, categoryId: $categoryId, data: $data)';
}


}

/// @nodoc
abstract mixin class $SingleDocumentModelCopyWith<$Res>  {
  factory $SingleDocumentModelCopyWith(SingleDocumentModel value, $Res Function(SingleDocumentModel) _then) = _$SingleDocumentModelCopyWithImpl;
@useResult
$Res call({
 int id, String? name, DateTime? createdAt,@JsonKey(name: 'DocCategoryId') int? categoryId,@JsonKey(name: 'userDocs', fromJson: docDataFromDocumentJson) DocumentDataModel? data
});


$DocumentDataModelCopyWith<$Res>? get data;

}
/// @nodoc
class _$SingleDocumentModelCopyWithImpl<$Res>
    implements $SingleDocumentModelCopyWith<$Res> {
  _$SingleDocumentModelCopyWithImpl(this._self, this._then);

  final SingleDocumentModel _self;
  final $Res Function(SingleDocumentModel) _then;

/// Create a copy of SingleDocumentModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = freezed,Object? createdAt = freezed,Object? categoryId = freezed,Object? data = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,categoryId: freezed == categoryId ? _self.categoryId : categoryId // ignore: cast_nullable_to_non_nullable
as int?,data: freezed == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as DocumentDataModel?,
  ));
}
/// Create a copy of SingleDocumentModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DocumentDataModelCopyWith<$Res>? get data {
    if (_self.data == null) {
    return null;
  }

  return $DocumentDataModelCopyWith<$Res>(_self.data!, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _SingleDocumentModel implements SingleDocumentModel {
  const _SingleDocumentModel({required this.id, this.name, this.createdAt, @JsonKey(name: 'DocCategoryId') this.categoryId, @JsonKey(name: 'userDocs', fromJson: docDataFromDocumentJson) this.data});
  factory _SingleDocumentModel.fromJson(Map<String, dynamic> json) => _$SingleDocumentModelFromJson(json);

@override final  int id;
@override final  String? name;
@override final  DateTime? createdAt;
@override@JsonKey(name: 'DocCategoryId') final  int? categoryId;
@override@JsonKey(name: 'userDocs', fromJson: docDataFromDocumentJson) final  DocumentDataModel? data;

/// Create a copy of SingleDocumentModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SingleDocumentModelCopyWith<_SingleDocumentModel> get copyWith => __$SingleDocumentModelCopyWithImpl<_SingleDocumentModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SingleDocumentModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SingleDocumentModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.categoryId, categoryId) || other.categoryId == categoryId)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,createdAt,categoryId,data);

@override
String toString() {
  return 'SingleDocumentModel(id: $id, name: $name, createdAt: $createdAt, categoryId: $categoryId, data: $data)';
}


}

/// @nodoc
abstract mixin class _$SingleDocumentModelCopyWith<$Res> implements $SingleDocumentModelCopyWith<$Res> {
  factory _$SingleDocumentModelCopyWith(_SingleDocumentModel value, $Res Function(_SingleDocumentModel) _then) = __$SingleDocumentModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String? name, DateTime? createdAt,@JsonKey(name: 'DocCategoryId') int? categoryId,@JsonKey(name: 'userDocs', fromJson: docDataFromDocumentJson) DocumentDataModel? data
});


@override $DocumentDataModelCopyWith<$Res>? get data;

}
/// @nodoc
class __$SingleDocumentModelCopyWithImpl<$Res>
    implements _$SingleDocumentModelCopyWith<$Res> {
  __$SingleDocumentModelCopyWithImpl(this._self, this._then);

  final _SingleDocumentModel _self;
  final $Res Function(_SingleDocumentModel) _then;

/// Create a copy of SingleDocumentModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = freezed,Object? createdAt = freezed,Object? categoryId = freezed,Object? data = freezed,}) {
  return _then(_SingleDocumentModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,categoryId: freezed == categoryId ? _self.categoryId : categoryId // ignore: cast_nullable_to_non_nullable
as int?,data: freezed == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as DocumentDataModel?,
  ));
}

/// Create a copy of SingleDocumentModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DocumentDataModelCopyWith<$Res>? get data {
    if (_self.data == null) {
    return null;
  }

  return $DocumentDataModelCopyWith<$Res>(_self.data!, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
mixin _$DocumentDataModel {

@JsonKey(name: 'docLink') String? get docLink;@JsonKey(name: 'docNumber') String? get docNumber;
/// Create a copy of DocumentDataModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DocumentDataModelCopyWith<DocumentDataModel> get copyWith => _$DocumentDataModelCopyWithImpl<DocumentDataModel>(this as DocumentDataModel, _$identity);

  /// Serializes this DocumentDataModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DocumentDataModel&&(identical(other.docLink, docLink) || other.docLink == docLink)&&(identical(other.docNumber, docNumber) || other.docNumber == docNumber));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,docLink,docNumber);

@override
String toString() {
  return 'DocumentDataModel(docLink: $docLink, docNumber: $docNumber)';
}


}

/// @nodoc
abstract mixin class $DocumentDataModelCopyWith<$Res>  {
  factory $DocumentDataModelCopyWith(DocumentDataModel value, $Res Function(DocumentDataModel) _then) = _$DocumentDataModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'docLink') String? docLink,@JsonKey(name: 'docNumber') String? docNumber
});




}
/// @nodoc
class _$DocumentDataModelCopyWithImpl<$Res>
    implements $DocumentDataModelCopyWith<$Res> {
  _$DocumentDataModelCopyWithImpl(this._self, this._then);

  final DocumentDataModel _self;
  final $Res Function(DocumentDataModel) _then;

/// Create a copy of DocumentDataModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? docLink = freezed,Object? docNumber = freezed,}) {
  return _then(_self.copyWith(
docLink: freezed == docLink ? _self.docLink : docLink // ignore: cast_nullable_to_non_nullable
as String?,docNumber: freezed == docNumber ? _self.docNumber : docNumber // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _DocumentDataModel implements DocumentDataModel {
  const _DocumentDataModel({@JsonKey(name: 'docLink') this.docLink, @JsonKey(name: 'docNumber') this.docNumber});
  factory _DocumentDataModel.fromJson(Map<String, dynamic> json) => _$DocumentDataModelFromJson(json);

@override@JsonKey(name: 'docLink') final  String? docLink;
@override@JsonKey(name: 'docNumber') final  String? docNumber;

/// Create a copy of DocumentDataModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DocumentDataModelCopyWith<_DocumentDataModel> get copyWith => __$DocumentDataModelCopyWithImpl<_DocumentDataModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DocumentDataModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DocumentDataModel&&(identical(other.docLink, docLink) || other.docLink == docLink)&&(identical(other.docNumber, docNumber) || other.docNumber == docNumber));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,docLink,docNumber);

@override
String toString() {
  return 'DocumentDataModel(docLink: $docLink, docNumber: $docNumber)';
}


}

/// @nodoc
abstract mixin class _$DocumentDataModelCopyWith<$Res> implements $DocumentDataModelCopyWith<$Res> {
  factory _$DocumentDataModelCopyWith(_DocumentDataModel value, $Res Function(_DocumentDataModel) _then) = __$DocumentDataModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'docLink') String? docLink,@JsonKey(name: 'docNumber') String? docNumber
});




}
/// @nodoc
class __$DocumentDataModelCopyWithImpl<$Res>
    implements _$DocumentDataModelCopyWith<$Res> {
  __$DocumentDataModelCopyWithImpl(this._self, this._then);

  final _DocumentDataModel _self;
  final $Res Function(_DocumentDataModel) _then;

/// Create a copy of DocumentDataModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? docLink = freezed,Object? docNumber = freezed,}) {
  return _then(_DocumentDataModel(
docLink: freezed == docLink ? _self.docLink : docLink // ignore: cast_nullable_to_non_nullable
as String?,docNumber: freezed == docNumber ? _self.docNumber : docNumber // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
