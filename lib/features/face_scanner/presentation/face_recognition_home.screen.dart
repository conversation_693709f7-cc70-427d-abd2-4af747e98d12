import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/snackbar.helper.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/face_scanner/domain/face_registration.controller.dart';
import 'package:hrms_tst/features/face_scanner/presentation/face_registration.screen.dart';
import 'package:hrms_tst/features/face_scanner/presentation/face_scan.screen.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';

class FaceRecognitionHomeScreen extends ConsumerWidget {
  const FaceRecognitionHomeScreen({super.key});

  static const String name = 'Face Recognition';
  static const String path = '/face-recognition';

  static GoRoute route({List<RouteBase> routes = const []}) => GoRoute(
    path: path,
    name: name,
    routes: routes,
    pageBuilder: (context, state) =>
        const NoTransitionPage(child: FaceRecognitionHomeScreen()),
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(title: const Text('Face Recognition'), centerTitle: true),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            _FeatureCard(
              title: 'Face Registration',
              description: 'Register employee face',
              icon: Icons.face_retouching_natural_rounded,
              onTap: () => context.pushNamed(FaceRegistrationScreen.name),
            ),
            Space.y(12),
            _FeatureCard(
              title: 'Attendance Scanner',
              description: 'Scan face for attendance',
              icon: Icons.face_rounded,
              onTap: () => context.pushNamed(FaceScanScreen.name),
            ),
            Space.y(12),
            _FeatureCard(
              title: 'Clear Embeddings',
              description: 'Clear all saved embeddings',
              icon: Icons.delete,
              onTap: () {
                ref.invalidate(embeddingsProvider);
                context.showInformation('Deleted all saved embeddings');
              },
            ),
            Space.y(12),
            ...ref
                .watch(embeddingsProvider)
                .indexed
                .map((e) => ListTile(title: Text('${e.$1 + 1}. ${e.$2.$1}'))),
          ],
        ),
      ),
    );
  }
}

class _FeatureCard extends StatelessWidget {
  const _FeatureCard({
    required this.title,
    required this.description,
    required this.icon,
    required this.onTap,
  });

  final String title;
  final String description;
  final IconData icon;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return CardContainer(
      child: ListTile(
        onTap: onTap,
        contentPadding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        leading: Container(
          padding: const EdgeInsets.all(14),
          decoration: BoxDecoration(
            color: context.colors.primaryContainer,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(icon, color: context.colors.onPrimaryContainer),
        ),
        title: Text(title, style: Theme.of(context).textTheme.titleMedium),
        subtitle: Text(
          description,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).textTheme.bodySmall?.color,
          ),
        ),
        trailing: Icon(
          Icons.chevron_right_outlined,
          color: context.colors.onSurfaceVariant,
        ),
      ),
    );
  }
}
