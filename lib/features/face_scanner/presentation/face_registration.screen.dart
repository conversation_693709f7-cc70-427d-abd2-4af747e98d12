import 'package:face_camera/face_camera.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/features/face_scanner/domain/face_registration.controller.dart';

class FaceRegistrationScreen extends ConsumerWidget {
  const FaceRegistrationScreen({super.key});

  static const String name = 'Face Registration';
  static const String path = 'registration';

  static GoRoute route({List<RouteBase> routes = const []}) => GoRoute(
    path: path,
    name: name,
    routes: routes,
    pageBuilder: (context, state) =>
        const NoTransitionPage(child: FaceRegistrationScreen()),
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(faceRegistrationProvider);
    final controller = ref.watch(faceRegistrationProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Face Registration'),
        backgroundColor: Colors.transparent,
      ),
      resizeToAvoidBottomInset: false,
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // Camera Preview with Face Guide
          switch (state) {
            AsyncData<FaceCameraController>(:final value) => Stack(
              children: [
                SmartFaceCamera(controller: value),
                // Overlay to prevent control interactions
                const Positioned.fill(
                  child: IgnorePointer(
                    child: ColoredBox(color: Colors.transparent),
                  ),
                ),
                // Face guide overlay
                if (controller.status == FaceRegistrationStatus.processing)
                  Center(
                    child: Container(
                      width: 250,
                      height: 250,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.white, width: 2),
                        shape: BoxShape.circle,
                      ),
                      child: const Center(
                        child: Text(
                          'Position face here',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            shadows: [
                              Shadow(
                                blurRadius: 4,
                                color: Colors.black,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            AsyncError(:final error) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 16),
                  Text('Error: $error'),
                ],
              ),
            ),
            _ => const Center(child: CircularProgressIndicator()),
          },

          // Status Overlay
          if (controller.status == FaceRegistrationStatus.processing)
            Container(
              color: Colors.black54,
              child: const Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(color: Colors.white),
                    SizedBox(height: 16),
                    Text(
                      'Processing...',
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),

          // Message Banner
          if (controller.message != null)
            Positioned(
              bottom: 20,
              left: 20,
              right: 20,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: switch (controller.status) {
                    FaceRegistrationStatus.success => Colors.green.withValues(
                      alpha: 0.9,
                    ),
                    FaceRegistrationStatus.error => Colors.red.withValues(
                      alpha: 0.9,
                    ),
                    FaceRegistrationStatus.processing => Colors.blue.withValues(
                      alpha: 0.9,
                    ),
                    _ => Colors.grey.withValues(alpha: 0.9),
                  },
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.9),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      switch (controller.status) {
                        FaceRegistrationStatus.success => Icons.check_circle,
                        FaceRegistrationStatus.error => Icons.error,
                        FaceRegistrationStatus.processing => Icons.face,
                        _ => Icons.info,
                      },
                      color: Colors.white,
                      size: 32,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      controller.message!,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

          // Cropped image display
          if (controller.lastCroppedImage != null)
            Positioned(
              top: 100,
              right: 20,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.white, width: 2),
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(6),
                  child: Image.memory(
                    controller.lastCroppedImage!,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),

          // Employee Info Input
          if (controller.status == FaceRegistrationStatus.idle)
            Positioned(
              bottom: 20 + MediaQuery.viewInsetsOf(context).bottom,
              left: 20,
              right: 20,
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextField(
                        controller: controller.employeeIdController,
                        decoration: const InputDecoration(
                          labelText: 'Employee ID',
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: controller.startRegistration,
                        child: const Text('Start Registration'),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
