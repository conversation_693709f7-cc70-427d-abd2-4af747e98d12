import 'package:face_camera/face_camera.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/face_scanner/domain/face_recognizer.controller.dart';

class FaceScanScreen extends ConsumerWidget {
  const FaceScanScreen({super.key});

  static const String name = 'Face Scanner';
  static const String path = 'scanner';

  static GoRoute route({List<RouteBase> routes = const []}) => GoRoute(
    path: path,
    name: name,
    routes: routes,
    pageBuilder: (context, state) =>
        const NoTransitionPage(child: FaceScanScreen()),
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(faceRecognizerProvider);
    final controller = ref.watch(faceRecognizerProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Face Attendance'),
        backgroundColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
      ),
      extendBodyBehindAppBar: true,
      body: Stack(
        fit: StackFit.expand,
        children: [
          switch (state) {
            AsyncData<FaceCameraController>(:final value) => Stack(
              children: [
                SmartFaceCamera(
                  controller: value,
                  showControls: false,
                  showCaptureControl: false,
                ),
                Positioned.fill(
                  child: Center(
                    child: Container(
                      width: context.screenWidth * 0.8,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.white, width: 2),
                        shape: BoxShape.circle,
                      ),
                      child: const Center(
                        child: Text(
                          'Position face here',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            shadows: [
                              Shadow(blurRadius: 4, color: Colors.black),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            AsyncError(:final error) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 16),
                  Text('Error: $error'),
                ],
              ),
            ),
            _ => const Center(child: CircularProgressIndicator()),
          },

          if (controller.message != null)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 600),
                switchInCurve: Curves.easeIn,
                switchOutCurve: Curves.ease,
                transitionBuilder: (child, animation) {
                  return SlideTransition(
                    position:
                        Tween<Offset>(
                          begin: const Offset(0, 1),
                          end: Offset.zero,
                        ).animate(
                          CurvedAnimation(
                            parent: animation,
                            curve: Curves.easeOutCubic,
                            reverseCurve: Curves.easeInCubic,
                          ),
                        ),
                    child: child,
                  );
                },
                child: switch (controller.status) {
                  FaceRecognizerStatus.processing => processing(context),
                  FaceRecognizerStatus.success => success(context),
                  FaceRecognizerStatus.error => failure(context),
                  _ => const SizedBox.shrink(),
                },
              ),
            ),

          // Cropped image display
          if (controller.lastCroppedImage != null)
            Positioned(
              top: 100,
              right: 20,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.white, width: 2),
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(6),
                  child: Image.memory(
                    controller.lastCroppedImage!,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget processing(BuildContext context) {
    return Container(
      key: const ValueKey('processing'),
      margin: const EdgeInsets.all(16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.colors.secondary,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          SizedBox.square(
            dimension: 48,
            child: Padding(
              padding: const EdgeInsets.all(4.0),
              child: const CircularProgressIndicator(color: Colors.white),
            ),
          ),
          Space.x(16),
          Text(
            'Processing...',
            style: context.textStyles.bodyLarge?.copyWith(
              color: context.colors.onSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget success(BuildContext context) {
    return Container(
      key: const ValueKey('success'),
      margin: const EdgeInsets.all(16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.colors.primary,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(Icons.check_circle, color: context.colors.surface, size: 48),
          Space.x(16),
          Text(
            'Face recognized successfully!',
            style: context.textStyles.bodyLarge?.copyWith(
              color: context.colors.onSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget failure(BuildContext context) {
    return Container(
      key: const ValueKey('failure'),
      margin: const EdgeInsets.all(16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.colors.errorContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(Icons.cancel, size: 48, color: context.colors.error),
          Space.x(16),
          Text(
            'Face not recognized.',
            textAlign: TextAlign.center,
            style: context.textStyles.bodyLarge?.copyWith(
              color: context.colors.onErrorContainer,
            ),
          ),
        ],
      ),
    );
  }
}
