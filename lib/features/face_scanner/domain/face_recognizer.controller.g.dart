// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'face_recognizer.controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$faceRecognizerHash() => r'b64d4c88340a0d8b4ad31d49556136613cdff147';

/// See also [FaceRecognizer].
@ProviderFor(FaceRecognizer)
final faceRecognizerProvider =
    AutoDisposeAsyncNotifierProvider<
      FaceRecognizer,
      FaceCameraController
    >.internal(
      FaceRecognizer.new,
      name: r'faceRecognizerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$faceRecognizerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$FaceRecognizer = AutoDisposeAsyncNotifier<FaceCameraController>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
