import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';

import 'package:face_camera/face_camera.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/services/face_recognization/face_recognition_utils.dart';
import 'package:hrms_tst/core/services/face_recognization/face_recognization.service.dart';
import 'package:image/image.dart' as img;
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'face_registration.controller.g.dart';

enum FaceRegistrationStatus { idle, registration, processing, success, error }

final embeddingsProvider = StateProvider<List<(String id, List<double>)>>((
  ref,
) {
  return [];
});

@riverpod
class FaceRegistration extends _$FaceRegistration {
  final faceService = FaceRecognizationService();

  late final FaceCameraController _cameraController;
  FaceRegistrationStatus _status = FaceRegistrationStatus.idle;
  String? _message;
  bool _isProcessing = false;
  DateTime? _lastProcessTime;
  Uint8List? _lastCroppedImage;

  final employeeIdController = TextEditingController();

  FaceRegistrationStatus get status => _status;
  String? get message => _message;
  bool get isRegistering => _isProcessing;
  Uint8List? get lastCroppedImage => _lastCroppedImage;

  @override
  FutureOr<FaceCameraController> build() async {
    _resetState();
    await faceService.loadModel();

    _cameraController = FaceCameraController(
      defaultCameraLens: CameraLens.front,
      performanceMode: FaceDetectorMode.accurate,
      enableAudio: false,
      onCapture: (image) {},
      onFaceDetected: (face) => _handleFaceDetected(face),
    );

    ref.onDispose(() {
      employeeIdController.dispose();
      _cameraController.dispose();
      faceService.dispose();
    });

    return _cameraController;
  }

  void startRegistration() {
    if (employeeIdController.text.trim().isEmpty) {
      _updateState(
        status: FaceRegistrationStatus.error,
        message: 'Please enter an Employee ID',
      );
      return;
    }

    _updateState(
      status: FaceRegistrationStatus.registration,
      message: 'Looking for face...',
    );
  }

  Future<void> _handleFaceDetected(Face? face) async {
    if (face == null || _isProcessing) return;
    if (_status != FaceRegistrationStatus.registration) return;

    final now = DateTime.now();
    if (_lastProcessTime != null &&
        now.difference(_lastProcessTime!) < const Duration(seconds: 2)) {
      return;
    }

    _isProcessing = true;
    _lastProcessTime = now;
    _updateState(
      status: FaceRegistrationStatus.processing,
      message: 'Stay still, capturing...',
    );

    try {
      final file = await _cameraController.takePicture();
      if (file == null) {
        await _handleError('Failed to capture image');
        return;
      }
      final fileData = await File(file.path).readAsBytes();
      final image = img.decodeImage(fileData);
      if (image == null) {
        await _handleError('Failed to process image');
        return;
      }

      await _processImage(image, face);
    } catch (e) {
      await _handleError('Failed to capture image: $e');
    }
  }

  Future<void> _processImage(img.Image faceImage, Face face) async {
    try {
      final cropped = faceService.cropFace(faceImage, face);
      _lastCroppedImage = Uint8List.fromList(img.encodePng(cropped));

      _updateState(
        status: FaceRegistrationStatus.processing,
        message: 'Processing face data...',
      );

      final embedding = await faceService.getEmbedding(faceImage, face);

      final success = await registerEmployeeEmbedding(
        employeeIdController.text.trim(),
        embedding,
      );

      String message;
      if (success) {
        message = 'Face registered successfully!';
        _updateState(status: FaceRegistrationStatus.success, message: message);
        await Future.delayed(const Duration(seconds: 3));
        employeeIdController.clear();
      } else {
        message = 'Failed to register face. Please try again.';
        _updateState(status: FaceRegistrationStatus.error, message: message);
        await Future.delayed(const Duration(seconds: 2));
      }
      _resetState();
    } catch (e) {
      await _handleError('An error occurred: $e');
    }
  }

  Future<void> _handleError(String message) async {
    _updateState(status: FaceRegistrationStatus.error, message: message);
    await Future.delayed(const Duration(seconds: 2));
    _resetState();
  }

  void _updateState({
    required FaceRegistrationStatus status,
    required String message,
  }) {
    _status = status;
    _message = message;
    ref.notifyListeners();
  }

  void _resetState() {
    _status = FaceRegistrationStatus.idle;
    _message = null;
    _isProcessing = false;
    _lastProcessTime = null;
    _lastCroppedImage = null;
    ref.notifyListeners();

    // Resume face detection if needed
    if (_status == FaceRegistrationStatus.processing) {
      _cameraController.startImageStream();
    }
  }

  Future<bool> registerEmployeeEmbedding(
    String id,
    List<double> embedding,
  ) async {
    final previousState = ref.read(embeddingsProvider);
    ref.read(embeddingsProvider.notifier).state = [
      ...previousState,
      (id, embedding),
    ];

    final isSame = FaceRecognizationUtils.hasSimiliarity(
      a: embedding,
      b: embedding,
    );
    log(
      'Registered face $id validation result: $isSame',
      name: 'FaceRecognizer',
    );

    return true;
  }
}
