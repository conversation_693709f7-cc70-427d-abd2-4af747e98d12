import 'dart:io';
import 'dart:typed_data';

import 'dart:developer' as dev;

import 'package:face_camera/face_camera.dart';
import 'package:hrms_tst/core/services/face_recognization/face_recognition_utils.dart';
import 'package:hrms_tst/core/services/face_recognization/face_recognization.service.dart';
import 'package:hrms_tst/features/face_scanner/domain/face_registration.controller.dart';
import 'package:image/image.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'face_recognizer.controller.g.dart';

enum FaceRecognizerStatus { idle, processing, success, error }

@riverpod
class FaceRecognizer extends _$FaceRecognizer {
  final faceService = FaceRecognizationService();

  FaceRecognizerStatus _status = FaceRecognizerStatus.idle;
  String? _message;
  bool _isProcessing = false;
  Uint8List? _lastCroppedImage;

  FaceRecognizerStatus get status => _status;
  String? get message => _message;
  Uint8List? get lastCroppedImage => _lastCroppedImage;

  DateTime? _lastProcessTime;

  @override
  FutureOr<FaceCameraController> build() async {
    _resetState();
    await faceService.loadModel();

    final controller = FaceCameraController(
      defaultCameraLens: CameraLens.front,
      performanceMode: FaceDetectorMode.accurate,
      enableAudio: false,
      onCapture: (file) async {},
      onFaceDetected: _handleFaceDetected,
    );

    ref.onDispose(() async {
      await faceService.dispose();
      await controller.dispose();
    });

    return controller;
  }

  Future<void> _handleFaceDetected(Face? face) async {
    if (face == null || _isProcessing) return;

    final now = DateTime.now();
    if (_lastProcessTime != null &&
        now.difference(_lastProcessTime!) < const Duration(seconds: 2)) {
      return;
    }

    _isProcessing = true;
    _lastProcessTime = now;
    _updateState(
      status: FaceRecognizerStatus.processing,
      message: 'Stay still, capturing...',
    );

    try {
      // Trigger auto-capture when face is detected
      final file = await state.requireValue.takePicture();
      if (file == null) {
        await _handleError('No face detected, please try again.');
        return;
      }
      // Get the captured image and crop it using the detected face bounds
      final fileData = await File(file.path).readAsBytes();
      final image = decodeImage(fileData);
      if (image == null) {
        await _handleError('Failed to process image');
        return;
      }

      await _processImage(image, face);
    } catch (e) {
      await _handleError('Failed to capture image: $e');
    }
  }

  Future<void> _processImage(Image croppedFace, Face face) async {
    try {
      final image = croppedFace;

      // Save cropped image for debugging/display
      final cropped = faceService.cropFace(image, face);
      _lastCroppedImage = Uint8List.fromList(encodePng(cropped));

      _updateState(
        status: FaceRecognizerStatus.processing,
        message: 'Verifying your identity...',
      );

      final embedding = await faceService.getEmbedding(image, face);
      final result = await verifyEmbedding(embedding);

      final isSuccess = result != null && result.$2 <= 1;

      if (isSuccess) {
        _updateState(
          status: FaceRecognizerStatus.success,
          message: 'Face Verified: ${result.$1}',
        );
      } else {
        _updateState(
          status: FaceRecognizerStatus.error,
          message: 'Face Verification Failed',
        );
      }

      // Wait and reset state based on response
      await Future.delayed(Duration(seconds: isSuccess ? 3 : 2));
      _resetState();
    } catch (e) {
      await _handleError('An error occurred: $e');
    }
  }

  Future<void> _handleError(String message) async {
    _updateState(status: FaceRecognizerStatus.error, message: message);
    await Future.delayed(const Duration(seconds: 2));
    _resetState();
  }

  void _updateState({
    required FaceRecognizerStatus status,
    required String message,
  }) {
    _status = status;
    _message = message;
    ref.notifyListeners();
  }

  void _resetState() {
    _status = FaceRecognizerStatus.idle;
    _message = null;
    _isProcessing = false;
    _lastProcessTime = null;
    _lastCroppedImage = null;
    ref.notifyListeners();
  }

  Future<(String id, double score)?> verifyEmbedding(
    List<double> embedding,
  ) async {
    final embeddings = ref.read(embeddingsProvider);

    if (embedding.isEmpty) return null;

    /// search the closest result 👓
    for (final entry in embeddings) {
      final isSimilar = FaceRecognizationUtils.hasSimiliarity(
        a: entry.$2,
        b: embedding,
      );
      if (isSimilar) {
        dev.log('Face verified: ${entry.$1}', name: 'FaceRecognizer');
        return (entry.$1, 0.0);
      }
    }

    dev.log('No similar face found', name: 'FaceRecognizer');

    return null;
  }
}
