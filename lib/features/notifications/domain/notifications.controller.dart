import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:hrms_tst/features/notifications/data/models/notification.model.dart';
import 'package:hrms_tst/features/notifications/data/notifications.protocol.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'notifications.controller.g.dart';

@riverpod
class NotificationsController extends _$NotificationsController {
  @override
  FutureOr<NotificationListResponse> build({
    int page = 1,
    int limit = 20,
    NotificationType? type,
    bool? isRead,
  }) async {
    final result = await getIt.get<NotificationsProtocol>().getNotifications(
      page: page,
      limit: limit,
      type: type,
      isRead: isRead,
    );

    return result.fold(
      onSuccess: (data) => data,
      onFailure: (error) => throw Exception(error),
    );
  }
}

@Riverpod(keepAlive: true)
class NotificationCount extends _$NotificationCount {
  @override
  int build() {
    return 0;
  }

  void set(int count) {
    state = count;
  }

  void clear() {
    state = 0;
  }
}
