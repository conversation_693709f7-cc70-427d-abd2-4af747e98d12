// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notifications.controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$notificationsControllerHash() =>
    r'c697e55e34ace53570475dfbdfc83cde6cc106eb';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$NotificationsController
    extends BuildlessAutoDisposeAsyncNotifier<NotificationListResponse> {
  late final int page;
  late final int limit;
  late final NotificationType? type;
  late final bool? isRead;

  FutureOr<NotificationListResponse> build({
    int page = 1,
    int limit = 20,
    NotificationType? type,
    bool? isRead,
  });
}

/// See also [NotificationsController].
@ProviderFor(NotificationsController)
const notificationsControllerProvider = NotificationsControllerFamily();

/// See also [NotificationsController].
class NotificationsControllerFamily
    extends Family<AsyncValue<NotificationListResponse>> {
  /// See also [NotificationsController].
  const NotificationsControllerFamily();

  /// See also [NotificationsController].
  NotificationsControllerProvider call({
    int page = 1,
    int limit = 20,
    NotificationType? type,
    bool? isRead,
  }) {
    return NotificationsControllerProvider(
      page: page,
      limit: limit,
      type: type,
      isRead: isRead,
    );
  }

  @override
  NotificationsControllerProvider getProviderOverride(
    covariant NotificationsControllerProvider provider,
  ) {
    return call(
      page: provider.page,
      limit: provider.limit,
      type: provider.type,
      isRead: provider.isRead,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'notificationsControllerProvider';
}

/// See also [NotificationsController].
class NotificationsControllerProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          NotificationsController,
          NotificationListResponse
        > {
  /// See also [NotificationsController].
  NotificationsControllerProvider({
    int page = 1,
    int limit = 20,
    NotificationType? type,
    bool? isRead,
  }) : this._internal(
         () => NotificationsController()
           ..page = page
           ..limit = limit
           ..type = type
           ..isRead = isRead,
         from: notificationsControllerProvider,
         name: r'notificationsControllerProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$notificationsControllerHash,
         dependencies: NotificationsControllerFamily._dependencies,
         allTransitiveDependencies:
             NotificationsControllerFamily._allTransitiveDependencies,
         page: page,
         limit: limit,
         type: type,
         isRead: isRead,
       );

  NotificationsControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.page,
    required this.limit,
    required this.type,
    required this.isRead,
  }) : super.internal();

  final int page;
  final int limit;
  final NotificationType? type;
  final bool? isRead;

  @override
  FutureOr<NotificationListResponse> runNotifierBuild(
    covariant NotificationsController notifier,
  ) {
    return notifier.build(page: page, limit: limit, type: type, isRead: isRead);
  }

  @override
  Override overrideWith(NotificationsController Function() create) {
    return ProviderOverride(
      origin: this,
      override: NotificationsControllerProvider._internal(
        () => create()
          ..page = page
          ..limit = limit
          ..type = type
          ..isRead = isRead,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        page: page,
        limit: limit,
        type: type,
        isRead: isRead,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<
    NotificationsController,
    NotificationListResponse
  >
  createElement() {
    return _NotificationsControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is NotificationsControllerProvider &&
        other.page == page &&
        other.limit == limit &&
        other.type == type &&
        other.isRead == isRead;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);
    hash = _SystemHash.combine(hash, type.hashCode);
    hash = _SystemHash.combine(hash, isRead.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin NotificationsControllerRef
    on AutoDisposeAsyncNotifierProviderRef<NotificationListResponse> {
  /// The parameter `page` of this provider.
  int get page;

  /// The parameter `limit` of this provider.
  int get limit;

  /// The parameter `type` of this provider.
  NotificationType? get type;

  /// The parameter `isRead` of this provider.
  bool? get isRead;
}

class _NotificationsControllerProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          NotificationsController,
          NotificationListResponse
        >
    with NotificationsControllerRef {
  _NotificationsControllerProviderElement(super.provider);

  @override
  int get page => (origin as NotificationsControllerProvider).page;
  @override
  int get limit => (origin as NotificationsControllerProvider).limit;
  @override
  NotificationType? get type =>
      (origin as NotificationsControllerProvider).type;
  @override
  bool? get isRead => (origin as NotificationsControllerProvider).isRead;
}

String _$notificationCountHash() => r'e56b44e6cbc0281fb817233d3aafeaa5ee86ede9';

/// See also [NotificationCount].
@ProviderFor(NotificationCount)
final notificationCountProvider =
    NotifierProvider<NotificationCount, int>.internal(
      NotificationCount.new,
      name: r'notificationCountProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$notificationCountHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$NotificationCount = Notifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
