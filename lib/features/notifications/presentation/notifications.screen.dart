import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/notifications/data/models/notification.model.dart';
import 'package:hrms_tst/features/notifications/domain/notifications.controller.dart';
import 'package:hrms_tst/features/notifications/presentation/widgets/notification_tile.dart';
import 'package:hrms_tst/shared/widgets/loading_placeholder.dart';

class NotificationsScreen extends HookConsumerWidget {
  const NotificationsScreen({super.key});

  static const String name = 'Notifications';
  static const String path = '/notifications';

  static GoRoute route({List<RouteBase> routes = const []}) => GoRoute(
    path: path,
    name: name,
    routes: routes,
    pageBuilder: (context, state) =>
        const NoTransitionPage(child: NotificationsScreen()),
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final scrollController = useScrollController();
    final selectedFilter = useState<NotificationType?>(null);

    // Listen for scroll to implement pagination
    useEffect(() {
      void onScroll() {
        if (scrollController.position.pixels >=
            scrollController.position.maxScrollExtent - 200) {
          // Load more when near bottom
          // ref.read(notificationsControllerProvider().notifier).loadMore();
        }
      }

      scrollController.addListener(onScroll);
      return () => scrollController.removeListener(onScroll);
    }, [scrollController]);

    final notificationsAsync = ref.watch(
      notificationsControllerProvider(type: selectedFilter.value),
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
        scrolledUnderElevation: 0,
      ),
      body: SafeArea(
        child: RefreshIndicator.adaptive(
          onRefresh: () async {
            ref.invalidate(notificationsControllerProvider());
            await ref.read(notificationsControllerProvider().future);
          },
          child: switch (notificationsAsync) {
            AsyncData(:final value) =>
              value.notifications.isEmpty
                  ? _EmptyNotificationsView()
                  : ListView.separated(
                      controller: scrollController,
                      padding: const EdgeInsets.all(16),
                      itemCount: value
                          .notifications
                          .length, // +1 for loading indicator
                      separatorBuilder: (context, index) => Space.y(12),
                      itemBuilder: (context, index) {
                        if (index >= value.notifications.length) {
                          // Loading indicator for pagination
                          return const Center(
                            child: Padding(
                              padding: EdgeInsets.all(16.0),
                              child: CircularProgressIndicator.adaptive(),
                            ),
                          );
                        }

                        final notification = value.notifications[index];
                        return NotificationTile(notification: notification);
                      },
                    ),
            AsyncError(:final error) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: context.colors.error,
                  ),
                  Space.y(16),
                  Text(
                    'Failed to load notifications',
                    style: context.textStyles.titleMedium,
                  ),
                  Space.y(8),
                  Text(
                    error.toString(),
                    style: context.textStyles.bodySmall,
                    textAlign: TextAlign.center,
                  ),
                  Space.y(16),
                  ElevatedButton(
                    onPressed: () async {
                      ref.invalidate(notificationsControllerProvider());
                      await ref.read(notificationsControllerProvider().future);
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
            _ => _LoadingView(),
          },
        ),
      ),
    );
  }
}

class _EmptyNotificationsView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 64,
            color: context.colors.outline,
          ),
          Space.y(16),
          Text('No notifications yet', style: context.textStyles.titleMedium),
          Space.y(8),
          Text(
            'You\'ll see notifications here when you receive them',
            style: context.textStyles.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _LoadingView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: 6,
      separatorBuilder: (context, index) => Space.y(12),
      itemBuilder: (context, index) {
        return LoadingPlaceholder(
          decoration: BoxDecoration(
            color: context.colors.outline,
            borderRadius: BorderRadius.circular(12),
          ),
          height: 80,
        );
      },
    );
  }
}
