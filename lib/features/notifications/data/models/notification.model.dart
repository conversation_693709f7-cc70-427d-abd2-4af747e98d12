import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification.model.freezed.dart';
part 'notification.model.g.dart';

@freezed
abstract class NotificationModel with _$NotificationModel {
  const factory NotificationModel({
    required int id,
    required String title,
    required String message,
    required NotificationType type,
    required bool cleared,
    required DateTime createdAt,
    required DateTime? deletedAt,
    required DateTime? removedAt,
    Map<String, dynamic>? clickAction,
  }) = _NotificationModel;

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationModelFromJson(json);
}

@freezed
abstract class NotificationListResponse with _$NotificationListResponse {
  const factory NotificationListResponse({
    @JsonKey(name: 'notificationRecords')
    required List<NotificationModel> notifications,
    required int totalRecords,
  }) = _NotificationListResponse;

  factory NotificationListResponse.fromJson(Map<String, dynamic> json) =>
      _$NotificationListResponseFromJson(json);
}

enum NotificationType {
  @JsonValue('clock-in-reminder')
  clockInReminder,
  @JsonValue('wfh')
  wfh,
  @JsonValue('leave')
  leave,
}

extension NotificationTypeExtension on NotificationType {
  String get displayName {
    return switch (this) {
      NotificationType.clockInReminder => 'Clock In Reminder',
      NotificationType.wfh => 'Work From Home',
      NotificationType.leave => 'Leave',
    };
  }

  IconData get icon {
    return switch (this) {
      NotificationType.clockInReminder => Icons.access_time,
      NotificationType.wfh => Icons.work,
      NotificationType.leave => Icons.event_busy,
    };
  }

  Color color(BuildContext context) {
    return switch (this) {
      NotificationType.clockInReminder => Colors.red,
      NotificationType.wfh => Colors.blue,
      NotificationType.leave => Colors.orange,
    };
  }
}
