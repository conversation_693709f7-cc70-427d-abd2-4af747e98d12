import 'package:hrms_tst/core/services/network/network.service.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:hrms_tst/features/notifications/data/models/notification.model.dart';
import 'package:hrms_tst/features/notifications/data/notifications.protocol.dart';

class NotificationsRepository implements NotificationsProtocol {
  final networkService = getIt.get<NetworkService>();

  @override
  Future<Result<NotificationListResponse>> getNotifications({
    int page = 1,
    int limit = 20,
    NotificationType? type,
    bool? isRead,
  }) async {
    return await networkService.get(
      '/notifications',
      queryParameters: {
        'page': page,
        'limit': limit,
        // if (isRead != null) 'isRead': isRead,
      },
      mapper: (response) {
        return NotificationListResponse.fromJson(response.data['data']);
      },
    );
  }
}
