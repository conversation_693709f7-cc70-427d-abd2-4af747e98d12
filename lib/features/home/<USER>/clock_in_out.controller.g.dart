// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'clock_in_out.controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$clockInOutHash() => r'0a159826d4662efcbc5b076515e23f6836e92de3';

/// See also [ClockInOut].
@ProviderFor(ClockInOut)
final clockInOutProvider =
    AutoDisposeAsyncNotifierProvider<ClockInOut, LastClockInModel>.internal(
      ClockInOut.new,
      name: r'clockInOutProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$clockInOutHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ClockInOut = AutoDisposeAsyncNotifier<LastClockInModel>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
