import 'package:hrms_tst/core/services/network/network.service.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:hrms_tst/features/home/<USER>/models/clock_in.model.dart';
import 'package:hrms_tst/features/home/<USER>/models/employee_presence.model.dart';
import 'package:hrms_tst/features/home/<USER>/models/holiday.model.dart';
import 'package:hrms_tst/features/home/<USER>/models/leave_balance.model.dart';
import 'package:hrms_tst/features/home/<USER>/models/wishes.model.dart';
import 'package:hrms_tst/features/home/<USER>/models/work_update.model.dart';

import 'home.protocol.dart';

class HomeRepository implements HomeProtocol {
  final networkService = getIt.get<NetworkService>();

  @override
  Future<Result<LastClockInModel>> getLastClockIn() {
    return networkService.get(
      '/attendances/last-clockIn',
      mapper: (response) {
        return LastClockInModel.fromJson(response.data['data']['lastClockIn']);
      },
    );
  }

  @override
  Future<Result<LeaveBalanceModel>> getLeaveBalance({
    required DateTime currentTime,
  }) async {
    return await networkService.get(
      '/leave-wfh-requests/my-balance',
      queryParameters: {'date': currentTime.toIso8601String()},
      mapper: (response) {
        return LeaveBalanceModel.fromJson(response.data['data']['balance']);
      },
    );
  }

  @override
  Future<Result<EmployeePresenceModel>> getEmployeePresence() async {
    return await networkService.get(
      '/leave-wfh-requests/home-summary',
      mapper: (response) {
        return EmployeePresenceModel.fromJson(response.data['data']);
      },
    );
  }

  @override
  Future<Result<WishesModel>> getWishes() async {
    return await networkService.get(
      '/employees/wishes',
      mapper: (response) {
        return WishesModel.fromJson(response.data['data']);
      },
    );
  }

  @override
  Future<Result<HolidayModel>> getHolidays({required DateTime? date}) async {
    if (date == null) {
      return await networkService.get(
        '/holidays/current-month',
        mapper: (response) {
          return HolidayModel.fromJson(response.data['data']);
        },
      );
    } else {
      return await networkService.get(
        '/holidays',
        queryParameters: {'date': date.toUtc().toIso8601String()},
        mapper: (response) {
          return HolidayModel.fromJson(response.data['data']);
        },
      );
    }
  }

  @override
  Future<Result<List<TeammateDataModel>>> getAllPresences({
    required String type,
    required bool isUpcoming,
  }) async {
    if (type == 'absent') {
      return await networkService.get(
        '/attendances/employees-by-attendance-status',
        queryParameters: {'status': type, 'limit': 100},
        mapper: (response) {
          return (response.data['data']['employees']['attendanceRecords']
                  as List<dynamic>)
              .map((e) => TeammateDataModel.fromJson(e))
              .toList();
        },
      );
    } else {
      return await networkService.get(
        '/leave-wfh-requests/teammates-list',
        queryParameters: {
          'type': type,
          if (isUpcoming) 'upComings': 1,
          'limit': 100,
        },
        mapper: (response) {
          return (response.data['data']['leaveWfhRequests']['rows']
                  as List<dynamic>)
              .map((e) => TeammateDataModel.fromJson(e))
              .toList();
        },
      );
    }
  }

  @override
  Future<Result<String?>> clockIn({
    required double? lat,
    required double? long,
  }) async {
    final clockInResult = await networkService.post(
      '/attendances/clock-in',
      mapper: (response) {
        return (response.data['isLate'] as bool? ?? false)
            ? (response.data['message'] as String?)
            : null;
      },
    );
    if (clockInResult.isSuccess) {
      await networkService.patch(
        '/attendances/location',
        data: {'lat': lat, 'long': long},
        mapper: (response) {
          return;
        },
      );
    }
    return clockInResult;
  }

  @override
  Future<Result<void>> clockOut({
    required List<WorkUpdateModel> workUpdates,
  }) async {
    final clockOutResult = await networkService.patch(
      '/attendances/clock-out',
      data: {
        'workUpdate': workUpdates.map((e) => e.update).toList().join('<br>'),
      },
      mapper: (response) {
        return;
      },
    );
    await networkService.post(
      '/work-updates',
      data: {'updates': workUpdates.map((e) => e.toJson()).toList()},
      mapper: (response) {
        return;
      },
    );
    return clockOutResult;
  }

  @override
  Future<Result<List<ProjectModel>>> getProjects() async {
    return await networkService.get(
      '/projects/search',
      mapper: (response) {
        return (response.data['data']['rows'] as List<dynamic>)
            .map((e) => ProjectModel.fromJson(e))
            .toList();
      },
    );
  }
}
