import 'package:freezed_annotation/freezed_annotation.dart';

part 'leave_balance.model.freezed.dart';
part 'leave_balance.model.g.dart';

@freezed
abstract class LeaveBalanceModel with _$LeaveBalanceModel {
  @JsonSerializable(explicitToJson: true)
  const factory LeaveBalanceModel({
    required final LeavePolicy leavePolicy,
    required final List<AvailableLeaveType> availableLeaveTypes,
  }) = _LeaveBalanceModel;

  factory LeaveBalanceModel.fromJson(Map<String, dynamic> json) =>
      _$LeaveBalanceModelFromJson(json);
}

@freezed
abstract class LeavePolicy with _$LeavePolicy {
  const factory LeavePolicy({
    required final int id,
    required final String name,
    @JsonKey(name: 'leaveTypeAllots')
    required final List<LeaveTypeAllotments> leaveTypeAllotments,
  }) = _LeavePolicy;

  factory LeavePolicy.fromJson(Map<String, dynamic> json) =>
      _$LeavePolicyFromJson(json);
}

@freezed
abstract class LeaveTypeAllotments with _$LeaveTypeAllotments {
  const factory LeaveTypeAllotments({
    required final double monthlyQuota,
    required final double annualQuota,
    required final LeaveType leaveType,
  }) = _LeaveTypeAllotments;

  factory LeaveTypeAllotments.fromJson(Map<String, dynamic> json) =>
      _$LeaveTypeAllotmentsFromJson(json);
}

@freezed
abstract class LeaveType with _$LeaveType {
  const factory LeaveType({required final int id, required final String name}) =
      _LeaveType;

  factory LeaveType.fromJson(Map<String, dynamic> json) =>
      _$LeaveTypeFromJson(json);
}

@freezed
abstract class AvailableLeaveType with _$AvailableLeaveType {
  const factory AvailableLeaveType({
    required final int id,
    required final String name,
    required final double balance,
    required final double annualQuota,
    required final double monthlyQuota,
    required final double consumedLeaves,
  }) = _AvailableLeaveType;

  factory AvailableLeaveType.fromJson(Map<String, dynamic> json) =>
      _$AvailableLeaveTypeFromJson(json);
}
