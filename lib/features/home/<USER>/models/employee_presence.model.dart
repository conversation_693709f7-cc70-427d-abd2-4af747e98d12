import 'package:freezed_annotation/freezed_annotation.dart';

part 'employee_presence.model.freezed.dart';
part 'employee_presence.model.g.dart';

List<EmployeeTileData> employeeTileDataFromJson(Map<String, dynamic> json) {
  return (json['rows'] as List<dynamic>)
      .map((row) => EmployeeTileData.fromJson(row['employee']['user']))
      .toList();
}

EmployeeTileData employeeTileDataFromTeammateData(Map<String, dynamic> json) {
  return EmployeeTileData.fromJson(json['user']);
}

@freezed
abstract class EmployeePresenceModel with _$EmployeePresenceModel {
  @JsonSerializable(explicitToJson: true)
  const factory EmployeePresenceModel({
    @JsonKey(name: 'absents', fromJson: employeeTileDataFromJson)
    required final List<EmployeeTileData> absentEmployees,
    @JsonKey(name: 'leave', fromJson: employeeTileDataFromJson)
    required final List<EmployeeTileData> onLeaveEmployees,
    @<PERSON>son<PERSON>ey(name: 'wfh', fromJson: employeeTileDataFromJson)
    required final List<EmployeeTileData> wfhEmployees,
  }) = _EmployeePresenceModel;

  factory EmployeePresenceModel.fromJson(Map<String, dynamic> json) =>
      _$EmployeePresenceModelFromJson(json);
}

@freezed
abstract class TeammateDataModel with _$TeammateDataModel {
  const factory TeammateDataModel({
    required final int id,
    final DateTime? startDate,
    final DateTime? endDate,
    final int? totalDays,
    final String? startDaySlot,
    final String? endDaySlot,
    final DateTime? createdAt,
    @JsonKey(name: 'EmployeeId') final int? employeeId,
    @JsonKey(name: 'employee', fromJson: employeeTileDataFromTeammateData)
    final EmployeeTileData? employee,
    final String? $daySlot,
  }) = _TeammateDataModel;

  factory TeammateDataModel.fromJson(Map<String, dynamic> json) =>
      _$TeammateDataModelFromJson(json);
}

@freezed
abstract class EmployeeTileData with _$EmployeeTileData {
  const factory EmployeeTileData({
    required final int id,
    required final String displayName,
    required final String? image,
  }) = _EmployeeTileData;

  factory EmployeeTileData.fromJson(Map<String, dynamic> json) =>
      _$EmployeeTileDataFromJson(json);
}
