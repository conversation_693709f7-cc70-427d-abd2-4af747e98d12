// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'employee_presence.model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$EmployeePresenceModel {

@JsonKey(name: 'absents', fromJson: employeeTileDataFromJson) List<EmployeeTileData> get absentEmployees;@JsonKey(name: 'leave', fromJson: employeeTileDataFromJson) List<EmployeeTileData> get onLeaveEmployees;@JsonKey(name: 'wfh', fromJson: employeeTileDataFromJson) List<EmployeeTileData> get wfhEmployees;
/// Create a copy of EmployeePresenceModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EmployeePresenceModelCopyWith<EmployeePresenceModel> get copyWith => _$EmployeePresenceModelCopyWithImpl<EmployeePresenceModel>(this as EmployeePresenceModel, _$identity);

  /// Serializes this EmployeePresenceModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EmployeePresenceModel&&const DeepCollectionEquality().equals(other.absentEmployees, absentEmployees)&&const DeepCollectionEquality().equals(other.onLeaveEmployees, onLeaveEmployees)&&const DeepCollectionEquality().equals(other.wfhEmployees, wfhEmployees));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(absentEmployees),const DeepCollectionEquality().hash(onLeaveEmployees),const DeepCollectionEquality().hash(wfhEmployees));

@override
String toString() {
  return 'EmployeePresenceModel(absentEmployees: $absentEmployees, onLeaveEmployees: $onLeaveEmployees, wfhEmployees: $wfhEmployees)';
}


}

/// @nodoc
abstract mixin class $EmployeePresenceModelCopyWith<$Res>  {
  factory $EmployeePresenceModelCopyWith(EmployeePresenceModel value, $Res Function(EmployeePresenceModel) _then) = _$EmployeePresenceModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'absents', fromJson: employeeTileDataFromJson) List<EmployeeTileData> absentEmployees,@JsonKey(name: 'leave', fromJson: employeeTileDataFromJson) List<EmployeeTileData> onLeaveEmployees,@JsonKey(name: 'wfh', fromJson: employeeTileDataFromJson) List<EmployeeTileData> wfhEmployees
});




}
/// @nodoc
class _$EmployeePresenceModelCopyWithImpl<$Res>
    implements $EmployeePresenceModelCopyWith<$Res> {
  _$EmployeePresenceModelCopyWithImpl(this._self, this._then);

  final EmployeePresenceModel _self;
  final $Res Function(EmployeePresenceModel) _then;

/// Create a copy of EmployeePresenceModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? absentEmployees = null,Object? onLeaveEmployees = null,Object? wfhEmployees = null,}) {
  return _then(_self.copyWith(
absentEmployees: null == absentEmployees ? _self.absentEmployees : absentEmployees // ignore: cast_nullable_to_non_nullable
as List<EmployeeTileData>,onLeaveEmployees: null == onLeaveEmployees ? _self.onLeaveEmployees : onLeaveEmployees // ignore: cast_nullable_to_non_nullable
as List<EmployeeTileData>,wfhEmployees: null == wfhEmployees ? _self.wfhEmployees : wfhEmployees // ignore: cast_nullable_to_non_nullable
as List<EmployeeTileData>,
  ));
}

}


/// @nodoc

@JsonSerializable(explicitToJson: true)
class _EmployeePresenceModel implements EmployeePresenceModel {
  const _EmployeePresenceModel({@JsonKey(name: 'absents', fromJson: employeeTileDataFromJson) required final  List<EmployeeTileData> absentEmployees, @JsonKey(name: 'leave', fromJson: employeeTileDataFromJson) required final  List<EmployeeTileData> onLeaveEmployees, @JsonKey(name: 'wfh', fromJson: employeeTileDataFromJson) required final  List<EmployeeTileData> wfhEmployees}): _absentEmployees = absentEmployees,_onLeaveEmployees = onLeaveEmployees,_wfhEmployees = wfhEmployees;
  factory _EmployeePresenceModel.fromJson(Map<String, dynamic> json) => _$EmployeePresenceModelFromJson(json);

 final  List<EmployeeTileData> _absentEmployees;
@override@JsonKey(name: 'absents', fromJson: employeeTileDataFromJson) List<EmployeeTileData> get absentEmployees {
  if (_absentEmployees is EqualUnmodifiableListView) return _absentEmployees;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_absentEmployees);
}

 final  List<EmployeeTileData> _onLeaveEmployees;
@override@JsonKey(name: 'leave', fromJson: employeeTileDataFromJson) List<EmployeeTileData> get onLeaveEmployees {
  if (_onLeaveEmployees is EqualUnmodifiableListView) return _onLeaveEmployees;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_onLeaveEmployees);
}

 final  List<EmployeeTileData> _wfhEmployees;
@override@JsonKey(name: 'wfh', fromJson: employeeTileDataFromJson) List<EmployeeTileData> get wfhEmployees {
  if (_wfhEmployees is EqualUnmodifiableListView) return _wfhEmployees;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_wfhEmployees);
}


/// Create a copy of EmployeePresenceModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EmployeePresenceModelCopyWith<_EmployeePresenceModel> get copyWith => __$EmployeePresenceModelCopyWithImpl<_EmployeePresenceModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EmployeePresenceModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EmployeePresenceModel&&const DeepCollectionEquality().equals(other._absentEmployees, _absentEmployees)&&const DeepCollectionEquality().equals(other._onLeaveEmployees, _onLeaveEmployees)&&const DeepCollectionEquality().equals(other._wfhEmployees, _wfhEmployees));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_absentEmployees),const DeepCollectionEquality().hash(_onLeaveEmployees),const DeepCollectionEquality().hash(_wfhEmployees));

@override
String toString() {
  return 'EmployeePresenceModel(absentEmployees: $absentEmployees, onLeaveEmployees: $onLeaveEmployees, wfhEmployees: $wfhEmployees)';
}


}

/// @nodoc
abstract mixin class _$EmployeePresenceModelCopyWith<$Res> implements $EmployeePresenceModelCopyWith<$Res> {
  factory _$EmployeePresenceModelCopyWith(_EmployeePresenceModel value, $Res Function(_EmployeePresenceModel) _then) = __$EmployeePresenceModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'absents', fromJson: employeeTileDataFromJson) List<EmployeeTileData> absentEmployees,@JsonKey(name: 'leave', fromJson: employeeTileDataFromJson) List<EmployeeTileData> onLeaveEmployees,@JsonKey(name: 'wfh', fromJson: employeeTileDataFromJson) List<EmployeeTileData> wfhEmployees
});




}
/// @nodoc
class __$EmployeePresenceModelCopyWithImpl<$Res>
    implements _$EmployeePresenceModelCopyWith<$Res> {
  __$EmployeePresenceModelCopyWithImpl(this._self, this._then);

  final _EmployeePresenceModel _self;
  final $Res Function(_EmployeePresenceModel) _then;

/// Create a copy of EmployeePresenceModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? absentEmployees = null,Object? onLeaveEmployees = null,Object? wfhEmployees = null,}) {
  return _then(_EmployeePresenceModel(
absentEmployees: null == absentEmployees ? _self._absentEmployees : absentEmployees // ignore: cast_nullable_to_non_nullable
as List<EmployeeTileData>,onLeaveEmployees: null == onLeaveEmployees ? _self._onLeaveEmployees : onLeaveEmployees // ignore: cast_nullable_to_non_nullable
as List<EmployeeTileData>,wfhEmployees: null == wfhEmployees ? _self._wfhEmployees : wfhEmployees // ignore: cast_nullable_to_non_nullable
as List<EmployeeTileData>,
  ));
}


}


/// @nodoc
mixin _$TeammateDataModel {

 int get id; DateTime? get startDate; DateTime? get endDate; int? get totalDays; String? get startDaySlot; String? get endDaySlot; DateTime? get createdAt;@JsonKey(name: 'EmployeeId') int? get employeeId;@JsonKey(name: 'employee', fromJson: employeeTileDataFromTeammateData) EmployeeTileData? get employee; String? get $daySlot;
/// Create a copy of TeammateDataModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TeammateDataModelCopyWith<TeammateDataModel> get copyWith => _$TeammateDataModelCopyWithImpl<TeammateDataModel>(this as TeammateDataModel, _$identity);

  /// Serializes this TeammateDataModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TeammateDataModel&&(identical(other.id, id) || other.id == id)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.totalDays, totalDays) || other.totalDays == totalDays)&&(identical(other.startDaySlot, startDaySlot) || other.startDaySlot == startDaySlot)&&(identical(other.endDaySlot, endDaySlot) || other.endDaySlot == endDaySlot)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.employeeId, employeeId) || other.employeeId == employeeId)&&(identical(other.employee, employee) || other.employee == employee)&&(identical(other.$daySlot, $daySlot) || other.$daySlot == $daySlot));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,startDate,endDate,totalDays,startDaySlot,endDaySlot,createdAt,employeeId,employee,$daySlot);

@override
String toString() {
  return 'TeammateDataModel(id: $id, startDate: $startDate, endDate: $endDate, totalDays: $totalDays, startDaySlot: $startDaySlot, endDaySlot: $endDaySlot, createdAt: $createdAt, employeeId: $employeeId, employee: $employee, \$daySlot: ${$daySlot})';
}


}

/// @nodoc
abstract mixin class $TeammateDataModelCopyWith<$Res>  {
  factory $TeammateDataModelCopyWith(TeammateDataModel value, $Res Function(TeammateDataModel) _then) = _$TeammateDataModelCopyWithImpl;
@useResult
$Res call({
 int id, DateTime? startDate, DateTime? endDate, int? totalDays, String? startDaySlot, String? endDaySlot, DateTime? createdAt,@JsonKey(name: 'EmployeeId') int? employeeId,@JsonKey(name: 'employee', fromJson: employeeTileDataFromTeammateData) EmployeeTileData? employee, String? $daySlot
});


$EmployeeTileDataCopyWith<$Res>? get employee;

}
/// @nodoc
class _$TeammateDataModelCopyWithImpl<$Res>
    implements $TeammateDataModelCopyWith<$Res> {
  _$TeammateDataModelCopyWithImpl(this._self, this._then);

  final TeammateDataModel _self;
  final $Res Function(TeammateDataModel) _then;

/// Create a copy of TeammateDataModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? startDate = freezed,Object? endDate = freezed,Object? totalDays = freezed,Object? startDaySlot = freezed,Object? endDaySlot = freezed,Object? createdAt = freezed,Object? employeeId = freezed,Object? employee = freezed,Object? $daySlot = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,totalDays: freezed == totalDays ? _self.totalDays : totalDays // ignore: cast_nullable_to_non_nullable
as int?,startDaySlot: freezed == startDaySlot ? _self.startDaySlot : startDaySlot // ignore: cast_nullable_to_non_nullable
as String?,endDaySlot: freezed == endDaySlot ? _self.endDaySlot : endDaySlot // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,employeeId: freezed == employeeId ? _self.employeeId : employeeId // ignore: cast_nullable_to_non_nullable
as int?,employee: freezed == employee ? _self.employee : employee // ignore: cast_nullable_to_non_nullable
as EmployeeTileData?,$daySlot: freezed == $daySlot ? _self.$daySlot : $daySlot // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of TeammateDataModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EmployeeTileDataCopyWith<$Res>? get employee {
    if (_self.employee == null) {
    return null;
  }

  return $EmployeeTileDataCopyWith<$Res>(_self.employee!, (value) {
    return _then(_self.copyWith(employee: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _TeammateDataModel implements TeammateDataModel {
  const _TeammateDataModel({required this.id, this.startDate, this.endDate, this.totalDays, this.startDaySlot, this.endDaySlot, this.createdAt, @JsonKey(name: 'EmployeeId') this.employeeId, @JsonKey(name: 'employee', fromJson: employeeTileDataFromTeammateData) this.employee, this.$daySlot});
  factory _TeammateDataModel.fromJson(Map<String, dynamic> json) => _$TeammateDataModelFromJson(json);

@override final  int id;
@override final  DateTime? startDate;
@override final  DateTime? endDate;
@override final  int? totalDays;
@override final  String? startDaySlot;
@override final  String? endDaySlot;
@override final  DateTime? createdAt;
@override@JsonKey(name: 'EmployeeId') final  int? employeeId;
@override@JsonKey(name: 'employee', fromJson: employeeTileDataFromTeammateData) final  EmployeeTileData? employee;
@override final  String? $daySlot;

/// Create a copy of TeammateDataModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TeammateDataModelCopyWith<_TeammateDataModel> get copyWith => __$TeammateDataModelCopyWithImpl<_TeammateDataModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TeammateDataModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TeammateDataModel&&(identical(other.id, id) || other.id == id)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.totalDays, totalDays) || other.totalDays == totalDays)&&(identical(other.startDaySlot, startDaySlot) || other.startDaySlot == startDaySlot)&&(identical(other.endDaySlot, endDaySlot) || other.endDaySlot == endDaySlot)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.employeeId, employeeId) || other.employeeId == employeeId)&&(identical(other.employee, employee) || other.employee == employee)&&(identical(other.$daySlot, $daySlot) || other.$daySlot == $daySlot));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,startDate,endDate,totalDays,startDaySlot,endDaySlot,createdAt,employeeId,employee,$daySlot);

@override
String toString() {
  return 'TeammateDataModel(id: $id, startDate: $startDate, endDate: $endDate, totalDays: $totalDays, startDaySlot: $startDaySlot, endDaySlot: $endDaySlot, createdAt: $createdAt, employeeId: $employeeId, employee: $employee, \$daySlot: ${$daySlot})';
}


}

/// @nodoc
abstract mixin class _$TeammateDataModelCopyWith<$Res> implements $TeammateDataModelCopyWith<$Res> {
  factory _$TeammateDataModelCopyWith(_TeammateDataModel value, $Res Function(_TeammateDataModel) _then) = __$TeammateDataModelCopyWithImpl;
@override @useResult
$Res call({
 int id, DateTime? startDate, DateTime? endDate, int? totalDays, String? startDaySlot, String? endDaySlot, DateTime? createdAt,@JsonKey(name: 'EmployeeId') int? employeeId,@JsonKey(name: 'employee', fromJson: employeeTileDataFromTeammateData) EmployeeTileData? employee, String? $daySlot
});


@override $EmployeeTileDataCopyWith<$Res>? get employee;

}
/// @nodoc
class __$TeammateDataModelCopyWithImpl<$Res>
    implements _$TeammateDataModelCopyWith<$Res> {
  __$TeammateDataModelCopyWithImpl(this._self, this._then);

  final _TeammateDataModel _self;
  final $Res Function(_TeammateDataModel) _then;

/// Create a copy of TeammateDataModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? startDate = freezed,Object? endDate = freezed,Object? totalDays = freezed,Object? startDaySlot = freezed,Object? endDaySlot = freezed,Object? createdAt = freezed,Object? employeeId = freezed,Object? employee = freezed,Object? $daySlot = freezed,}) {
  return _then(_TeammateDataModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,totalDays: freezed == totalDays ? _self.totalDays : totalDays // ignore: cast_nullable_to_non_nullable
as int?,startDaySlot: freezed == startDaySlot ? _self.startDaySlot : startDaySlot // ignore: cast_nullable_to_non_nullable
as String?,endDaySlot: freezed == endDaySlot ? _self.endDaySlot : endDaySlot // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,employeeId: freezed == employeeId ? _self.employeeId : employeeId // ignore: cast_nullable_to_non_nullable
as int?,employee: freezed == employee ? _self.employee : employee // ignore: cast_nullable_to_non_nullable
as EmployeeTileData?,$daySlot: freezed == $daySlot ? _self.$daySlot : $daySlot // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of TeammateDataModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EmployeeTileDataCopyWith<$Res>? get employee {
    if (_self.employee == null) {
    return null;
  }

  return $EmployeeTileDataCopyWith<$Res>(_self.employee!, (value) {
    return _then(_self.copyWith(employee: value));
  });
}
}


/// @nodoc
mixin _$EmployeeTileData {

 int get id; String get displayName; String? get image;
/// Create a copy of EmployeeTileData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EmployeeTileDataCopyWith<EmployeeTileData> get copyWith => _$EmployeeTileDataCopyWithImpl<EmployeeTileData>(this as EmployeeTileData, _$identity);

  /// Serializes this EmployeeTileData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EmployeeTileData&&(identical(other.id, id) || other.id == id)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.image, image) || other.image == image));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,displayName,image);

@override
String toString() {
  return 'EmployeeTileData(id: $id, displayName: $displayName, image: $image)';
}


}

/// @nodoc
abstract mixin class $EmployeeTileDataCopyWith<$Res>  {
  factory $EmployeeTileDataCopyWith(EmployeeTileData value, $Res Function(EmployeeTileData) _then) = _$EmployeeTileDataCopyWithImpl;
@useResult
$Res call({
 int id, String displayName, String? image
});




}
/// @nodoc
class _$EmployeeTileDataCopyWithImpl<$Res>
    implements $EmployeeTileDataCopyWith<$Res> {
  _$EmployeeTileDataCopyWithImpl(this._self, this._then);

  final EmployeeTileData _self;
  final $Res Function(EmployeeTileData) _then;

/// Create a copy of EmployeeTileData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? displayName = null,Object? image = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,displayName: null == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String,image: freezed == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _EmployeeTileData implements EmployeeTileData {
  const _EmployeeTileData({required this.id, required this.displayName, required this.image});
  factory _EmployeeTileData.fromJson(Map<String, dynamic> json) => _$EmployeeTileDataFromJson(json);

@override final  int id;
@override final  String displayName;
@override final  String? image;

/// Create a copy of EmployeeTileData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EmployeeTileDataCopyWith<_EmployeeTileData> get copyWith => __$EmployeeTileDataCopyWithImpl<_EmployeeTileData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EmployeeTileDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EmployeeTileData&&(identical(other.id, id) || other.id == id)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.image, image) || other.image == image));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,displayName,image);

@override
String toString() {
  return 'EmployeeTileData(id: $id, displayName: $displayName, image: $image)';
}


}

/// @nodoc
abstract mixin class _$EmployeeTileDataCopyWith<$Res> implements $EmployeeTileDataCopyWith<$Res> {
  factory _$EmployeeTileDataCopyWith(_EmployeeTileData value, $Res Function(_EmployeeTileData) _then) = __$EmployeeTileDataCopyWithImpl;
@override @useResult
$Res call({
 int id, String displayName, String? image
});




}
/// @nodoc
class __$EmployeeTileDataCopyWithImpl<$Res>
    implements _$EmployeeTileDataCopyWith<$Res> {
  __$EmployeeTileDataCopyWithImpl(this._self, this._then);

  final _EmployeeTileData _self;
  final $Res Function(_EmployeeTileData) _then;

/// Create a copy of EmployeeTileData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? displayName = null,Object? image = freezed,}) {
  return _then(_EmployeeTileData(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,displayName: null == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String,image: freezed == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
