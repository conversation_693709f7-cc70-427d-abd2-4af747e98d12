// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'wishes.model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WishesModel {

@JsonKey(name: 'forBirthday', fromJson: bithdayEmployeeDataFromJson) List<WishEmployeeTileData> get birthdayEmployees;@JsonKey(name: 'forAnniversary', fromJson: anniversaryEmployeeDataFromJson) List<WishEmployeeTileData> get anniversaryEmployees;@JsonKey(name: 'forNewJoiners', fromJson: anniversaryEmployeeDataFromJson) List<WishEmployeeTileData> get newJoineeEmployees;
/// Create a copy of WishesModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WishesModelCopyWith<WishesModel> get copyWith => _$WishesModelCopyWithImpl<WishesModel>(this as WishesModel, _$identity);

  /// Serializes this WishesModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WishesModel&&const DeepCollectionEquality().equals(other.birthdayEmployees, birthdayEmployees)&&const DeepCollectionEquality().equals(other.anniversaryEmployees, anniversaryEmployees)&&const DeepCollectionEquality().equals(other.newJoineeEmployees, newJoineeEmployees));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(birthdayEmployees),const DeepCollectionEquality().hash(anniversaryEmployees),const DeepCollectionEquality().hash(newJoineeEmployees));

@override
String toString() {
  return 'WishesModel(birthdayEmployees: $birthdayEmployees, anniversaryEmployees: $anniversaryEmployees, newJoineeEmployees: $newJoineeEmployees)';
}


}

/// @nodoc
abstract mixin class $WishesModelCopyWith<$Res>  {
  factory $WishesModelCopyWith(WishesModel value, $Res Function(WishesModel) _then) = _$WishesModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'forBirthday', fromJson: bithdayEmployeeDataFromJson) List<WishEmployeeTileData> birthdayEmployees,@JsonKey(name: 'forAnniversary', fromJson: anniversaryEmployeeDataFromJson) List<WishEmployeeTileData> anniversaryEmployees,@JsonKey(name: 'forNewJoiners', fromJson: anniversaryEmployeeDataFromJson) List<WishEmployeeTileData> newJoineeEmployees
});




}
/// @nodoc
class _$WishesModelCopyWithImpl<$Res>
    implements $WishesModelCopyWith<$Res> {
  _$WishesModelCopyWithImpl(this._self, this._then);

  final WishesModel _self;
  final $Res Function(WishesModel) _then;

/// Create a copy of WishesModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? birthdayEmployees = null,Object? anniversaryEmployees = null,Object? newJoineeEmployees = null,}) {
  return _then(_self.copyWith(
birthdayEmployees: null == birthdayEmployees ? _self.birthdayEmployees : birthdayEmployees // ignore: cast_nullable_to_non_nullable
as List<WishEmployeeTileData>,anniversaryEmployees: null == anniversaryEmployees ? _self.anniversaryEmployees : anniversaryEmployees // ignore: cast_nullable_to_non_nullable
as List<WishEmployeeTileData>,newJoineeEmployees: null == newJoineeEmployees ? _self.newJoineeEmployees : newJoineeEmployees // ignore: cast_nullable_to_non_nullable
as List<WishEmployeeTileData>,
  ));
}

}


/// @nodoc

@JsonSerializable(explicitToJson: true)
class _WishesModel implements WishesModel {
  const _WishesModel({@JsonKey(name: 'forBirthday', fromJson: bithdayEmployeeDataFromJson) required final  List<WishEmployeeTileData> birthdayEmployees, @JsonKey(name: 'forAnniversary', fromJson: anniversaryEmployeeDataFromJson) required final  List<WishEmployeeTileData> anniversaryEmployees, @JsonKey(name: 'forNewJoiners', fromJson: anniversaryEmployeeDataFromJson) required final  List<WishEmployeeTileData> newJoineeEmployees}): _birthdayEmployees = birthdayEmployees,_anniversaryEmployees = anniversaryEmployees,_newJoineeEmployees = newJoineeEmployees;
  factory _WishesModel.fromJson(Map<String, dynamic> json) => _$WishesModelFromJson(json);

 final  List<WishEmployeeTileData> _birthdayEmployees;
@override@JsonKey(name: 'forBirthday', fromJson: bithdayEmployeeDataFromJson) List<WishEmployeeTileData> get birthdayEmployees {
  if (_birthdayEmployees is EqualUnmodifiableListView) return _birthdayEmployees;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_birthdayEmployees);
}

 final  List<WishEmployeeTileData> _anniversaryEmployees;
@override@JsonKey(name: 'forAnniversary', fromJson: anniversaryEmployeeDataFromJson) List<WishEmployeeTileData> get anniversaryEmployees {
  if (_anniversaryEmployees is EqualUnmodifiableListView) return _anniversaryEmployees;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_anniversaryEmployees);
}

 final  List<WishEmployeeTileData> _newJoineeEmployees;
@override@JsonKey(name: 'forNewJoiners', fromJson: anniversaryEmployeeDataFromJson) List<WishEmployeeTileData> get newJoineeEmployees {
  if (_newJoineeEmployees is EqualUnmodifiableListView) return _newJoineeEmployees;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_newJoineeEmployees);
}


/// Create a copy of WishesModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WishesModelCopyWith<_WishesModel> get copyWith => __$WishesModelCopyWithImpl<_WishesModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WishesModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WishesModel&&const DeepCollectionEquality().equals(other._birthdayEmployees, _birthdayEmployees)&&const DeepCollectionEquality().equals(other._anniversaryEmployees, _anniversaryEmployees)&&const DeepCollectionEquality().equals(other._newJoineeEmployees, _newJoineeEmployees));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_birthdayEmployees),const DeepCollectionEquality().hash(_anniversaryEmployees),const DeepCollectionEquality().hash(_newJoineeEmployees));

@override
String toString() {
  return 'WishesModel(birthdayEmployees: $birthdayEmployees, anniversaryEmployees: $anniversaryEmployees, newJoineeEmployees: $newJoineeEmployees)';
}


}

/// @nodoc
abstract mixin class _$WishesModelCopyWith<$Res> implements $WishesModelCopyWith<$Res> {
  factory _$WishesModelCopyWith(_WishesModel value, $Res Function(_WishesModel) _then) = __$WishesModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'forBirthday', fromJson: bithdayEmployeeDataFromJson) List<WishEmployeeTileData> birthdayEmployees,@JsonKey(name: 'forAnniversary', fromJson: anniversaryEmployeeDataFromJson) List<WishEmployeeTileData> anniversaryEmployees,@JsonKey(name: 'forNewJoiners', fromJson: anniversaryEmployeeDataFromJson) List<WishEmployeeTileData> newJoineeEmployees
});




}
/// @nodoc
class __$WishesModelCopyWithImpl<$Res>
    implements _$WishesModelCopyWith<$Res> {
  __$WishesModelCopyWithImpl(this._self, this._then);

  final _WishesModel _self;
  final $Res Function(_WishesModel) _then;

/// Create a copy of WishesModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? birthdayEmployees = null,Object? anniversaryEmployees = null,Object? newJoineeEmployees = null,}) {
  return _then(_WishesModel(
birthdayEmployees: null == birthdayEmployees ? _self._birthdayEmployees : birthdayEmployees // ignore: cast_nullable_to_non_nullable
as List<WishEmployeeTileData>,anniversaryEmployees: null == anniversaryEmployees ? _self._anniversaryEmployees : anniversaryEmployees // ignore: cast_nullable_to_non_nullable
as List<WishEmployeeTileData>,newJoineeEmployees: null == newJoineeEmployees ? _self._newJoineeEmployees : newJoineeEmployees // ignore: cast_nullable_to_non_nullable
as List<WishEmployeeTileData>,
  ));
}


}


/// @nodoc
mixin _$WishEmployeeTileData {

 int get id; String get displayName; String? get image; DateTime? get date;
/// Create a copy of WishEmployeeTileData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WishEmployeeTileDataCopyWith<WishEmployeeTileData> get copyWith => _$WishEmployeeTileDataCopyWithImpl<WishEmployeeTileData>(this as WishEmployeeTileData, _$identity);

  /// Serializes this WishEmployeeTileData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WishEmployeeTileData&&(identical(other.id, id) || other.id == id)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.image, image) || other.image == image)&&(identical(other.date, date) || other.date == date));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,displayName,image,date);

@override
String toString() {
  return 'WishEmployeeTileData(id: $id, displayName: $displayName, image: $image, date: $date)';
}


}

/// @nodoc
abstract mixin class $WishEmployeeTileDataCopyWith<$Res>  {
  factory $WishEmployeeTileDataCopyWith(WishEmployeeTileData value, $Res Function(WishEmployeeTileData) _then) = _$WishEmployeeTileDataCopyWithImpl;
@useResult
$Res call({
 int id, String displayName, String? image, DateTime? date
});




}
/// @nodoc
class _$WishEmployeeTileDataCopyWithImpl<$Res>
    implements $WishEmployeeTileDataCopyWith<$Res> {
  _$WishEmployeeTileDataCopyWithImpl(this._self, this._then);

  final WishEmployeeTileData _self;
  final $Res Function(WishEmployeeTileData) _then;

/// Create a copy of WishEmployeeTileData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? displayName = null,Object? image = freezed,Object? date = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,displayName: null == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String,image: freezed == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String?,date: freezed == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _WishEmployeeTileData implements WishEmployeeTileData {
  const _WishEmployeeTileData({required this.id, required this.displayName, required this.image, required this.date});
  factory _WishEmployeeTileData.fromJson(Map<String, dynamic> json) => _$WishEmployeeTileDataFromJson(json);

@override final  int id;
@override final  String displayName;
@override final  String? image;
@override final  DateTime? date;

/// Create a copy of WishEmployeeTileData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WishEmployeeTileDataCopyWith<_WishEmployeeTileData> get copyWith => __$WishEmployeeTileDataCopyWithImpl<_WishEmployeeTileData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WishEmployeeTileDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WishEmployeeTileData&&(identical(other.id, id) || other.id == id)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.image, image) || other.image == image)&&(identical(other.date, date) || other.date == date));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,displayName,image,date);

@override
String toString() {
  return 'WishEmployeeTileData(id: $id, displayName: $displayName, image: $image, date: $date)';
}


}

/// @nodoc
abstract mixin class _$WishEmployeeTileDataCopyWith<$Res> implements $WishEmployeeTileDataCopyWith<$Res> {
  factory _$WishEmployeeTileDataCopyWith(_WishEmployeeTileData value, $Res Function(_WishEmployeeTileData) _then) = __$WishEmployeeTileDataCopyWithImpl;
@override @useResult
$Res call({
 int id, String displayName, String? image, DateTime? date
});




}
/// @nodoc
class __$WishEmployeeTileDataCopyWithImpl<$Res>
    implements _$WishEmployeeTileDataCopyWith<$Res> {
  __$WishEmployeeTileDataCopyWithImpl(this._self, this._then);

  final _WishEmployeeTileData _self;
  final $Res Function(_WishEmployeeTileData) _then;

/// Create a copy of WishEmployeeTileData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? displayName = null,Object? image = freezed,Object? date = freezed,}) {
  return _then(_WishEmployeeTileData(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,displayName: null == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String,image: freezed == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String?,date: freezed == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
