// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'holiday.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_HolidayModel _$HolidayModelFromJson(Map<String, dynamic> json) =>
    _HolidayModel(
      holidays: (json['holidays'] as List<dynamic>)
          .map((e) => HolidayTileData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$HolidayModelToJson(_HolidayModel instance) =>
    <String, dynamic>{'holidays': instance.holidays};

_HolidayTileData _$HolidayTileDataFromJson(Map<String, dynamic> json) =>
    _HolidayTileData(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
    );

Map<String, dynamic> _$HolidayTileDataToJson(_HolidayTileData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
    };
