import 'package:freezed_annotation/freezed_annotation.dart';

part 'holiday.model.freezed.dart';
part 'holiday.model.g.dart';

@freezed
abstract class HolidayModel with _$HolidayModel {
  const factory HolidayModel({
    @JsonKey(name: 'holidays') required final List<HolidayTileData> holidays,
  }) = _HolidayModel;

  factory HolidayModel.fromJson(Map<String, dynamic> json) =>
      _$HolidayModelFromJson(json);
}

@freezed
abstract class HolidayTileData with _$HolidayTileData {
  const factory HolidayTileData({
    required final int id,
    required final String name,
    required final DateTime startDate,
    required final DateTime endDate,
  }) = _HolidayTileData;

  factory HolidayTileData.fromJson(Map<String, dynamic> json) =>
      _$HolidayTileDataFromJson(json);
}
