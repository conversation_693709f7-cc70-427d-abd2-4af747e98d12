import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hrms_tst/features/home/<USER>/models/clock_in.model.dart';

part 'work_update.model.freezed.dart';
part 'work_update.model.g.dart';

@freezed
abstract class WorkUpdateModel with _$WorkUpdateModel {
  const factory WorkUpdateModel({
    @JsonKey(name: 'ProjectId') final int? projectId,
    @JsonKey(includeFromJson: false, includeToJson: false)
    final ProjectModel? project,
    @JsonKey(includeFromJson: false, includeToJson: false)
    final TimeOfDay? startTime,
    @JsonKey(includeFromJson: false, includeToJson: false)
    final TimeOfDay? endTime,
    required final String update,
    required final double? hours,
  }) = _WorkUpdateModel;

  factory WorkUpdateModel.fromJson(Map<String, dynamic> json) =>
      _$WorkUpdateModelFromJson(json);
}
