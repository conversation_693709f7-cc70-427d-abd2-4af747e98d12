// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wishes.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WishesModel _$WishesModelFromJson(Map<String, dynamic> json) => _WishesModel(
  birthdayEmployees: bithdayEmployeeDataFromJson(json['forBirthday'] as List),
  anniversaryEmployees: anniversaryEmployeeDataFromJson(
    json['forAnniversary'] as List,
  ),
  newJoineeEmployees: anniversaryEmployeeDataFromJson(
    json['forNewJoiners'] as List,
  ),
);

Map<String, dynamic> _$WishesModelToJson(
  _WishesModel instance,
) => <String, dynamic>{
  'forBirthday': instance.birthdayEmployees.map((e) => e.toJson()).toList(),
  'forAnniversary': instance.anniversaryEmployees
      .map((e) => e.toJson())
      .toList(),
  'forNewJoiners': instance.newJoineeEmployees.map((e) => e.toJson()).toList(),
};

_WishEmployeeTileData _$WishEmployeeTileDataFromJson(
  Map<String, dynamic> json,
) => _WishEmployeeTileData(
  id: (json['id'] as num).toInt(),
  displayName: json['displayName'] as String,
  image: json['image'] as String?,
  date: json['date'] == null ? null : DateTime.parse(json['date'] as String),
);

Map<String, dynamic> _$WishEmployeeTileDataToJson(
  _WishEmployeeTileData instance,
) => <String, dynamic>{
  'id': instance.id,
  'displayName': instance.displayName,
  'image': instance.image,
  'date': instance.date?.toIso8601String(),
};
