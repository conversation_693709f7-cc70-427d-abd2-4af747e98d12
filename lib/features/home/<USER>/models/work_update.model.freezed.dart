// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'work_update.model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WorkUpdateModel {

@JsonKey(name: 'ProjectId') int? get projectId;@JsonKey(includeFromJson: false, includeToJson: false) ProjectModel? get project;@JsonKey(includeFromJson: false, includeToJson: false) TimeOfDay? get startTime;@JsonKey(includeFromJson: false, includeToJson: false) TimeOfDay? get endTime; String get update; double? get hours;
/// Create a copy of WorkUpdateModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WorkUpdateModelCopyWith<WorkUpdateModel> get copyWith => _$WorkUpdateModelCopyWithImpl<WorkUpdateModel>(this as WorkUpdateModel, _$identity);

  /// Serializes this WorkUpdateModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WorkUpdateModel&&(identical(other.projectId, projectId) || other.projectId == projectId)&&(identical(other.project, project) || other.project == project)&&(identical(other.startTime, startTime) || other.startTime == startTime)&&(identical(other.endTime, endTime) || other.endTime == endTime)&&(identical(other.update, update) || other.update == update)&&(identical(other.hours, hours) || other.hours == hours));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,projectId,project,startTime,endTime,update,hours);

@override
String toString() {
  return 'WorkUpdateModel(projectId: $projectId, project: $project, startTime: $startTime, endTime: $endTime, update: $update, hours: $hours)';
}


}

/// @nodoc
abstract mixin class $WorkUpdateModelCopyWith<$Res>  {
  factory $WorkUpdateModelCopyWith(WorkUpdateModel value, $Res Function(WorkUpdateModel) _then) = _$WorkUpdateModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'ProjectId') int? projectId,@JsonKey(includeFromJson: false, includeToJson: false) ProjectModel? project,@JsonKey(includeFromJson: false, includeToJson: false) TimeOfDay? startTime,@JsonKey(includeFromJson: false, includeToJson: false) TimeOfDay? endTime, String update, double? hours
});


$ProjectModelCopyWith<$Res>? get project;

}
/// @nodoc
class _$WorkUpdateModelCopyWithImpl<$Res>
    implements $WorkUpdateModelCopyWith<$Res> {
  _$WorkUpdateModelCopyWithImpl(this._self, this._then);

  final WorkUpdateModel _self;
  final $Res Function(WorkUpdateModel) _then;

/// Create a copy of WorkUpdateModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? projectId = freezed,Object? project = freezed,Object? startTime = freezed,Object? endTime = freezed,Object? update = null,Object? hours = freezed,}) {
  return _then(_self.copyWith(
projectId: freezed == projectId ? _self.projectId : projectId // ignore: cast_nullable_to_non_nullable
as int?,project: freezed == project ? _self.project : project // ignore: cast_nullable_to_non_nullable
as ProjectModel?,startTime: freezed == startTime ? _self.startTime : startTime // ignore: cast_nullable_to_non_nullable
as TimeOfDay?,endTime: freezed == endTime ? _self.endTime : endTime // ignore: cast_nullable_to_non_nullable
as TimeOfDay?,update: null == update ? _self.update : update // ignore: cast_nullable_to_non_nullable
as String,hours: freezed == hours ? _self.hours : hours // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}
/// Create a copy of WorkUpdateModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProjectModelCopyWith<$Res>? get project {
    if (_self.project == null) {
    return null;
  }

  return $ProjectModelCopyWith<$Res>(_self.project!, (value) {
    return _then(_self.copyWith(project: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _WorkUpdateModel implements WorkUpdateModel {
  const _WorkUpdateModel({@JsonKey(name: 'ProjectId') this.projectId, @JsonKey(includeFromJson: false, includeToJson: false) this.project, @JsonKey(includeFromJson: false, includeToJson: false) this.startTime, @JsonKey(includeFromJson: false, includeToJson: false) this.endTime, required this.update, required this.hours});
  factory _WorkUpdateModel.fromJson(Map<String, dynamic> json) => _$WorkUpdateModelFromJson(json);

@override@JsonKey(name: 'ProjectId') final  int? projectId;
@override@JsonKey(includeFromJson: false, includeToJson: false) final  ProjectModel? project;
@override@JsonKey(includeFromJson: false, includeToJson: false) final  TimeOfDay? startTime;
@override@JsonKey(includeFromJson: false, includeToJson: false) final  TimeOfDay? endTime;
@override final  String update;
@override final  double? hours;

/// Create a copy of WorkUpdateModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WorkUpdateModelCopyWith<_WorkUpdateModel> get copyWith => __$WorkUpdateModelCopyWithImpl<_WorkUpdateModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WorkUpdateModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WorkUpdateModel&&(identical(other.projectId, projectId) || other.projectId == projectId)&&(identical(other.project, project) || other.project == project)&&(identical(other.startTime, startTime) || other.startTime == startTime)&&(identical(other.endTime, endTime) || other.endTime == endTime)&&(identical(other.update, update) || other.update == update)&&(identical(other.hours, hours) || other.hours == hours));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,projectId,project,startTime,endTime,update,hours);

@override
String toString() {
  return 'WorkUpdateModel(projectId: $projectId, project: $project, startTime: $startTime, endTime: $endTime, update: $update, hours: $hours)';
}


}

/// @nodoc
abstract mixin class _$WorkUpdateModelCopyWith<$Res> implements $WorkUpdateModelCopyWith<$Res> {
  factory _$WorkUpdateModelCopyWith(_WorkUpdateModel value, $Res Function(_WorkUpdateModel) _then) = __$WorkUpdateModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'ProjectId') int? projectId,@JsonKey(includeFromJson: false, includeToJson: false) ProjectModel? project,@JsonKey(includeFromJson: false, includeToJson: false) TimeOfDay? startTime,@JsonKey(includeFromJson: false, includeToJson: false) TimeOfDay? endTime, String update, double? hours
});


@override $ProjectModelCopyWith<$Res>? get project;

}
/// @nodoc
class __$WorkUpdateModelCopyWithImpl<$Res>
    implements _$WorkUpdateModelCopyWith<$Res> {
  __$WorkUpdateModelCopyWithImpl(this._self, this._then);

  final _WorkUpdateModel _self;
  final $Res Function(_WorkUpdateModel) _then;

/// Create a copy of WorkUpdateModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? projectId = freezed,Object? project = freezed,Object? startTime = freezed,Object? endTime = freezed,Object? update = null,Object? hours = freezed,}) {
  return _then(_WorkUpdateModel(
projectId: freezed == projectId ? _self.projectId : projectId // ignore: cast_nullable_to_non_nullable
as int?,project: freezed == project ? _self.project : project // ignore: cast_nullable_to_non_nullable
as ProjectModel?,startTime: freezed == startTime ? _self.startTime : startTime // ignore: cast_nullable_to_non_nullable
as TimeOfDay?,endTime: freezed == endTime ? _self.endTime : endTime // ignore: cast_nullable_to_non_nullable
as TimeOfDay?,update: null == update ? _self.update : update // ignore: cast_nullable_to_non_nullable
as String,hours: freezed == hours ? _self.hours : hours // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}

/// Create a copy of WorkUpdateModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProjectModelCopyWith<$Res>? get project {
    if (_self.project == null) {
    return null;
  }

  return $ProjectModelCopyWith<$Res>(_self.project!, (value) {
    return _then(_self.copyWith(project: value));
  });
}
}

// dart format on
