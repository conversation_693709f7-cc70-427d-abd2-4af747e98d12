// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'clock_in.model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LastClockInModel {

 int get id; DateTime? get clockIn; DateTime? get clockOut; double? get hoursWorked; DateTime? get createdAt;
/// Create a copy of LastClockInModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LastClockInModelCopyWith<LastClockInModel> get copyWith => _$LastClockInModelCopyWithImpl<LastClockInModel>(this as LastClockInModel, _$identity);

  /// Serializes this LastClockInModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LastClockInModel&&(identical(other.id, id) || other.id == id)&&(identical(other.clockIn, clockIn) || other.clockIn == clockIn)&&(identical(other.clockOut, clockOut) || other.clockOut == clockOut)&&(identical(other.hoursWorked, hoursWorked) || other.hoursWorked == hoursWorked)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,clockIn,clockOut,hoursWorked,createdAt);

@override
String toString() {
  return 'LastClockInModel(id: $id, clockIn: $clockIn, clockOut: $clockOut, hoursWorked: $hoursWorked, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class $LastClockInModelCopyWith<$Res>  {
  factory $LastClockInModelCopyWith(LastClockInModel value, $Res Function(LastClockInModel) _then) = _$LastClockInModelCopyWithImpl;
@useResult
$Res call({
 int id, DateTime? clockIn, DateTime? clockOut, double? hoursWorked, DateTime? createdAt
});




}
/// @nodoc
class _$LastClockInModelCopyWithImpl<$Res>
    implements $LastClockInModelCopyWith<$Res> {
  _$LastClockInModelCopyWithImpl(this._self, this._then);

  final LastClockInModel _self;
  final $Res Function(LastClockInModel) _then;

/// Create a copy of LastClockInModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? clockIn = freezed,Object? clockOut = freezed,Object? hoursWorked = freezed,Object? createdAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,clockIn: freezed == clockIn ? _self.clockIn : clockIn // ignore: cast_nullable_to_non_nullable
as DateTime?,clockOut: freezed == clockOut ? _self.clockOut : clockOut // ignore: cast_nullable_to_non_nullable
as DateTime?,hoursWorked: freezed == hoursWorked ? _self.hoursWorked : hoursWorked // ignore: cast_nullable_to_non_nullable
as double?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _LastClockInModel implements LastClockInModel {
  const _LastClockInModel({required this.id, this.clockIn, this.clockOut, this.hoursWorked, this.createdAt});
  factory _LastClockInModel.fromJson(Map<String, dynamic> json) => _$LastClockInModelFromJson(json);

@override final  int id;
@override final  DateTime? clockIn;
@override final  DateTime? clockOut;
@override final  double? hoursWorked;
@override final  DateTime? createdAt;

/// Create a copy of LastClockInModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LastClockInModelCopyWith<_LastClockInModel> get copyWith => __$LastClockInModelCopyWithImpl<_LastClockInModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LastClockInModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LastClockInModel&&(identical(other.id, id) || other.id == id)&&(identical(other.clockIn, clockIn) || other.clockIn == clockIn)&&(identical(other.clockOut, clockOut) || other.clockOut == clockOut)&&(identical(other.hoursWorked, hoursWorked) || other.hoursWorked == hoursWorked)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,clockIn,clockOut,hoursWorked,createdAt);

@override
String toString() {
  return 'LastClockInModel(id: $id, clockIn: $clockIn, clockOut: $clockOut, hoursWorked: $hoursWorked, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class _$LastClockInModelCopyWith<$Res> implements $LastClockInModelCopyWith<$Res> {
  factory _$LastClockInModelCopyWith(_LastClockInModel value, $Res Function(_LastClockInModel) _then) = __$LastClockInModelCopyWithImpl;
@override @useResult
$Res call({
 int id, DateTime? clockIn, DateTime? clockOut, double? hoursWorked, DateTime? createdAt
});




}
/// @nodoc
class __$LastClockInModelCopyWithImpl<$Res>
    implements _$LastClockInModelCopyWith<$Res> {
  __$LastClockInModelCopyWithImpl(this._self, this._then);

  final _LastClockInModel _self;
  final $Res Function(_LastClockInModel) _then;

/// Create a copy of LastClockInModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? clockIn = freezed,Object? clockOut = freezed,Object? hoursWorked = freezed,Object? createdAt = freezed,}) {
  return _then(_LastClockInModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,clockIn: freezed == clockIn ? _self.clockIn : clockIn // ignore: cast_nullable_to_non_nullable
as DateTime?,clockOut: freezed == clockOut ? _self.clockOut : clockOut // ignore: cast_nullable_to_non_nullable
as DateTime?,hoursWorked: freezed == hoursWorked ? _self.hoursWorked : hoursWorked // ignore: cast_nullable_to_non_nullable
as double?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$ProjectModel {

 int get id; String get name;
/// Create a copy of ProjectModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProjectModelCopyWith<ProjectModel> get copyWith => _$ProjectModelCopyWithImpl<ProjectModel>(this as ProjectModel, _$identity);

  /// Serializes this ProjectModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProjectModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name);

@override
String toString() {
  return 'ProjectModel(id: $id, name: $name)';
}


}

/// @nodoc
abstract mixin class $ProjectModelCopyWith<$Res>  {
  factory $ProjectModelCopyWith(ProjectModel value, $Res Function(ProjectModel) _then) = _$ProjectModelCopyWithImpl;
@useResult
$Res call({
 int id, String name
});




}
/// @nodoc
class _$ProjectModelCopyWithImpl<$Res>
    implements $ProjectModelCopyWith<$Res> {
  _$ProjectModelCopyWithImpl(this._self, this._then);

  final ProjectModel _self;
  final $Res Function(ProjectModel) _then;

/// Create a copy of ProjectModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ProjectModel implements ProjectModel {
  const _ProjectModel({required this.id, required this.name});
  factory _ProjectModel.fromJson(Map<String, dynamic> json) => _$ProjectModelFromJson(json);

@override final  int id;
@override final  String name;

/// Create a copy of ProjectModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProjectModelCopyWith<_ProjectModel> get copyWith => __$ProjectModelCopyWithImpl<_ProjectModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProjectModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProjectModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name);

@override
String toString() {
  return 'ProjectModel(id: $id, name: $name)';
}


}

/// @nodoc
abstract mixin class _$ProjectModelCopyWith<$Res> implements $ProjectModelCopyWith<$Res> {
  factory _$ProjectModelCopyWith(_ProjectModel value, $Res Function(_ProjectModel) _then) = __$ProjectModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String name
});




}
/// @nodoc
class __$ProjectModelCopyWithImpl<$Res>
    implements _$ProjectModelCopyWith<$Res> {
  __$ProjectModelCopyWithImpl(this._self, this._then);

  final _ProjectModel _self;
  final $Res Function(_ProjectModel) _then;

/// Create a copy of ProjectModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,}) {
  return _then(_ProjectModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
