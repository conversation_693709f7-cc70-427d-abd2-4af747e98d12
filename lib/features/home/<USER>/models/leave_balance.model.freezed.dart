// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'leave_balance.model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LeaveBalanceModel {

 LeavePolicy get leavePolicy; List<AvailableLeaveType> get availableLeaveTypes;
/// Create a copy of LeaveBalanceModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LeaveBalanceModelCopyWith<LeaveBalanceModel> get copyWith => _$LeaveBalanceModelCopyWithImpl<LeaveBalanceModel>(this as LeaveBalanceModel, _$identity);

  /// Serializes this LeaveBalanceModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LeaveBalanceModel&&(identical(other.leavePolicy, leavePolicy) || other.leavePolicy == leavePolicy)&&const DeepCollectionEquality().equals(other.availableLeaveTypes, availableLeaveTypes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,leavePolicy,const DeepCollectionEquality().hash(availableLeaveTypes));

@override
String toString() {
  return 'LeaveBalanceModel(leavePolicy: $leavePolicy, availableLeaveTypes: $availableLeaveTypes)';
}


}

/// @nodoc
abstract mixin class $LeaveBalanceModelCopyWith<$Res>  {
  factory $LeaveBalanceModelCopyWith(LeaveBalanceModel value, $Res Function(LeaveBalanceModel) _then) = _$LeaveBalanceModelCopyWithImpl;
@useResult
$Res call({
 LeavePolicy leavePolicy, List<AvailableLeaveType> availableLeaveTypes
});


$LeavePolicyCopyWith<$Res> get leavePolicy;

}
/// @nodoc
class _$LeaveBalanceModelCopyWithImpl<$Res>
    implements $LeaveBalanceModelCopyWith<$Res> {
  _$LeaveBalanceModelCopyWithImpl(this._self, this._then);

  final LeaveBalanceModel _self;
  final $Res Function(LeaveBalanceModel) _then;

/// Create a copy of LeaveBalanceModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? leavePolicy = null,Object? availableLeaveTypes = null,}) {
  return _then(_self.copyWith(
leavePolicy: null == leavePolicy ? _self.leavePolicy : leavePolicy // ignore: cast_nullable_to_non_nullable
as LeavePolicy,availableLeaveTypes: null == availableLeaveTypes ? _self.availableLeaveTypes : availableLeaveTypes // ignore: cast_nullable_to_non_nullable
as List<AvailableLeaveType>,
  ));
}
/// Create a copy of LeaveBalanceModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeavePolicyCopyWith<$Res> get leavePolicy {
  
  return $LeavePolicyCopyWith<$Res>(_self.leavePolicy, (value) {
    return _then(_self.copyWith(leavePolicy: value));
  });
}
}


/// @nodoc

@JsonSerializable(explicitToJson: true)
class _LeaveBalanceModel implements LeaveBalanceModel {
  const _LeaveBalanceModel({required this.leavePolicy, required final  List<AvailableLeaveType> availableLeaveTypes}): _availableLeaveTypes = availableLeaveTypes;
  factory _LeaveBalanceModel.fromJson(Map<String, dynamic> json) => _$LeaveBalanceModelFromJson(json);

@override final  LeavePolicy leavePolicy;
 final  List<AvailableLeaveType> _availableLeaveTypes;
@override List<AvailableLeaveType> get availableLeaveTypes {
  if (_availableLeaveTypes is EqualUnmodifiableListView) return _availableLeaveTypes;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_availableLeaveTypes);
}


/// Create a copy of LeaveBalanceModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LeaveBalanceModelCopyWith<_LeaveBalanceModel> get copyWith => __$LeaveBalanceModelCopyWithImpl<_LeaveBalanceModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LeaveBalanceModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LeaveBalanceModel&&(identical(other.leavePolicy, leavePolicy) || other.leavePolicy == leavePolicy)&&const DeepCollectionEquality().equals(other._availableLeaveTypes, _availableLeaveTypes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,leavePolicy,const DeepCollectionEquality().hash(_availableLeaveTypes));

@override
String toString() {
  return 'LeaveBalanceModel(leavePolicy: $leavePolicy, availableLeaveTypes: $availableLeaveTypes)';
}


}

/// @nodoc
abstract mixin class _$LeaveBalanceModelCopyWith<$Res> implements $LeaveBalanceModelCopyWith<$Res> {
  factory _$LeaveBalanceModelCopyWith(_LeaveBalanceModel value, $Res Function(_LeaveBalanceModel) _then) = __$LeaveBalanceModelCopyWithImpl;
@override @useResult
$Res call({
 LeavePolicy leavePolicy, List<AvailableLeaveType> availableLeaveTypes
});


@override $LeavePolicyCopyWith<$Res> get leavePolicy;

}
/// @nodoc
class __$LeaveBalanceModelCopyWithImpl<$Res>
    implements _$LeaveBalanceModelCopyWith<$Res> {
  __$LeaveBalanceModelCopyWithImpl(this._self, this._then);

  final _LeaveBalanceModel _self;
  final $Res Function(_LeaveBalanceModel) _then;

/// Create a copy of LeaveBalanceModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? leavePolicy = null,Object? availableLeaveTypes = null,}) {
  return _then(_LeaveBalanceModel(
leavePolicy: null == leavePolicy ? _self.leavePolicy : leavePolicy // ignore: cast_nullable_to_non_nullable
as LeavePolicy,availableLeaveTypes: null == availableLeaveTypes ? _self._availableLeaveTypes : availableLeaveTypes // ignore: cast_nullable_to_non_nullable
as List<AvailableLeaveType>,
  ));
}

/// Create a copy of LeaveBalanceModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeavePolicyCopyWith<$Res> get leavePolicy {
  
  return $LeavePolicyCopyWith<$Res>(_self.leavePolicy, (value) {
    return _then(_self.copyWith(leavePolicy: value));
  });
}
}


/// @nodoc
mixin _$LeavePolicy {

 int get id; String get name;@JsonKey(name: 'leaveTypeAllots') List<LeaveTypeAllotments> get leaveTypeAllotments;
/// Create a copy of LeavePolicy
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LeavePolicyCopyWith<LeavePolicy> get copyWith => _$LeavePolicyCopyWithImpl<LeavePolicy>(this as LeavePolicy, _$identity);

  /// Serializes this LeavePolicy to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LeavePolicy&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&const DeepCollectionEquality().equals(other.leaveTypeAllotments, leaveTypeAllotments));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,const DeepCollectionEquality().hash(leaveTypeAllotments));

@override
String toString() {
  return 'LeavePolicy(id: $id, name: $name, leaveTypeAllotments: $leaveTypeAllotments)';
}


}

/// @nodoc
abstract mixin class $LeavePolicyCopyWith<$Res>  {
  factory $LeavePolicyCopyWith(LeavePolicy value, $Res Function(LeavePolicy) _then) = _$LeavePolicyCopyWithImpl;
@useResult
$Res call({
 int id, String name,@JsonKey(name: 'leaveTypeAllots') List<LeaveTypeAllotments> leaveTypeAllotments
});




}
/// @nodoc
class _$LeavePolicyCopyWithImpl<$Res>
    implements $LeavePolicyCopyWith<$Res> {
  _$LeavePolicyCopyWithImpl(this._self, this._then);

  final LeavePolicy _self;
  final $Res Function(LeavePolicy) _then;

/// Create a copy of LeavePolicy
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? leaveTypeAllotments = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,leaveTypeAllotments: null == leaveTypeAllotments ? _self.leaveTypeAllotments : leaveTypeAllotments // ignore: cast_nullable_to_non_nullable
as List<LeaveTypeAllotments>,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _LeavePolicy implements LeavePolicy {
  const _LeavePolicy({required this.id, required this.name, @JsonKey(name: 'leaveTypeAllots') required final  List<LeaveTypeAllotments> leaveTypeAllotments}): _leaveTypeAllotments = leaveTypeAllotments;
  factory _LeavePolicy.fromJson(Map<String, dynamic> json) => _$LeavePolicyFromJson(json);

@override final  int id;
@override final  String name;
 final  List<LeaveTypeAllotments> _leaveTypeAllotments;
@override@JsonKey(name: 'leaveTypeAllots') List<LeaveTypeAllotments> get leaveTypeAllotments {
  if (_leaveTypeAllotments is EqualUnmodifiableListView) return _leaveTypeAllotments;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_leaveTypeAllotments);
}


/// Create a copy of LeavePolicy
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LeavePolicyCopyWith<_LeavePolicy> get copyWith => __$LeavePolicyCopyWithImpl<_LeavePolicy>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LeavePolicyToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LeavePolicy&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&const DeepCollectionEquality().equals(other._leaveTypeAllotments, _leaveTypeAllotments));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,const DeepCollectionEquality().hash(_leaveTypeAllotments));

@override
String toString() {
  return 'LeavePolicy(id: $id, name: $name, leaveTypeAllotments: $leaveTypeAllotments)';
}


}

/// @nodoc
abstract mixin class _$LeavePolicyCopyWith<$Res> implements $LeavePolicyCopyWith<$Res> {
  factory _$LeavePolicyCopyWith(_LeavePolicy value, $Res Function(_LeavePolicy) _then) = __$LeavePolicyCopyWithImpl;
@override @useResult
$Res call({
 int id, String name,@JsonKey(name: 'leaveTypeAllots') List<LeaveTypeAllotments> leaveTypeAllotments
});




}
/// @nodoc
class __$LeavePolicyCopyWithImpl<$Res>
    implements _$LeavePolicyCopyWith<$Res> {
  __$LeavePolicyCopyWithImpl(this._self, this._then);

  final _LeavePolicy _self;
  final $Res Function(_LeavePolicy) _then;

/// Create a copy of LeavePolicy
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? leaveTypeAllotments = null,}) {
  return _then(_LeavePolicy(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,leaveTypeAllotments: null == leaveTypeAllotments ? _self._leaveTypeAllotments : leaveTypeAllotments // ignore: cast_nullable_to_non_nullable
as List<LeaveTypeAllotments>,
  ));
}


}


/// @nodoc
mixin _$LeaveTypeAllotments {

 double get monthlyQuota; double get annualQuota; LeaveType get leaveType;
/// Create a copy of LeaveTypeAllotments
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LeaveTypeAllotmentsCopyWith<LeaveTypeAllotments> get copyWith => _$LeaveTypeAllotmentsCopyWithImpl<LeaveTypeAllotments>(this as LeaveTypeAllotments, _$identity);

  /// Serializes this LeaveTypeAllotments to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LeaveTypeAllotments&&(identical(other.monthlyQuota, monthlyQuota) || other.monthlyQuota == monthlyQuota)&&(identical(other.annualQuota, annualQuota) || other.annualQuota == annualQuota)&&(identical(other.leaveType, leaveType) || other.leaveType == leaveType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,monthlyQuota,annualQuota,leaveType);

@override
String toString() {
  return 'LeaveTypeAllotments(monthlyQuota: $monthlyQuota, annualQuota: $annualQuota, leaveType: $leaveType)';
}


}

/// @nodoc
abstract mixin class $LeaveTypeAllotmentsCopyWith<$Res>  {
  factory $LeaveTypeAllotmentsCopyWith(LeaveTypeAllotments value, $Res Function(LeaveTypeAllotments) _then) = _$LeaveTypeAllotmentsCopyWithImpl;
@useResult
$Res call({
 double monthlyQuota, double annualQuota, LeaveType leaveType
});


$LeaveTypeCopyWith<$Res> get leaveType;

}
/// @nodoc
class _$LeaveTypeAllotmentsCopyWithImpl<$Res>
    implements $LeaveTypeAllotmentsCopyWith<$Res> {
  _$LeaveTypeAllotmentsCopyWithImpl(this._self, this._then);

  final LeaveTypeAllotments _self;
  final $Res Function(LeaveTypeAllotments) _then;

/// Create a copy of LeaveTypeAllotments
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? monthlyQuota = null,Object? annualQuota = null,Object? leaveType = null,}) {
  return _then(_self.copyWith(
monthlyQuota: null == monthlyQuota ? _self.monthlyQuota : monthlyQuota // ignore: cast_nullable_to_non_nullable
as double,annualQuota: null == annualQuota ? _self.annualQuota : annualQuota // ignore: cast_nullable_to_non_nullable
as double,leaveType: null == leaveType ? _self.leaveType : leaveType // ignore: cast_nullable_to_non_nullable
as LeaveType,
  ));
}
/// Create a copy of LeaveTypeAllotments
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeaveTypeCopyWith<$Res> get leaveType {
  
  return $LeaveTypeCopyWith<$Res>(_self.leaveType, (value) {
    return _then(_self.copyWith(leaveType: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _LeaveTypeAllotments implements LeaveTypeAllotments {
  const _LeaveTypeAllotments({required this.monthlyQuota, required this.annualQuota, required this.leaveType});
  factory _LeaveTypeAllotments.fromJson(Map<String, dynamic> json) => _$LeaveTypeAllotmentsFromJson(json);

@override final  double monthlyQuota;
@override final  double annualQuota;
@override final  LeaveType leaveType;

/// Create a copy of LeaveTypeAllotments
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LeaveTypeAllotmentsCopyWith<_LeaveTypeAllotments> get copyWith => __$LeaveTypeAllotmentsCopyWithImpl<_LeaveTypeAllotments>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LeaveTypeAllotmentsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LeaveTypeAllotments&&(identical(other.monthlyQuota, monthlyQuota) || other.monthlyQuota == monthlyQuota)&&(identical(other.annualQuota, annualQuota) || other.annualQuota == annualQuota)&&(identical(other.leaveType, leaveType) || other.leaveType == leaveType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,monthlyQuota,annualQuota,leaveType);

@override
String toString() {
  return 'LeaveTypeAllotments(monthlyQuota: $monthlyQuota, annualQuota: $annualQuota, leaveType: $leaveType)';
}


}

/// @nodoc
abstract mixin class _$LeaveTypeAllotmentsCopyWith<$Res> implements $LeaveTypeAllotmentsCopyWith<$Res> {
  factory _$LeaveTypeAllotmentsCopyWith(_LeaveTypeAllotments value, $Res Function(_LeaveTypeAllotments) _then) = __$LeaveTypeAllotmentsCopyWithImpl;
@override @useResult
$Res call({
 double monthlyQuota, double annualQuota, LeaveType leaveType
});


@override $LeaveTypeCopyWith<$Res> get leaveType;

}
/// @nodoc
class __$LeaveTypeAllotmentsCopyWithImpl<$Res>
    implements _$LeaveTypeAllotmentsCopyWith<$Res> {
  __$LeaveTypeAllotmentsCopyWithImpl(this._self, this._then);

  final _LeaveTypeAllotments _self;
  final $Res Function(_LeaveTypeAllotments) _then;

/// Create a copy of LeaveTypeAllotments
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? monthlyQuota = null,Object? annualQuota = null,Object? leaveType = null,}) {
  return _then(_LeaveTypeAllotments(
monthlyQuota: null == monthlyQuota ? _self.monthlyQuota : monthlyQuota // ignore: cast_nullable_to_non_nullable
as double,annualQuota: null == annualQuota ? _self.annualQuota : annualQuota // ignore: cast_nullable_to_non_nullable
as double,leaveType: null == leaveType ? _self.leaveType : leaveType // ignore: cast_nullable_to_non_nullable
as LeaveType,
  ));
}

/// Create a copy of LeaveTypeAllotments
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeaveTypeCopyWith<$Res> get leaveType {
  
  return $LeaveTypeCopyWith<$Res>(_self.leaveType, (value) {
    return _then(_self.copyWith(leaveType: value));
  });
}
}


/// @nodoc
mixin _$LeaveType {

 int get id; String get name;
/// Create a copy of LeaveType
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LeaveTypeCopyWith<LeaveType> get copyWith => _$LeaveTypeCopyWithImpl<LeaveType>(this as LeaveType, _$identity);

  /// Serializes this LeaveType to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LeaveType&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name);

@override
String toString() {
  return 'LeaveType(id: $id, name: $name)';
}


}

/// @nodoc
abstract mixin class $LeaveTypeCopyWith<$Res>  {
  factory $LeaveTypeCopyWith(LeaveType value, $Res Function(LeaveType) _then) = _$LeaveTypeCopyWithImpl;
@useResult
$Res call({
 int id, String name
});




}
/// @nodoc
class _$LeaveTypeCopyWithImpl<$Res>
    implements $LeaveTypeCopyWith<$Res> {
  _$LeaveTypeCopyWithImpl(this._self, this._then);

  final LeaveType _self;
  final $Res Function(LeaveType) _then;

/// Create a copy of LeaveType
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _LeaveType implements LeaveType {
  const _LeaveType({required this.id, required this.name});
  factory _LeaveType.fromJson(Map<String, dynamic> json) => _$LeaveTypeFromJson(json);

@override final  int id;
@override final  String name;

/// Create a copy of LeaveType
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LeaveTypeCopyWith<_LeaveType> get copyWith => __$LeaveTypeCopyWithImpl<_LeaveType>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LeaveTypeToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LeaveType&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name);

@override
String toString() {
  return 'LeaveType(id: $id, name: $name)';
}


}

/// @nodoc
abstract mixin class _$LeaveTypeCopyWith<$Res> implements $LeaveTypeCopyWith<$Res> {
  factory _$LeaveTypeCopyWith(_LeaveType value, $Res Function(_LeaveType) _then) = __$LeaveTypeCopyWithImpl;
@override @useResult
$Res call({
 int id, String name
});




}
/// @nodoc
class __$LeaveTypeCopyWithImpl<$Res>
    implements _$LeaveTypeCopyWith<$Res> {
  __$LeaveTypeCopyWithImpl(this._self, this._then);

  final _LeaveType _self;
  final $Res Function(_LeaveType) _then;

/// Create a copy of LeaveType
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,}) {
  return _then(_LeaveType(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$AvailableLeaveType {

 int get id; String get name; double get balance; double get annualQuota; double get monthlyQuota; double get consumedLeaves;
/// Create a copy of AvailableLeaveType
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AvailableLeaveTypeCopyWith<AvailableLeaveType> get copyWith => _$AvailableLeaveTypeCopyWithImpl<AvailableLeaveType>(this as AvailableLeaveType, _$identity);

  /// Serializes this AvailableLeaveType to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AvailableLeaveType&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.balance, balance) || other.balance == balance)&&(identical(other.annualQuota, annualQuota) || other.annualQuota == annualQuota)&&(identical(other.monthlyQuota, monthlyQuota) || other.monthlyQuota == monthlyQuota)&&(identical(other.consumedLeaves, consumedLeaves) || other.consumedLeaves == consumedLeaves));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,balance,annualQuota,monthlyQuota,consumedLeaves);

@override
String toString() {
  return 'AvailableLeaveType(id: $id, name: $name, balance: $balance, annualQuota: $annualQuota, monthlyQuota: $monthlyQuota, consumedLeaves: $consumedLeaves)';
}


}

/// @nodoc
abstract mixin class $AvailableLeaveTypeCopyWith<$Res>  {
  factory $AvailableLeaveTypeCopyWith(AvailableLeaveType value, $Res Function(AvailableLeaveType) _then) = _$AvailableLeaveTypeCopyWithImpl;
@useResult
$Res call({
 int id, String name, double balance, double annualQuota, double monthlyQuota, double consumedLeaves
});




}
/// @nodoc
class _$AvailableLeaveTypeCopyWithImpl<$Res>
    implements $AvailableLeaveTypeCopyWith<$Res> {
  _$AvailableLeaveTypeCopyWithImpl(this._self, this._then);

  final AvailableLeaveType _self;
  final $Res Function(AvailableLeaveType) _then;

/// Create a copy of AvailableLeaveType
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? balance = null,Object? annualQuota = null,Object? monthlyQuota = null,Object? consumedLeaves = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,balance: null == balance ? _self.balance : balance // ignore: cast_nullable_to_non_nullable
as double,annualQuota: null == annualQuota ? _self.annualQuota : annualQuota // ignore: cast_nullable_to_non_nullable
as double,monthlyQuota: null == monthlyQuota ? _self.monthlyQuota : monthlyQuota // ignore: cast_nullable_to_non_nullable
as double,consumedLeaves: null == consumedLeaves ? _self.consumedLeaves : consumedLeaves // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _AvailableLeaveType implements AvailableLeaveType {
  const _AvailableLeaveType({required this.id, required this.name, required this.balance, required this.annualQuota, required this.monthlyQuota, required this.consumedLeaves});
  factory _AvailableLeaveType.fromJson(Map<String, dynamic> json) => _$AvailableLeaveTypeFromJson(json);

@override final  int id;
@override final  String name;
@override final  double balance;
@override final  double annualQuota;
@override final  double monthlyQuota;
@override final  double consumedLeaves;

/// Create a copy of AvailableLeaveType
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AvailableLeaveTypeCopyWith<_AvailableLeaveType> get copyWith => __$AvailableLeaveTypeCopyWithImpl<_AvailableLeaveType>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AvailableLeaveTypeToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AvailableLeaveType&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.balance, balance) || other.balance == balance)&&(identical(other.annualQuota, annualQuota) || other.annualQuota == annualQuota)&&(identical(other.monthlyQuota, monthlyQuota) || other.monthlyQuota == monthlyQuota)&&(identical(other.consumedLeaves, consumedLeaves) || other.consumedLeaves == consumedLeaves));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,balance,annualQuota,monthlyQuota,consumedLeaves);

@override
String toString() {
  return 'AvailableLeaveType(id: $id, name: $name, balance: $balance, annualQuota: $annualQuota, monthlyQuota: $monthlyQuota, consumedLeaves: $consumedLeaves)';
}


}

/// @nodoc
abstract mixin class _$AvailableLeaveTypeCopyWith<$Res> implements $AvailableLeaveTypeCopyWith<$Res> {
  factory _$AvailableLeaveTypeCopyWith(_AvailableLeaveType value, $Res Function(_AvailableLeaveType) _then) = __$AvailableLeaveTypeCopyWithImpl;
@override @useResult
$Res call({
 int id, String name, double balance, double annualQuota, double monthlyQuota, double consumedLeaves
});




}
/// @nodoc
class __$AvailableLeaveTypeCopyWithImpl<$Res>
    implements _$AvailableLeaveTypeCopyWith<$Res> {
  __$AvailableLeaveTypeCopyWithImpl(this._self, this._then);

  final _AvailableLeaveType _self;
  final $Res Function(_AvailableLeaveType) _then;

/// Create a copy of AvailableLeaveType
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? balance = null,Object? annualQuota = null,Object? monthlyQuota = null,Object? consumedLeaves = null,}) {
  return _then(_AvailableLeaveType(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,balance: null == balance ? _self.balance : balance // ignore: cast_nullable_to_non_nullable
as double,annualQuota: null == annualQuota ? _self.annualQuota : annualQuota // ignore: cast_nullable_to_non_nullable
as double,monthlyQuota: null == monthlyQuota ? _self.monthlyQuota : monthlyQuota // ignore: cast_nullable_to_non_nullable
as double,consumedLeaves: null == consumedLeaves ? _self.consumedLeaves : consumedLeaves // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
