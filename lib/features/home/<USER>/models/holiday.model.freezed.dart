// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'holiday.model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$HolidayModel {

@JsonKey(name: 'holidays') List<HolidayTileData> get holidays;
/// Create a copy of HolidayModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HolidayModelCopyWith<HolidayModel> get copyWith => _$HolidayModelCopyWithImpl<HolidayModel>(this as HolidayModel, _$identity);

  /// Serializes this HolidayModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HolidayModel&&const DeepCollectionEquality().equals(other.holidays, holidays));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(holidays));

@override
String toString() {
  return 'HolidayModel(holidays: $holidays)';
}


}

/// @nodoc
abstract mixin class $HolidayModelCopyWith<$Res>  {
  factory $HolidayModelCopyWith(HolidayModel value, $Res Function(HolidayModel) _then) = _$HolidayModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'holidays') List<HolidayTileData> holidays
});




}
/// @nodoc
class _$HolidayModelCopyWithImpl<$Res>
    implements $HolidayModelCopyWith<$Res> {
  _$HolidayModelCopyWithImpl(this._self, this._then);

  final HolidayModel _self;
  final $Res Function(HolidayModel) _then;

/// Create a copy of HolidayModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? holidays = null,}) {
  return _then(_self.copyWith(
holidays: null == holidays ? _self.holidays : holidays // ignore: cast_nullable_to_non_nullable
as List<HolidayTileData>,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _HolidayModel implements HolidayModel {
  const _HolidayModel({@JsonKey(name: 'holidays') required final  List<HolidayTileData> holidays}): _holidays = holidays;
  factory _HolidayModel.fromJson(Map<String, dynamic> json) => _$HolidayModelFromJson(json);

 final  List<HolidayTileData> _holidays;
@override@JsonKey(name: 'holidays') List<HolidayTileData> get holidays {
  if (_holidays is EqualUnmodifiableListView) return _holidays;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_holidays);
}


/// Create a copy of HolidayModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HolidayModelCopyWith<_HolidayModel> get copyWith => __$HolidayModelCopyWithImpl<_HolidayModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$HolidayModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HolidayModel&&const DeepCollectionEquality().equals(other._holidays, _holidays));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_holidays));

@override
String toString() {
  return 'HolidayModel(holidays: $holidays)';
}


}

/// @nodoc
abstract mixin class _$HolidayModelCopyWith<$Res> implements $HolidayModelCopyWith<$Res> {
  factory _$HolidayModelCopyWith(_HolidayModel value, $Res Function(_HolidayModel) _then) = __$HolidayModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'holidays') List<HolidayTileData> holidays
});




}
/// @nodoc
class __$HolidayModelCopyWithImpl<$Res>
    implements _$HolidayModelCopyWith<$Res> {
  __$HolidayModelCopyWithImpl(this._self, this._then);

  final _HolidayModel _self;
  final $Res Function(_HolidayModel) _then;

/// Create a copy of HolidayModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? holidays = null,}) {
  return _then(_HolidayModel(
holidays: null == holidays ? _self._holidays : holidays // ignore: cast_nullable_to_non_nullable
as List<HolidayTileData>,
  ));
}


}


/// @nodoc
mixin _$HolidayTileData {

 int get id; String get name; DateTime get startDate; DateTime get endDate;
/// Create a copy of HolidayTileData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HolidayTileDataCopyWith<HolidayTileData> get copyWith => _$HolidayTileDataCopyWithImpl<HolidayTileData>(this as HolidayTileData, _$identity);

  /// Serializes this HolidayTileData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HolidayTileData&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,startDate,endDate);

@override
String toString() {
  return 'HolidayTileData(id: $id, name: $name, startDate: $startDate, endDate: $endDate)';
}


}

/// @nodoc
abstract mixin class $HolidayTileDataCopyWith<$Res>  {
  factory $HolidayTileDataCopyWith(HolidayTileData value, $Res Function(HolidayTileData) _then) = _$HolidayTileDataCopyWithImpl;
@useResult
$Res call({
 int id, String name, DateTime startDate, DateTime endDate
});




}
/// @nodoc
class _$HolidayTileDataCopyWithImpl<$Res>
    implements $HolidayTileDataCopyWith<$Res> {
  _$HolidayTileDataCopyWithImpl(this._self, this._then);

  final HolidayTileData _self;
  final $Res Function(HolidayTileData) _then;

/// Create a copy of HolidayTileData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? startDate = null,Object? endDate = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _HolidayTileData implements HolidayTileData {
  const _HolidayTileData({required this.id, required this.name, required this.startDate, required this.endDate});
  factory _HolidayTileData.fromJson(Map<String, dynamic> json) => _$HolidayTileDataFromJson(json);

@override final  int id;
@override final  String name;
@override final  DateTime startDate;
@override final  DateTime endDate;

/// Create a copy of HolidayTileData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HolidayTileDataCopyWith<_HolidayTileData> get copyWith => __$HolidayTileDataCopyWithImpl<_HolidayTileData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$HolidayTileDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HolidayTileData&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,startDate,endDate);

@override
String toString() {
  return 'HolidayTileData(id: $id, name: $name, startDate: $startDate, endDate: $endDate)';
}


}

/// @nodoc
abstract mixin class _$HolidayTileDataCopyWith<$Res> implements $HolidayTileDataCopyWith<$Res> {
  factory _$HolidayTileDataCopyWith(_HolidayTileData value, $Res Function(_HolidayTileData) _then) = __$HolidayTileDataCopyWithImpl;
@override @useResult
$Res call({
 int id, String name, DateTime startDate, DateTime endDate
});




}
/// @nodoc
class __$HolidayTileDataCopyWithImpl<$Res>
    implements _$HolidayTileDataCopyWith<$Res> {
  __$HolidayTileDataCopyWithImpl(this._self, this._then);

  final _HolidayTileData _self;
  final $Res Function(_HolidayTileData) _then;

/// Create a copy of HolidayTileData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? startDate = null,Object? endDate = null,}) {
  return _then(_HolidayTileData(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}

// dart format on
