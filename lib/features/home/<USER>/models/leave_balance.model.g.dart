// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'leave_balance.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_LeaveBalanceModel _$LeaveBalanceModelFromJson(Map<String, dynamic> json) =>
    _LeaveBalanceModel(
      leavePolicy: LeavePolicy.fromJson(
        json['leavePolicy'] as Map<String, dynamic>,
      ),
      availableLeaveTypes: (json['availableLeaveTypes'] as List<dynamic>)
          .map((e) => AvailableLeaveType.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$LeaveBalanceModelToJson(_LeaveBalanceModel instance) =>
    <String, dynamic>{
      'leavePolicy': instance.leavePolicy.toJson(),
      'availableLeaveTypes': instance.availableLeaveTypes
          .map((e) => e.toJson())
          .toList(),
    };

_LeavePolicy _$LeavePolicyFromJson(Map<String, dynamic> json) => _LeavePolicy(
  id: (json['id'] as num).toInt(),
  name: json['name'] as String,
  leaveTypeAllotments: (json['leaveTypeAllots'] as List<dynamic>)
      .map((e) => LeaveTypeAllotments.fromJson(e as Map<String, dynamic>))
      .toList(),
);

Map<String, dynamic> _$LeavePolicyToJson(_LeavePolicy instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'leaveTypeAllots': instance.leaveTypeAllotments,
    };

_LeaveTypeAllotments _$LeaveTypeAllotmentsFromJson(Map<String, dynamic> json) =>
    _LeaveTypeAllotments(
      monthlyQuota: (json['monthlyQuota'] as num).toDouble(),
      annualQuota: (json['annualQuota'] as num).toDouble(),
      leaveType: LeaveType.fromJson(json['leaveType'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$LeaveTypeAllotmentsToJson(
  _LeaveTypeAllotments instance,
) => <String, dynamic>{
  'monthlyQuota': instance.monthlyQuota,
  'annualQuota': instance.annualQuota,
  'leaveType': instance.leaveType,
};

_LeaveType _$LeaveTypeFromJson(Map<String, dynamic> json) =>
    _LeaveType(id: (json['id'] as num).toInt(), name: json['name'] as String);

Map<String, dynamic> _$LeaveTypeToJson(_LeaveType instance) =>
    <String, dynamic>{'id': instance.id, 'name': instance.name};

_AvailableLeaveType _$AvailableLeaveTypeFromJson(Map<String, dynamic> json) =>
    _AvailableLeaveType(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      balance: (json['balance'] as num).toDouble(),
      annualQuota: (json['annualQuota'] as num).toDouble(),
      monthlyQuota: (json['monthlyQuota'] as num).toDouble(),
      consumedLeaves: (json['consumedLeaves'] as num).toDouble(),
    );

Map<String, dynamic> _$AvailableLeaveTypeToJson(_AvailableLeaveType instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'balance': instance.balance,
      'annualQuota': instance.annualQuota,
      'monthlyQuota': instance.monthlyQuota,
      'consumedLeaves': instance.consumedLeaves,
    };
