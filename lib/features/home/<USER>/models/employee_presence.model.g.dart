// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'employee_presence.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_EmployeePresenceModel _$EmployeePresenceModelFromJson(
  Map<String, dynamic> json,
) => _EmployeePresenceModel(
  absentEmployees: employeeTileDataFromJson(
    json['absents'] as Map<String, dynamic>,
  ),
  onLeaveEmployees: employeeTileDataFromJson(
    json['leave'] as Map<String, dynamic>,
  ),
  wfhEmployees: employeeTileDataFromJson(json['wfh'] as Map<String, dynamic>),
);

Map<String, dynamic> _$EmployeePresenceModelToJson(
  _EmployeePresenceModel instance,
) => <String, dynamic>{
  'absents': instance.absentEmployees.map((e) => e.toJson()).toList(),
  'leave': instance.onLeaveEmployees.map((e) => e.toJson()).toList(),
  'wfh': instance.wfhEmployees.map((e) => e.toJson()).toList(),
};

_TeammateDataModel _$TeammateDataModelFromJson(Map<String, dynamic> json) =>
    _TeammateDataModel(
      id: (json['id'] as num).toInt(),
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      totalDays: (json['totalDays'] as num?)?.toInt(),
      startDaySlot: json['startDaySlot'] as String?,
      endDaySlot: json['endDaySlot'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      employeeId: (json['EmployeeId'] as num?)?.toInt(),
      employee: employeeTileDataFromTeammateData(
        json['employee'] as Map<String, dynamic>,
      ),
      $daySlot: json[r'$daySlot'] as String?,
    );

Map<String, dynamic> _$TeammateDataModelToJson(_TeammateDataModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'startDate': instance.startDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'totalDays': instance.totalDays,
      'startDaySlot': instance.startDaySlot,
      'endDaySlot': instance.endDaySlot,
      'createdAt': instance.createdAt?.toIso8601String(),
      'EmployeeId': instance.employeeId,
      'employee': instance.employee,
      r'$daySlot': instance.$daySlot,
    };

_EmployeeTileData _$EmployeeTileDataFromJson(Map<String, dynamic> json) =>
    _EmployeeTileData(
      id: (json['id'] as num).toInt(),
      displayName: json['displayName'] as String,
      image: json['image'] as String?,
    );

Map<String, dynamic> _$EmployeeTileDataToJson(_EmployeeTileData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'displayName': instance.displayName,
      'image': instance.image,
    };
