import 'package:freezed_annotation/freezed_annotation.dart';

part 'clock_in.model.freezed.dart';
part 'clock_in.model.g.dart';

@freezed
abstract class LastClockInModel with _$LastClockInModel {
  const factory LastClockInModel({
    required final int id,
    final DateTime? clockIn,
    final DateTime? clockOut,
    final double? hoursWorked,
    final DateTime? createdAt,
  }) = _LastClockInModel;

  factory LastClockInModel.fromJson(Map<String, dynamic> json) =>
      _$LastClockInModelFromJson(json);
}

@freezed
abstract class ProjectModel with _$ProjectModel {
  const factory ProjectModel({
    required final int id,
    required final String name,
  }) = _ProjectModel;

  factory ProjectModel.fromJson(Map<String, dynamic> json) =>
      _$ProjectModelFromJson(json);
}
