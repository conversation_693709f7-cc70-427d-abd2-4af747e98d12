import 'package:freezed_annotation/freezed_annotation.dart';

part 'wishes.model.freezed.dart';
part 'wishes.model.g.dart';

List<WishEmployeeTileData> bithdayEmployeeDataFromJson(List<dynamic> json) {
  return (((json)[0]) as List<dynamic>)
      .map(
        (row) => WishEmployeeTileData.fromJson(
          row['user'],
        ).copyWith(date: DateTime.tryParse(row['user']['dob'])),
      )
      .toList();
}

List<WishEmployeeTileData> anniversaryEmployeeDataFromJson(List<dynamic> json) {
  return (((json)[0]) as List<dynamic>)
      .map(
        (row) => WishEmployeeTileData.fromJson(
          row['user'],
        ).copyWith(date: DateTime.tryParse(row['joiningDate'])),
      )
      .toList();
}

@freezed
abstract class WishesModel with _$WishesModel {
  @JsonSerializable(explicitToJson: true)
  const factory WishesModel({
    @JsonKey(name: 'forBirthday', fromJson: bithdayEmployeeDataFromJson)
    required final List<WishEmployeeTileData> birthdayEmployees,
    @JsonKey(name: 'forAnniversary', fromJson: anniversaryEmployeeDataFromJson)
    required final List<WishEmployeeTileData> anniversaryEmployees,
    @JsonKey(name: 'forNewJoiners', fromJson: anniversaryEmployeeDataFromJson)
    required final List<WishEmployeeTileData> newJoineeEmployees,
  }) = _WishesModel;

  factory WishesModel.fromJson(Map<String, dynamic> json) =>
      _$WishesModelFromJson(json);
}

@freezed
abstract class WishEmployeeTileData with _$WishEmployeeTileData {
  const factory WishEmployeeTileData({
    required final int id,
    required final String displayName,
    required final String? image,
    required final DateTime? date,
  }) = _WishEmployeeTileData;

  factory WishEmployeeTileData.fromJson(Map<String, dynamic> json) =>
      _$WishEmployeeTileDataFromJson(json);
}
