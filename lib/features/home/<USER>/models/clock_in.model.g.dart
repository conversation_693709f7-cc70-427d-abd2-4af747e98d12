// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'clock_in.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_LastClockInModel _$LastClockInModelFromJson(Map<String, dynamic> json) =>
    _LastClockInModel(
      id: (json['id'] as num).toInt(),
      clockIn: json['clockIn'] == null
          ? null
          : DateTime.parse(json['clockIn'] as String),
      clockOut: json['clockOut'] == null
          ? null
          : DateTime.parse(json['clockOut'] as String),
      hoursWorked: (json['hoursWorked'] as num?)?.toDouble(),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$LastClockInModelToJson(_LastClockInModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'clockIn': instance.clockIn?.toIso8601String(),
      'clockOut': instance.clockOut?.toIso8601String(),
      'hoursWorked': instance.hoursWorked,
      'createdAt': instance.createdAt?.toIso8601String(),
    };

_ProjectModel _$ProjectModelFromJson(Map<String, dynamic> json) =>
    _ProjectModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
    );

Map<String, dynamic> _$ProjectModelToJson(_ProjectModel instance) =>
    <String, dynamic>{'id': instance.id, 'name': instance.name};
