import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/core/utils/helpers/snackbar.helper.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/home/<USER>/models/work_update.model.dart';
import 'package:hrms_tst/shared/widgets/confirmation_dialog.dart';

import '../domain/clock_in_out.controller.dart';
import '../domain/work_update.controller.dart';
import 'widgets/work_update_dialog.dart';

class WorkUpdateScreen extends HookConsumerWidget {
  const WorkUpdateScreen({super.key});

  static const String name = 'Work Update';
  static const String path = '/work-update';

  static GoRoute route({List<RouteBase> routes = const []}) => GoRoute(
    path: path,
    name: name,
    routes: routes,
    pageBuilder: (context, state) =>
        const NoTransitionPage(child: WorkUpdateScreen()),
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(workUpdateProvider);
    final notifier = ref.read(workUpdateProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: Text(name),
        actions: [
          IconButton.filledTonal(
            onPressed: () async {
              if (state.isEmpty) {
                context.showError('Please add Today\'s Work Update');
                return;
              }

              showAdaptiveDialog(
                context: context,
                builder: (dialogContext) => ConfirmationDialog(
                  title: 'Are you sure you want to Clock Out?',
                  confirmText: 'Yes',
                  onConfirm: () async {
                    (await ref
                            .read(clockInOutProvider.notifier)
                            .clockOut(workUpdates: state))
                        .fold(
                          onSuccess: (_) {
                            context.pop();
                            context.showSuccess('Clocked Out Successfully');
                          },
                          onFailure: (message) => context.showError(message),
                        );
                  },
                ),
              );
            },
            icon: Icon(Icons.check),
          ),
          Space.x(8),
        ],
      ),
      body: ListView(
        children: [
          GestureDetector(
            onTap: () {
              final lastUpdateTime =
                  state.lastOrNull?.endTime ??
                  TimeOfDay.fromDateTime(
                    ref
                        .read(clockInOutProvider)
                        .requireValue
                        .clockIn!
                        .toLocal(),
                  );

              if (lastUpdateTime.isAtSameTimeAs(TimeOfDay.now()) ||
                  lastUpdateTime.isAfter(TimeOfDay.now())) {
                if (state.isEmpty) {
                  context.showInformation('No time has passed since clock in');
                } else {
                  context.showInformation(
                    'Already added update for work duration',
                  );
                }
                return;
              }
              showDialog(
                context: context,
                builder: (context) {
                  return WorkUpdateDialog(
                    editIndex: null,
                    workUpdate: WorkUpdateModel(
                      update: '',
                      hours: null,
                      startTime: lastUpdateTime,
                      endTime: TimeOfDay.now(),
                    ),
                  );
                },
              );
            },
            child: Container(
              margin: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              padding: EdgeInsets.symmetric(vertical: 4, horizontal: 16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(14),
                color: context.colors.surface,
              ),
              child: ListTile(
                leading: Icon(Icons.add),
                title: Text('Add Work Update'),
              ),
            ),
          ),
          Divider(),
          ...state.indexed.map(
            (e) => Container(
              margin: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              padding: EdgeInsets.symmetric(vertical: 4),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(14),
                color: context.colors.surface,
              ),
              child: ListTile(
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (context) {
                      return WorkUpdateDialog(
                        workUpdate: e.$2,
                        editIndex: e.$1,
                      );
                    },
                  );
                },
                contentPadding: EdgeInsets.only(left: 16, right: 8),
                leading: Icon(Icons.edit),
                title: Text(e.$2.project?.name ?? ''),
                trailing: IconButton(
                  onPressed: () {
                    notifier.removeWorkUpdate(e.$2, e.$1);
                  },
                  icon: Icon(Icons.delete, color: context.colors.error),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
