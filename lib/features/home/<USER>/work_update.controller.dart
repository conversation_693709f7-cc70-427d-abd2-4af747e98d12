import 'package:hrms_tst/features/home/<USER>/models/work_update.model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'work_update.controller.g.dart';

@riverpod
class WorkUpdate extends _$WorkUpdate {
  @override
  List<WorkUpdateModel> build() {
    return [];
  }

  void addWorkUpdate(WorkUpdateModel workUpdate, [int? editIndex]) {
    if (editIndex != null) {
      state = [...state..[editIndex] = workUpdate];
      return;
    }
    state = [...state, workUpdate];
  }

  void removeWorkUpdate(WorkUpdateModel workUpdate, int index) {
    state = [...state..removeAt(index)];
  }
}
