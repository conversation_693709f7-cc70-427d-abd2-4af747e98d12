// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'work_update.controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$workUpdateHash() => r'44966e59db7c3dae8fb7cf9395f688018aa9c29a';

/// See also [WorkUpdate].
@ProviderFor(WorkUpdate)
final workUpdateProvider =
    AutoDisposeNotifierProvider<WorkUpdate, List<WorkUpdateModel>>.internal(
      WorkUpdate.new,
      name: r'workUpdateProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$workUpdateHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$WorkUpdate = AutoDisposeNotifier<List<WorkUpdateModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
