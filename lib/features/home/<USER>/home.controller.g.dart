// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home.controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$projectsHash() => r'55914e6afa0e34049f23a6841e3705d8e280b0ee';

/// See also [projects].
@ProviderFor(projects)
final projectsProvider = AutoDisposeFutureProvider<List<ProjectModel>>.internal(
  projects,
  name: r'projectsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$projectsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ProjectsRef = AutoDisposeFutureProviderRef<List<ProjectModel>>;
String _$leaveBalanceHash() => r'814ff006515dc5c8f3fd901c5a8c417f04f47d96';

/// See also [leaveBalance].
@ProviderFor(leaveBalance)
final leaveBalanceProvider = FutureProvider<LeaveBalanceModel>.internal(
  leaveBalance,
  name: r'leaveBalanceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$leaveBalanceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LeaveBalanceRef = FutureProviderRef<LeaveBalanceModel>;
String _$employeePresenceHash() => r'b3a585adbeb1b7f61bbcd9d5bc7aae647e54f008';

/// See also [employeePresence].
@ProviderFor(employeePresence)
final employeePresenceProvider =
    AutoDisposeFutureProvider<EmployeePresenceModel>.internal(
      employeePresence,
      name: r'employeePresenceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$employeePresenceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef EmployeePresenceRef =
    AutoDisposeFutureProviderRef<EmployeePresenceModel>;
String _$wishesHash() => r'f7d2e785ed6e810288e35b098708c09fad6e6b7e';

/// See also [wishes].
@ProviderFor(wishes)
final wishesProvider = AutoDisposeFutureProvider<WishesModel>.internal(
  wishes,
  name: r'wishesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$wishesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WishesRef = AutoDisposeFutureProviderRef<WishesModel>;
String _$holidaysHash() => r'472350131c22f637086deb1402ba5788a299a9e4';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [holidays].
@ProviderFor(holidays)
const holidaysProvider = HolidaysFamily();

/// See also [holidays].
class HolidaysFamily extends Family<AsyncValue<HolidayModel>> {
  /// See also [holidays].
  const HolidaysFamily();

  /// See also [holidays].
  HolidaysProvider call(DateTime? date) {
    return HolidaysProvider(date);
  }

  @override
  HolidaysProvider getProviderOverride(covariant HolidaysProvider provider) {
    return call(provider.date);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'holidaysProvider';
}

/// See also [holidays].
class HolidaysProvider extends AutoDisposeFutureProvider<HolidayModel> {
  /// See also [holidays].
  HolidaysProvider(DateTime? date)
    : this._internal(
        (ref) => holidays(ref as HolidaysRef, date),
        from: holidaysProvider,
        name: r'holidaysProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$holidaysHash,
        dependencies: HolidaysFamily._dependencies,
        allTransitiveDependencies: HolidaysFamily._allTransitiveDependencies,
        date: date,
      );

  HolidaysProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.date,
  }) : super.internal();

  final DateTime? date;

  @override
  Override overrideWith(
    FutureOr<HolidayModel> Function(HolidaysRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: HolidaysProvider._internal(
        (ref) => create(ref as HolidaysRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        date: date,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<HolidayModel> createElement() {
    return _HolidaysProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is HolidaysProvider && other.date == date;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, date.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin HolidaysRef on AutoDisposeFutureProviderRef<HolidayModel> {
  /// The parameter `date` of this provider.
  DateTime? get date;
}

class _HolidaysProviderElement
    extends AutoDisposeFutureProviderElement<HolidayModel>
    with HolidaysRef {
  _HolidaysProviderElement(super.provider);

  @override
  DateTime? get date => (origin as HolidaysProvider).date;
}

String _$teammatePresenceHash() => r'708d550c3c30c7b14346830b3c8737a32d5b9184';

/// See also [teammatePresence].
@ProviderFor(teammatePresence)
const teammatePresenceProvider = TeammatePresenceFamily();

/// See also [teammatePresence].
class TeammatePresenceFamily
    extends Family<AsyncValue<List<TeammateDataModel>>> {
  /// See also [teammatePresence].
  const TeammatePresenceFamily();

  /// See also [teammatePresence].
  TeammatePresenceProvider call(String type, bool isUpcoming) {
    return TeammatePresenceProvider(type, isUpcoming);
  }

  @override
  TeammatePresenceProvider getProviderOverride(
    covariant TeammatePresenceProvider provider,
  ) {
    return call(provider.type, provider.isUpcoming);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'teammatePresenceProvider';
}

/// See also [teammatePresence].
class TeammatePresenceProvider
    extends AutoDisposeFutureProvider<List<TeammateDataModel>> {
  /// See also [teammatePresence].
  TeammatePresenceProvider(String type, bool isUpcoming)
    : this._internal(
        (ref) => teammatePresence(ref as TeammatePresenceRef, type, isUpcoming),
        from: teammatePresenceProvider,
        name: r'teammatePresenceProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$teammatePresenceHash,
        dependencies: TeammatePresenceFamily._dependencies,
        allTransitiveDependencies:
            TeammatePresenceFamily._allTransitiveDependencies,
        type: type,
        isUpcoming: isUpcoming,
      );

  TeammatePresenceProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.type,
    required this.isUpcoming,
  }) : super.internal();

  final String type;
  final bool isUpcoming;

  @override
  Override overrideWith(
    FutureOr<List<TeammateDataModel>> Function(TeammatePresenceRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TeammatePresenceProvider._internal(
        (ref) => create(ref as TeammatePresenceRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        type: type,
        isUpcoming: isUpcoming,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<TeammateDataModel>> createElement() {
    return _TeammatePresenceProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TeammatePresenceProvider &&
        other.type == type &&
        other.isUpcoming == isUpcoming;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, type.hashCode);
    hash = _SystemHash.combine(hash, isUpcoming.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TeammatePresenceRef
    on AutoDisposeFutureProviderRef<List<TeammateDataModel>> {
  /// The parameter `type` of this provider.
  String get type;

  /// The parameter `isUpcoming` of this provider.
  bool get isUpcoming;
}

class _TeammatePresenceProviderElement
    extends AutoDisposeFutureProviderElement<List<TeammateDataModel>>
    with TeammatePresenceRef {
  _TeammatePresenceProviderElement(super.provider);

  @override
  String get type => (origin as TeammatePresenceProvider).type;
  @override
  bool get isUpcoming => (origin as TeammatePresenceProvider).isUpcoming;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
