import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/features/home/<USER>/models/clock_in.model.dart';
import 'package:hrms_tst/features/home/<USER>/models/employee_presence.model.dart';
import 'package:hrms_tst/features/home/<USER>/models/holiday.model.dart';
import 'package:hrms_tst/features/home/<USER>/models/leave_balance.model.dart';
import 'package:hrms_tst/features/home/<USER>/models/wishes.model.dart';

import 'models/work_update.model.dart';

abstract interface class HomeProtocol {
  Future<Result<List<ProjectModel>>> getProjects();
  Future<Result<LastClockInModel>> getLastClockIn();
  Future<Result<String?>> clockIn({
    required double? lat,
    required double? long,
  });
  Future<Result<void>> clockOut({required List<WorkUpdateModel> workUpdates});

  Future<Result<LeaveBalanceModel>> getLeaveBalance({
    required DateTime currentTime,
  });

  Future<Result<EmployeePresenceModel>> getEmployeePresence();
  Future<Result<WishesModel>> getWishes();
  Future<Result<HolidayModel>> getHolidays({required DateTime? date});
  Future<Result<List<TeammateDataModel>>> getAllPresences({
    required String type,
    required bool isUpcoming,
  });
}
