import 'dart:developer';

import 'package:geolocator/geolocator.dart';
import 'package:hrms_tst/core/services/geofencing/geofencing.service.dart';
import 'package:hrms_tst/core/services/notifications/local_notifications/local_notifications.protocol.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:hrms_tst/features/home/<USER>/models/clock_in.model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../data/home.protocol.dart';
import '../data/models/work_update.model.dart';

part 'clock_in_out.controller.g.dart';

@riverpod
class ClockInOut extends _$ClockInOut {
  final _localNotificationService = getIt.get<LocalNotificationsProtocol>();

  @override
  FutureOr<LastClockInModel> build() async {
    return (await getIt.get<HomeProtocol>().getLastClockIn()).ignoreFailure();
  }

  Future<Result<String?>> clockIn() async {
    final fallbackState = state.requireValue;
    state = AsyncLoading();
    final position = await getLocation(
      onError: (message) {
        state = AsyncData(fallbackState);
        return Failure(message);
      },
    );

    if (position == null) {
      state = AsyncData(fallbackState);
      return Failure('Unable to get location');
    }

    final result = await getIt.get<HomeProtocol>().clockIn(
      lat: position.latitude,
      long: position.longitude,
    );

    result.fold(
      onSuccess: (data) => ref.invalidateSelf(),
      onFailure: (message) {
        state = AsyncData(fallbackState);
        return Failure(message);
      },
    );

    return result;
  }

  Future<Result<void>> clockOut({
    required List<WorkUpdateModel> workUpdates,
  }) async {
    final result = await getIt.get<HomeProtocol>().clockOut(
      workUpdates: workUpdates,
    );
    if (result.isSuccess) {
      ref.invalidateSelf();
    }

    return result;
  }

  Future<Position?> getLocation({
    required Function(String message) onError,
  }) async {
    LocationPermission permission;

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        onError(
          'Location permission is required for attendance tracking. Please grant location access.',
        );
        return null;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      onError(
        'Location permission is permanently denied. Please enable location access in app settings to continue.',
      );
      return null;
    }

    // When we reach here, permissions are granted and we can
    // continue accessing the position of the device.
    try {
      return await Geolocator.getCurrentPosition(
        locationSettings: LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 30),
        ),
      );
    } catch (e) {
      onError(
        'Unable to get current location. Please check if location services are enabled and try again.',
      );
      return null;
    }
  }

  Future<void> setupGeofence({
    required Function(String message) onError,
  }) async {
    try {
      final geofence = getIt.get<GeofencingService>();
      final initResult = await geofence.init(onError: onError);

      if (initResult.isFailure) {
        // Error already handled by onError callback in init method
        return;
      }

      final addedGeofences = await geofence.getGeofences();
      await addedGeofences.fold(
        onSuccess: (fences) async {
          final isAlreadyAdded = fences
              .map((e) => e.id)
              .contains('surat-office');
          if (isAlreadyAdded) {
            final recreateResult = await geofence.reCreateAfterReboot();
            recreateResult.fold(
              onSuccess: (_) => log('Geofences recreated successfully'),
              onFailure: (message) {
                log('Failed to recreate geofences: $message');
                onError(
                  'Failed to setup automatic attendance tracking. Please try again.',
                );
              },
            );
          } else {
            final addResult = await geofence.addGeofence(
              id: 'surat-office',
              latitude: 21.235,
              longitude: 72.871,
              radiusInMeters: 100,
            );
            addResult.fold(
              onSuccess: (_) async {
                log('Geofence added successfully');
                await _localNotificationService.showNotification(
                  title: 'Auto Clock In Enabled',
                  body:
                      'You will be automatically clocked in when you enter the office location.',
                );
              },
              onFailure: (message) {
                log('Failed to add geofence: $message');
                onError(
                  'Failed to setup automatic attendance tracking. Please try again.',
                );
              },
            );
          }
        },
        onFailure: (message) {
          log('Failed to get geofences: $message');
          onError(
            'Unable to access geofencing service. Please check your device settings and try again.',
          );
        },
      );
    } catch (e) {
      log('Unexpected error in setupGeofence: $e');
      onError(
        'An unexpected error occurred while setting up automatic attendance. Please try again.',
      );
    }
  }

  Future<void> removeGeofence() async {
    try {
      final geofence = getIt.get<GeofencingService>();
      final removeResult = await geofence.removeGeofence(id: 'surat-office');
      removeResult.fold(
        onSuccess: (_) async {
          log('Geofence removed successfully');
          await _localNotificationService.showNotification(
            title: 'Auto Clock In Disabled',
            body:
                'Automatic clock-in has been disabled. You will need to manually clock in from now on.',
          );
        },
        onFailure: (message) {
          log('Failed to remove geofence: $message');
        },
      );
    } catch (e) {
      log('Unexpected error in removeGeofence: $e');
    }
  }
}
