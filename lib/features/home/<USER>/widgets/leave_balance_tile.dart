import 'dart:math';

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/extensions/number.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/home/<USER>/home.controller.dart';
import 'package:hrms_tst/features/request/presentation/widgets/leave_request_dialog.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';

class LeaveBalanceTile extends ConsumerWidget {
  const LeaveBalanceTile({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final leaveBalance = ref.watch(leaveBalanceProvider);
    return CardContainer(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'Leave Balance',
                  style: context.textStyles.titleMedium?.copyWith(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: context.colors.secondary,
                  ),
                ),
                Spacer(),
                GestureDetector(
                  onTap: () {
                    showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (context) => LeaveRequestDialog(),
                    );
                  },
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        'Request Leave',
                        style: context.textStyles.titleMedium?.copyWith(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: context.colors.secondary,
                        ),
                      ),
                      Space.x(2),
                      SVGImage(
                        Assets.svgs.forwardArrow,
                        height: 18,
                        color: context.colors.secondary,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Space.y(18),
            Wrap(
              alignment: WrapAlignment.spaceAround,
              spacing: 8,
              runSpacing: 16,
              children: switch (leaveBalance) {
                AsyncData(:final value) =>
                  value.availableLeaveTypes
                      .map(
                        (leave) => CircleChart(
                          label: 'Days',
                          value: leave.balance == 365
                              ? '∞'
                              : leave.balance.showFractionIfAvailable,
                          footer: leave.name,
                          progressFactor: leave.balance / leave.annualQuota,
                          color: [
                            Colors.green,
                            Colors.red,
                            Colors.blue,
                            Colors.purple,
                            Colors.amber,
                          ][(leave.id - 1).clamp(0, 4)],
                        ),
                      )
                      .toList(),
                AsyncError(:final error) => [Text(error.toString())],
                _ => List.generate(
                  3,
                  (index) => CircleChart(
                    label: 'Days',
                    value: '0',
                    footer: 'Leave',
                    progressFactor: 100,
                    color: context.colors.outline,
                  ),
                ),
              },
            ),
          ],
        ),
      ),
    );
  }
}

class CircleChart extends ConsumerWidget {
  const CircleChart({
    super.key,
    this.color = Colors.blue,
    required this.label,
    required this.value,
    this.progressFactor = 0.0,
    required this.footer,
  });

  final Color color;
  final String label;
  final String value;
  final double progressFactor;
  final String footer;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            SizedBox(
              width: 80,
              height: 80,
              child: Transform.rotate(
                angle: pi, // Rotate 180 degrees (pi radians)
                child: CircularProgressIndicator(
                  value: progressFactor, // 70% filled arc
                  strokeWidth: 8,
                  backgroundColor: context.colors.outline,
                  valueColor: AlwaysStoppedAnimation<Color>(color),
                  strokeCap: StrokeCap.round,
                ),
              ),
            ),

            // Custom center child
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Space.y(2),

                Text(
                  value,
                  style: context.textStyles.bodySmall?.copyWith(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: context.colors.secondary,
                  ),
                ),
                Text(
                  label,
                  style: context.textStyles.bodySmall?.copyWith(
                    fontSize: 10,
                    fontWeight: FontWeight.w400,
                    color: context.colors.secondary,
                  ),
                ),
              ],
            ),
          ],
        ),
        Space.y(16),
        Text(
          footer,
          style: context.textStyles.bodySmall?.copyWith(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: context.colors.secondary,
          ),
        ),
      ],
    );
  }
}
