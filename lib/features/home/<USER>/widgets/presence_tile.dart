import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/home/<USER>/models/employee_presence.model.dart';
import 'package:hrms_tst/features/teammates/presentation/teammate_detail.screen.dart';
import 'package:hrms_tst/shared/widgets/cached_image.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';
import 'package:hrms_tst/shared/widgets/loading_placeholder.dart';

import '../../domain/home.controller.dart';
import 'view_all_dialog.dart';

class PresenceTile extends HookConsumerWidget {
  const PresenceTile({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final presence = ref.watch(employeePresenceProvider);

    final selectedFilter = useState(PresenceFilter.onLeave);
    return CardContainer(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                PresenceFilterOptions(
                  selectedFilter: selectedFilter.value,
                  onFilterSelected: (filter) {
                    selectedFilter.value = filter;
                  },
                ),
                Spacer(),
                GestureDetector(
                  onTap: () {
                    showDialog(
                      context: context,
                      builder: (context) {
                        return switch (selectedFilter.value) {
                          PresenceFilter.onLeave => ViewAllDialog(
                            label: 'On Leave',
                            type: 'leave',
                          ),
                          PresenceFilter.remote => ViewAllDialog(
                            label: 'Remote',
                            type: 'wfh',
                          ),
                          PresenceFilter.absent => ViewAllDialog(
                            label: 'Absent',
                            type: 'absent',
                          ),
                        };
                      },
                    );
                  },
                  child: Text(
                    'View all',
                    style: context.textStyles.titleLarge?.copyWith(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: context.colors.secondary,
                    ),
                  ),
                ),
              ],
            ),
            Space.y(18),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                spacing: 8,
                children: switch (presence) {
                  AsyncData(:final value) => () {
                    final ({List<EmployeeTileData> list, String emptyMessage})
                    data = switch (selectedFilter.value) {
                      PresenceFilter.onLeave => (
                        list: value.onLeaveEmployees,
                        emptyMessage: 'No One is On Leave Today🎉',
                      ),
                      PresenceFilter.remote => (
                        list: value.wfhEmployees,
                        emptyMessage: 'No One is Working Remotely Today🎉',
                      ),
                      PresenceFilter.absent => (
                        list: value.absentEmployees,
                        emptyMessage: 'No One is Absent Today🎉',
                      ),
                    };
                    return data.list.isEmpty
                        ? [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6.0,
                              ).copyWith(top: 22, bottom: 22),
                              child: Text(
                                data.emptyMessage,
                                style: context.textStyles.bodySmall?.copyWith(
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ]
                        : data.list
                              .map(
                                (e) =>
                                    PresenceEmployeeTile(employeeTileData: e),
                              )
                              .toList();
                  }(),
                  AsyncError(:final error) => [Text(error.toString())],
                  _ => List.generate(
                    3,
                    (index) => Column(
                      children: [
                        LoadingPlaceholder(
                          decoration: BoxDecoration(
                            color: context.colors.outline,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          height: 41,
                          width: 41,
                        ),
                        Space.y(4),
                        LoadingPlaceholder(
                          decoration: BoxDecoration(
                            color: context.colors.outline,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          width: 60,
                          height: 18,
                        ),
                      ],
                    ),
                  ),
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class PresenceEmployeeTile extends StatelessWidget {
  const PresenceEmployeeTile({super.key, required this.employeeTileData});

  final EmployeeTileData employeeTileData;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        context.goNamed(
          TeammateDetailScreen.name,
          pathParameters: {'id': employeeTileData.id.toString()},
        );
      },
      child: Column(
        children: [
          CachedImage(
            url: employeeTileData.image ?? '',
            radiusValue: 16,
            height: 41,
            width: 41,
          ),
          Space.y(4),
          SizedBox(
            width: 60,
            child: Text(
              employeeTileData.displayName,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: context.textStyles.titleMedium?.copyWith(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: context.colors.secondary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

enum PresenceFilter {
  onLeave('On Leave'),
  remote('Remote'),
  absent('Absent');

  final String label;
  const PresenceFilter(this.label);
}

class PresenceFilterOptions extends StatelessWidget {
  const PresenceFilterOptions({
    super.key,
    required this.selectedFilter,
    required this.onFilterSelected,
  });

  final PresenceFilter selectedFilter;
  final Function(PresenceFilter filter) onFilterSelected;

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 8,
      children: PresenceFilter.values.map((e) {
        final isSelected = e == selectedFilter;
        return GestureDetector(
          onTap: () {
            onFilterSelected(e);
          },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.5),
              color: isSelected ? Colors.red : context.colors.outline,
            ),
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            child: Text(
              e.label,
              style: context.textStyles.titleMedium?.copyWith(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: isSelected
                    ? context.colors.surface
                    : context.colors.secondary,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
