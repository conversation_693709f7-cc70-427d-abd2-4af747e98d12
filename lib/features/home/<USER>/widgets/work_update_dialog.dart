import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/extensions/string.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/home/<USER>/models/clock_in.model.dart';
import 'package:hrms_tst/features/home/<USER>/models/work_update.model.dart';
import 'package:hrms_tst/features/home/<USER>/home.controller.dart';
import 'package:hrms_tst/features/home/<USER>/work_update.controller.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/app_async_dropdown_field.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';
import 'package:hrms_tst/shared/widgets/time_range_picker.dart';
import 'package:hrms_tst/shared/widgets/app_html_editor.dart';

class WorkUpdateDialog extends HookConsumerWidget {
  const WorkUpdateDialog({
    super.key,
    required this.workUpdate,
    required this.editIndex,
  });

  final WorkUpdateModel? workUpdate;
  final int? editIndex;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useState(GlobalKey<FormState>());
    final notifier = ref.read(workUpdateProvider.notifier);

    final selectedProject = useState<ProjectModel?>(workUpdate?.project);
    final startTime = useState<TimeOfDay>(
      workUpdate?.startTime ?? TimeOfDay(hour: 09, minute: 30),
    );
    final endTime = useState<TimeOfDay>(workUpdate?.endTime ?? TimeOfDay.now());
    final isTimeValid = useState(true);
    final updateText = useState(workUpdate?.update ?? '');

    final editorKey = useState(GlobalKey());

    return Dialog.fullscreen(
      backgroundColor: context.colors.surface,
      child: Form(
        key: formKey.value,

        child: ListView(
          padding: EdgeInsets.all(16),
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Add Work Update',
                  style: context.textStyles.titleLarge?.copyWith(
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                    color: context.colors.secondary,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    context.pop();
                  },
                  icon: SVGImage(
                    Assets.svgs.closeIcon,
                    height: 24,
                    width: 24,
                    color: context.colors.secondary,
                  ),
                ),
              ],
            ),
            Space.y(10),
            FormField(
              validator: (value) {
                if (!isTimeValid.value) {
                  return 'Time range is not valid';
                }
                return null;
              },
              builder: (field) => Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TimeRangePicker(
                    isStartTimeSelectable: false,
                    startTime: startTime.value,
                    endTime: endTime.value,
                    onSelectionChange: (start, end, valid) {
                      startTime.value = start;
                      endTime.value = end;
                      isTimeValid.value = valid ?? false;
                      field.validate();
                    },
                  ),
                  !field.hasError
                      ? Space.y(22)
                      : Text(
                          'Time range is not valid',
                          style: context.textStyles.bodyMedium?.copyWith(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: context.colors.error,
                          ),
                        ),
                ],
              ),
            ),

            Space.y(8),
            Text(
              'Select Project',
              style: context.textStyles.bodyMedium?.copyWith(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Color(0xFF7A7A7A),
              ),
            ),
            Space.y(4),
            AppAsyncDropdownField(
              items: ref
                  .watch(projectsProvider)
                  .whenData(
                    (value) => value
                        .map(
                          (e) =>
                              DropdownMenuItem(value: e, child: Text(e.name)),
                        )
                        .toList(),
                  ),
              value: selectedProject.value,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              enabled: true,
              readOnly: false,
              validator: (project) {
                if (project == null) return 'Please select project';
                return null;
              },

              fillColor: context.colors.surface,
              onChanged: (project) {
                selectedProject.value = project;
              },
            ),
            Space.y(10),
            FormField(
              builder: (field) => Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  AppHTMLEditor(
                    key: editorKey.value,
                    fieldState: field,
                    onFocus: () {
                      Future.delayed(Duration(milliseconds: 400), () {
                        Scrollable.ensureVisible(
                          editorKey.value.currentContext!,
                          duration: Duration(milliseconds: 300),
                        );
                      });
                    },
                    initialValue: updateText.value,
                    onChanged: (value) {
                      updateText.value = value;
                    },
                  ),
                  if (field.hasError)
                    Padding(
                      padding: const EdgeInsets.only(top: 4.0),
                      child: Text(
                        field.errorText!,
                        style: context.textStyles.labelMedium?.copyWith(
                          color: context.colors.error,
                        ),
                      ),
                    ),
                ],
              ),
              validator: (value) {
                if (updateText.value.trim().isEmpty) {
                  return 'Please add work update';
                }
                if (updateText.value.stripHTMLTags().trim().isEmpty) {
                  return 'Please add work update';
                }
                return null;
              },
            ),
            Space.y(10),
            OutlinedButton(
              onPressed: () async {
                if (!formKey.value.currentState!.validate() ||
                    selectedProject.value == null) {
                  return;
                }

                notifier.addWorkUpdate(
                  WorkUpdateModel(
                    projectId: selectedProject.value?.id,
                    project: selectedProject.value,
                    update: updateText.value,
                    startTime: startTime.value,
                    endTime: endTime.value,
                    hours: getDifference(startTime.value, endTime.value),
                  ),
                  editIndex,
                );

                if (context.mounted) {
                  context.pop();
                }
              },
              child: Text(
                'Save Work Update',
                style: context.textStyles.titleMedium?.copyWith(
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  color: context.colors.primary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  double getDifference(TimeOfDay start, TimeOfDay end) {
    final mins =
        (end.hour * 60 + end.minute) - (start.hour * 60 + start.minute);
    return double.parse((mins / 60).toStringAsFixed(2));
  }
}
