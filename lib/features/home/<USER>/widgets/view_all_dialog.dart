import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/home/<USER>/models/employee_presence.model.dart';
import 'package:hrms_tst/features/home/<USER>/home.controller.dart';
import 'package:hrms_tst/features/teammates/presentation/teammate_detail.screen.dart';
import 'package:hrms_tst/shared/widgets/cached_image.dart';
import 'package:intl/intl.dart';

class ViewAllDialog extends HookConsumerWidget {
  const ViewAllDialog({super.key, required this.label, required this.type});

  final String label;
  final String type;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isUpcoming = useState(false);

    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 20),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Space.y(16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Text(
                  label,
                  style: context.textStyles.titleMedium?.copyWith(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: context.colors.secondary,
                  ),
                ),
                Spacer(),
                if (type != 'absent')
                  Row(
                    children: [
                      Text('Upcoming'),
                      Space.x(8),
                      Switch(
                        value: isUpcoming.value,
                        onChanged: (value) {
                          isUpcoming.value = value;
                        },
                      ),
                    ],
                  ),
              ],
            ),
          ),
          Space.y(8),
          SizedBox(
            height: 400,
            child: switch (ref.watch(
              teammatePresenceProvider(type, isUpcoming.value),
            )) {
              AsyncData(:final value) =>
                value.isEmpty
                    ? Center(
                        child: Text(switch (type) {
                          'leave' =>
                            isUpcoming.value
                                ? 'No employees have upcoming leave.'
                                : 'No employee is on leave today',
                          'wfh' =>
                            isUpcoming.value
                                ? 'No employees have upcoming work from home.'
                                : 'No employee is working from home',
                          'absent' => 'No employee is absent today',
                          _ => '',
                        }),
                      )
                    : ListView.separated(
                        itemCount: value.length,
                        separatorBuilder: (context, index) =>
                            Divider(indent: 16, endIndent: 16),
                        padding: EdgeInsets.only(bottom: 12),
                        itemBuilder: (context, index) {
                          return ListTile(
                            onTap: () {
                              context.pop();
                              context.goNamed(
                                TeammateDetailScreen.name,
                                pathParameters: {
                                  'id': value[index].employee!.id.toString(),
                                },
                              );
                            },
                            leading: CachedImage(
                              url: value[index].employee?.image ?? '',
                              height: 42,
                              width: 42,
                            ),
                            title: Text(
                              value[index].employee?.displayName ?? '',
                              style: context.textStyles.bodyMedium,
                            ),
                            trailing: Text(formattedLeavePeriod(value[index])),
                          );
                        },
                      ),
              AsyncError(:final error) => Center(child: Text(error.toString())),
              _ => Center(child: CircularProgressIndicator.adaptive()),
            },
          ),
        ],
      ),
    );
  }

  String formattedLeavePeriod(TeammateDataModel? data) {
    if (data == null || data.startDate == null || data.endDate == null) {
      return '';
    }
    return '${leaveDateFormat(data.startDate!, data.endDate!)}${data.totalDays != 1 ? ', ${data.totalDays} Days' : ''}';
  }

  String leaveDateFormat(
    DateTime startDate,
    DateTime endDate, [
    String format = 'dd MMM yyyy',
  ]) {
    final bool isSameDate =
        startDate.year == endDate.year &&
        startDate.month == endDate.month &&
        startDate.day == endDate.day;

    final bool isSameYear = startDate.year == endDate.year;
    final bool isSameMonth = startDate.month == endDate.month && isSameYear;

    if (isSameYear) {
      if (isSameDate) {
        return DateFormat('dd MMMM').format(startDate);
      } else if (isSameMonth) {
        return '${DateFormat('dd').format(startDate)} - ${DateFormat('dd').format(endDate)} ${DateFormat('MMMM').format(startDate)}';
      } else {
        return '${DateFormat('dd MMM').format(startDate)} - ${DateFormat('dd MMM').format(endDate)}';
      }
    } else {
      if (isSameDate) {
        return DateFormat(format).format(startDate);
      } else {
        return '${DateFormat(format).format(startDate)} - ${DateFormat(format).format(endDate)}';
      }
    }
  }
}
