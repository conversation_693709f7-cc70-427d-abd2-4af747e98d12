import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/home/<USER>/widgets/all_holiday_dialog.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';
import 'package:hrms_tst/shared/widgets/loading_placeholder.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';
import 'package:intl/intl.dart';

import '../../domain/home.controller.dart';

class HolidayTile extends ConsumerWidget {
  const HolidayTile({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final holidays = ref.watch(holidaysProvider(null));
    return CardContainer(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'Upcoming Holidays',
                  style: context.textStyles.titleMedium?.copyWith(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: context.colors.secondary,
                  ),
                ),
                Spacer(),
                GestureDetector(
                  onTap: () {
                    showDialog(
                      context: context,
                      builder: (context) => AllHolidayDialog(),
                    );
                  },
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        'View all',
                        style: context.textStyles.titleMedium?.copyWith(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: context.colors.secondary,
                        ),
                      ),
                      Space.x(2),
                      SVGImage(
                        Assets.svgs.forwardArrow,
                        height: 18,
                        color: context.colors.secondary,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Space.y(18),
            switch (holidays) {
              AsyncData(:final value) => Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    value.holidays.first.name,
                    style: context.textStyles.displayLarge?.copyWith(
                      fontSize: 38,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFFFFA500),
                    ),
                  ),
                  Text(
                    DateFormat(
                      'EEE, dd MMMM yyyy',
                    ).format(value.holidays.first.startDate.toLocal()),
                    style: context.textStyles.titleMedium?.copyWith(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: context.colors.secondary,
                    ),
                  ),
                ],
              ),
              AsyncError(:final error) => Text(error.toString()),
              _ => Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  LoadingPlaceholder(
                    decoration: BoxDecoration(
                      color: context.colors.outline,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    height: 37,
                    width: 80,
                  ),
                  Space.y(2),
                  LoadingPlaceholder(
                    decoration: BoxDecoration(
                      color: context.colors.outline,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    height: 13,
                    width: 60,
                  ),
                ],
              ),
            },
          ],
        ),
      ),
    );
  }
}
