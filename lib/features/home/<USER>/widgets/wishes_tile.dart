import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/home/<USER>/models/wishes.model.dart';
import 'package:hrms_tst/features/teammates/presentation/teammate_detail.screen.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/cached_image.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';
import 'package:hrms_tst/shared/widgets/loading_placeholder.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';
import 'package:intl/intl.dart';

import '../../domain/home.controller.dart';

class WishesTile extends HookConsumerWidget {
  const WishesTile({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final wishes = ref.watch(wishesProvider);

    final selectedFilter = useState(WishesFilter.birthday);
    return CardContainer(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                WishesFilterOptions(
                  selectedFilter: selectedFilter.value,
                  onFilterSelected: (filter) {
                    selectedFilter.value = filter;
                  },
                ),
              ],
            ),
            Space.y(18),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                spacing: 8,
                children: switch (wishes) {
                  AsyncData(:final value) => () {
                    final ({
                      List<WishEmployeeTileData> list,
                      String emptyMessage,
                    })
                    data = switch (selectedFilter.value) {
                      WishesFilter.birthday => (
                        list: value.birthdayEmployees,
                        emptyMessage: 'No birthday wishes',
                      ),
                      WishesFilter.anniversary => (
                        list: value.anniversaryEmployees,
                        emptyMessage: 'No anniversary wishes',
                      ),
                      WishesFilter.newJoinee => (
                        list: value.newJoineeEmployees,
                        emptyMessage: 'No new Joinees',
                      ),
                    };
                    return data.list.isEmpty
                        ? [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6.0,
                              ).copyWith(top: 22, bottom: 45),
                              child: Text(
                                data.emptyMessage,
                                style: context.textStyles.bodySmall?.copyWith(
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ]
                        : data.list
                              .map(
                                (e) => WishEmployeeTile(
                                  filter: selectedFilter.value,
                                  employeeTileData: e,
                                ),
                              )
                              .toList();
                  }(),
                  AsyncError(:final error) => [Text(error.toString())],
                  _ => List.generate(
                    3,
                    (index) => Column(
                      children: [
                        LoadingPlaceholder(
                          decoration: BoxDecoration(
                            color: context.colors.outline,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          height: 44,
                          width: 44,
                        ),
                        Space.y(4),
                        LoadingPlaceholder(
                          decoration: BoxDecoration(
                            color: context.colors.outline,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          height: 20,
                          width: 60,
                        ),
                        Space.y(4),
                        LoadingPlaceholder(
                          decoration: BoxDecoration(
                            color: context.colors.outline,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          height: 14,
                          width: 60,
                        ),
                      ],
                    ),
                  ),
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class WishEmployeeTile extends StatelessWidget {
  const WishEmployeeTile({
    super.key,
    required this.filter,
    required this.employeeTileData,
  });

  final WishEmployeeTileData employeeTileData;
  final WishesFilter filter;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        context.goNamed(
          TeammateDetailScreen.name,
          pathParameters: {'id': employeeTileData.id.toString()},
        );
      },
      child: Column(
        children: [
          Builder(
            builder: (context) {
              final isEmployeesDay = isSameDayOfYear(
                employeeTileData.date?.toLocal(),
                DateTime.now(),
              );

              return Badge(
                label: switch (filter) {
                  WishesFilter.birthday => Container(
                    padding: EdgeInsets.all(3),
                    decoration: ShapeDecoration(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadiusGeometry.circular(6),
                      ),

                      color: Color(0xFFFFA500),
                    ),
                    child: Icon(
                      Icons.cake,
                      size: 14,
                      color: context.colors.surface,
                    ),
                  ),
                  WishesFilter.anniversary => Container(
                    padding: EdgeInsets.symmetric(horizontal: 6),
                    decoration: ShapeDecoration(
                      shape: StadiumBorder(),

                      color: context.colors.onSurface,
                    ),
                    child: Text(
                      '${DateTime.now().year - (employeeTileData.date?.year ?? DateTime.now().year)}Y',
                    ),
                  ),
                  _ => null,
                },
                backgroundColor: Colors.transparent,
                alignment: Alignment(0.5, -1),
                isLabelVisible: switch (filter) {
                  WishesFilter.birthday => isEmployeesDay,
                  _ => true,
                },
                child: Container(
                  decoration: BoxDecoration(
                    border: BoxBorder.all(
                      color: isEmployeesDay
                          ? context.colors.primary
                          : Colors.transparent,
                      width: 3,
                      strokeAlign: BorderSide.strokeAlignOutside,
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: CachedImage(
                    url: employeeTileData.image ?? '',
                    radiusValue: 16,
                    height: 44,
                    width: 44,
                  ),
                ),
              );
            },
          ),
          Space.y(4),
          SizedBox(
            width: 60,
            child: Text(
              employeeTileData.displayName,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: context.textStyles.titleMedium?.copyWith(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: context.colors.secondary,
              ),
            ),
          ),
          if (employeeTileData.date != null)
            SizedBox(
              width: 60,
              child: Text(
                DateFormat('dd MMM').format(employeeTileData.date!.toLocal()),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: context.textStyles.titleSmall?.copyWith(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: context.colors.secondary,
                ),
              ),
            ),
        ],
      ),
    );
  }

  bool isSameDayOfYear(DateTime? date1, DateTime? date2) {
    if (date1 == null || date2 == null) {
      return false;
    }
    return date1.month == date2.month && date1.day == date2.day;
  }
}

enum WishesFilter {
  birthday(label: 'Birthday'),
  anniversary(label: 'Anniversary'),
  newJoinee(label: 'New Joinee');

  final String label;
  const WishesFilter({required this.label});

  String get icon => switch (this) {
    WishesFilter.birthday => Assets.svgs.cake,
    WishesFilter.anniversary => Assets.svgs.anniversary,
    WishesFilter.newJoinee => Assets.svgs.newJoinee,
  };
}

class WishesFilterOptions extends StatelessWidget {
  const WishesFilterOptions({
    super.key,
    required this.selectedFilter,
    required this.onFilterSelected,
  });

  final WishesFilter selectedFilter;
  final Function(WishesFilter filter) onFilterSelected;

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 8,
      children: WishesFilter.values.map((e) {
        final isSelected = e == selectedFilter;
        return GestureDetector(
          onTap: () {
            onFilterSelected(e);
          },
          child: Tooltip(
            message: e.label,
            preferBelow: false,
            decoration: BoxDecoration(
              color: context.colors.surface,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: context.colors.secondary.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            textStyle: context.textStyles.labelMedium?.copyWith(
              color: context.colors.primary,
            ),
            triggerMode: TooltipTriggerMode.longPress,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  width: 0.5,
                  color: isSelected
                      ? context.colors.primary
                      : context.colors.secondary.withValues(alpha: 0.2),
                ),
                color: isSelected
                    ? context.colors.primary
                    : context.colors.surface,
              ),
              padding: EdgeInsets.all(4),
              child: SizedBox.square(
                dimension: 24,
                child: SVGImage(
                  e.icon,
                  height: 24,
                  color: isSelected
                      ? context.colors.surface
                      : context.colors.secondary,
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
