import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/extensions/list.extension.dart';
import 'package:hrms_tst/features/home/<USER>/models/holiday.model.dart';
import 'package:hrms_tst/features/home/<USER>/home.controller.dart';
import 'package:table_calendar/table_calendar.dart';

class AllHolidayDialog extends HookConsumerWidget {
  const AllHolidayDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDate = useState(DateTime.now());
    final currentMonthHolidays = useState(<HolidayTileData>[]);

    useEffect(() {
      ref.read(holidaysProvider(selectedDate.value).future).then((value) {
        currentMonthHolidays.value = value.holidays;
      });

      return null;
    }, [selectedDate.value]);

    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 20),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: SizedBox(
          height: context.screenHeight * 0.4,
          child: TableCalendar(
            focusedDay: selectedDate.value,
            firstDay: DateTime(1000),
            lastDay: DateTime(4000),
            availableCalendarFormats: {CalendarFormat.month: 'Month'},
            weekendDays: [DateTime.sunday],

            holidayPredicate: (day) {
              return currentMonthHolidays.value.any(
                (element) =>
                    isDateBetween(day, element.startDate, element.endDate),
              );
            },

            calendarStyle: CalendarStyle(
              outsideDaysVisible: false,
              todayDecoration: BoxDecoration(
                color: context.colors.primary,
                borderRadius: BorderRadius.circular(12),
              ),
              selectedDecoration: BoxDecoration(
                color: context.colors.primary,
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            shouldFillViewport: true,
            daysOfWeekHeight: 30,
            onPageChanged: (focusedDay) {
              selectedDate.value = focusedDay;
            },
            calendarBuilders: CalendarBuilders(
              holidayBuilder: (context, day, focusedDay) {
                final holiday = currentMonthHolidays.value.firstWhereOrNull(
                  (element) =>
                      isDateBetween(day, element.startDate, element.endDate),
                );
                return Tooltip(
                  message: holiday?.name ?? '',
                  triggerMode: TooltipTriggerMode.tap,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: context.colors.primaryContainer,
                  ),
                  child: Center(
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 400),
                      curve: Curves.ease,
                      height: 38,
                      width: 38,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: context.colors.primary.withValues(alpha: 0.2),
                        shape: BoxShape.circle,
                        border: Border.all(color: context.colors.primary),
                      ),
                      child: Text(day.day.toString()),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  bool isDateBetween(DateTime oprand, DateTime startDate, DateTime endDate) {
    DateTime onlyDate = DateTime(oprand.year, oprand.month, oprand.day);
    DateTime onlyStart = DateTime(
      startDate.year,
      startDate.month,
      startDate.day,
    );
    DateTime onlyEnd = DateTime(endDate.year, endDate.month, endDate.day);

    return onlyDate.isAtSameMomentAs(onlyStart) ||
        onlyDate.isAtSameMomentAs(onlyEnd) ||
        (onlyDate.isAfter(onlyStart) && onlyDate.isBefore(onlyEnd));
  }
}
