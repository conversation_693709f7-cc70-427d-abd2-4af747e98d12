import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:hrms_tst/features/home/<USER>/models/clock_in.model.dart';
import 'package:hrms_tst/features/home/<USER>/models/employee_presence.model.dart';
import 'package:hrms_tst/features/home/<USER>/models/holiday.model.dart';
import 'package:hrms_tst/features/home/<USER>/models/leave_balance.model.dart';
import 'package:hrms_tst/features/home/<USER>/models/wishes.model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../data/home.protocol.dart';

part 'home.controller.g.dart';

@riverpod
FutureOr<List<ProjectModel>> projects(Ref ref) async {
  return (await getIt.get<HomeProtocol>().getProjects()).ignoreFailure();
}

@Riverpod(keepAlive: true)
FutureOr<LeaveBalanceModel> leaveBalance(Ref ref) async {
  return (await getIt.get<HomeProtocol>().getLeaveBalance(
    currentTime: DateTime.now(),
  )).ignoreFailure();
}

@riverpod
FutureOr<EmployeePresenceModel> employeePresence(Ref ref) async {
  return (await getIt.get<HomeProtocol>().getEmployeePresence())
      .ignoreFailure();
}

@riverpod
FutureOr<WishesModel> wishes(Ref ref) async {
  return (await getIt.get<HomeProtocol>().getWishes()).ignoreFailure();
}

@riverpod
FutureOr<HolidayModel> holidays(Ref ref, DateTime? date) async {
  return (await getIt.get<HomeProtocol>().getHolidays(
    date: date,
  )).ignoreFailure();
}

@riverpod
FutureOr<List<TeammateDataModel>> teammatePresence(
  Ref ref,
  String type,
  bool isUpcoming,
) async {
  return (await getIt.get<HomeProtocol>().getAllPresences(
    type: type,
    isUpcoming: isUpcoming,
  )).ignoreFailure();
}
