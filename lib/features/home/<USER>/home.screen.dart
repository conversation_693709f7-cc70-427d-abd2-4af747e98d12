import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/features/bottom_navigation/widgets/main_app_bar.dart';
import 'package:hrms_tst/features/home/<USER>/clock_in_out.controller.dart';
import 'package:hrms_tst/features/home/<USER>/home.controller.dart';
import 'package:hrms_tst/features/home/<USER>/widgets/holiday_tile.dart';
import 'package:hrms_tst/features/home/<USER>/widgets/leave_balance_tile.dart';
import 'package:hrms_tst/features/home/<USER>/widgets/presence_tile.dart';
import 'package:hrms_tst/features/home/<USER>/widgets/wishes_tile.dart';

import 'widgets/clock_in_tile.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  static const String name = 'Home';
  static const String path = '/home';

  static GoRoute route({List<RouteBase> routes = const []}) => GoRoute(
    path: path,
    name: name,
    routes: routes,
    pageBuilder: (context, state) =>
        const NoTransitionPage(child: HomeScreen()),
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: MainAppBar(),
      body: SafeArea(
        child: RefreshIndicator.adaptive(
          onRefresh: () async {
            ref.invalidate(clockInOutProvider);
            ref.invalidate(leaveBalanceProvider);
            ref.invalidate(employeePresenceProvider);
            ref.invalidate(wishesProvider);
            ref.invalidate(holidaysProvider);

            await ref.read(clockInOutProvider.future);
            await ref.read(leaveBalanceProvider.future);
            await ref.read(employeePresenceProvider.future);
            await ref.read(wishesProvider.future);
            await ref.read(holidaysProvider(null).future);
          },
          child: CustomScrollView(
            slivers: [
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 4,
                    horizontal: 16,
                  ),
                  child: ClockInTile(),
                ),
              ),
              SliverPadding(padding: EdgeInsetsGeometry.all(3)),
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 4,
                    horizontal: 16,
                  ),
                  child: LeaveBalanceTile(),
                ),
              ),
              SliverPadding(padding: EdgeInsetsGeometry.all(3)),
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 4,
                    horizontal: 16,
                  ),
                  child: PresenceTile(),
                ),
              ),
              SliverPadding(padding: EdgeInsetsGeometry.all(3)),
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 4,
                    horizontal: 16,
                  ),
                  child: WishesTile(),
                ),
              ),
              SliverPadding(padding: EdgeInsetsGeometry.all(3)),
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 4,
                    horizontal: 16,
                  ),
                  child: HolidayTile(),
                ),
              ),
              SliverPadding(padding: EdgeInsetsGeometry.all(6)),
            ],
          ),
        ),
      ),
    );
  }
}
