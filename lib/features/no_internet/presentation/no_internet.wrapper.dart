import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/features/no_internet/presentation/connectivity.provider.dart';

class NoInternetWrapper extends ConsumerWidget {
  const NoInternetWrapper({super.key, required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectivity = ref.watch(connectivityProvider);

    return switch (connectivity) {
      AsyncData(:final value) => Stack(
        children: [
          child,
          if (value == ConnectivityState.offline)
            Scaffold(body: Center(child: Text('No Internet Connection'))),
        ],
      ),
      AsyncError(:final error) => Center(child: Text(error.toString())),
      _ => const Center(child: CircularProgressIndicator.adaptive()),
    };
  }
}
