import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'connectivity.provider.g.dart';

enum ConnectivityState { online, offline }

@riverpod
Stream<ConnectivityState> connectivity(Ref ref) {
  return Connectivity().onConnectivityChanged.map((event) {
    if (event.contains(ConnectivityResult.mobile) ||
        event.contains(ConnectivityResult.wifi) ||
        event.contains(ConnectivityResult.ethernet) ||
        event.contains(ConnectivityResult.vpn)) {
      return ConnectivityState.online;
    } else if (event.contains(ConnectivityResult.none)) {
      return ConnectivityState.offline;
    }
    return ConnectivityState.offline;
  });
}
