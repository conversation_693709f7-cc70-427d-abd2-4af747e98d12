<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>690147380218-5mjlkegts4biae24327sfg6jo1kgsos6.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.690147380218-5mjlkegts4biae24327sfg6jo1kgsos6</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>690147380218-527qbvdss6s7t5ek2v28lk4a8jrcfmu7.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyBXgMhZ8JLUk97xdh5oj1UrJUFcTDWQHxM</string>
	<key>GCM_SENDER_ID</key>
	<string>690147380218</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.tsttechnology.hrms</string>
	<key>PROJECT_ID</key>
	<string>tst-hrms-c8f00</string>
	<key>STORAGE_BUCKET</key>
	<string>tst-hrms-c8f00.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:690147380218:ios:ed90d79c6582a45e427243</string>
</dict>
</plist>