name: hrms_tst
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  get_it: ^8.0.3
  dio: ^5.8.0+1
  go_router: ^16.0.0
  device_info_plus: ^11.5.0
  shared_preferences: ^2.5.3
  permission_handler: ^12.0.0+1
  image_picker: ^1.1.2
  file_picker: ^9.2.3
  firebase_core: ^3.14.0
  firebase_auth: ^5.6.0
  firebase_messaging: ^15.2.7
  flutter_local_notifications: ^19.2.1
  hooks_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1
  flutter_native_splash: ^2.4.6
  flutter_hooks: ^0.21.2
  google_sign_in: ^6.3.0
  flutter_svg: ^2.2.0
  google_fonts: ^6.2.1
  intl: ^0.20.2
  cached_network_image: ^3.4.1
  talker_dio_logger: ^4.9.0
  freezed_annotation: ^3.0.0
  json_annotation: ^4.9.0
  flutter_html: ^3.0.0
  url_launcher: ^6.3.2
  dropdown_button2: ^2.3.9
  geolocator: ^14.0.2
  progressive_time_picker: ^1.0.3
  table_calendar: ^3.2.0
  flutter_typeahead: ^5.2.0
  native_geofence: ^1.0.9
  connectivity_plus: ^6.1.4
  flutter_quill_delta_from_html: ^1.5.2
  vsc_quill_delta_to_html: ^1.0.5
  flutter_quill: ^11.4.1
  tflite_flutter: ^0.11.0
  face_camera: ^0.1.4
  image: ^4.5.4

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  build_runner: ^2.5.3
  riverpod_generator: ^2.6.5
  custom_lint: ^0.7.5
  riverpod_lint: ^2.6.5
  package_rename: ^1.10.0
  flutter_launcher_icons: ^0.14.4
  flutter_gen_runner: ^5.10.0
  freezed: ^3.0.6
  json_serializable: ^6.9.5
  

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/svgs/
    - assets/ml-model/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

flutter_gen:
  # output: lib/gen/ # Optional (default: lib/gen/)
  # line_length: 80 # Optional (default: 80)

  # # Optional
  # integrations:
  #   flutter_svg: true
  #   flare_flutter: true
  #   rive: true
  #   lottie: true